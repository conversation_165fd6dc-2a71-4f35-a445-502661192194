/// نموذج نوع المهمة
class TaskType {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final String? icon;
  final bool isDefault;
  final int createdAt;

  const TaskType({
    required this.id,
    required this.name,
    this.description,
    this.color,
    this.icon,
    this.isDefault = false,
    required this.createdAt,
  });

  factory TaskType.fromJson(Map<String, dynamic> json) {
    return TaskType(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
      'createdAt': createdAt,
    };
  }

  TaskType copyWith({
    int? id,
    String? name,
    String? description,
    String? color,
    String? icon,
    bool? isDefault,
    int? createdAt,
  }) {
    return TaskType(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'TaskType(id: $id, name: $name, isDefault: $isDefault)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskType && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء نوع مهمة
class CreateTaskTypeRequest {
  final String name;
  final String? description;
  final String? color;
  final String? icon;
  final bool isDefault;

  const CreateTaskTypeRequest({
    required this.name,
    this.description,
    this.color,
    this.icon,
    this.isDefault = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
    };
  }
}

/// نموذج طلب تحديث نوع مهمة
class UpdateTaskTypeRequest {
  final String? name;
  final String? description;
  final String? color;
  final String? icon;
  final bool? isDefault;
  final bool? isActive;

  const UpdateTaskTypeRequest({
    this.name,
    this.description,
    this.color,
    this.icon,
    this.isDefault,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (color != null) data['color'] = color;
    if (icon != null) data['icon'] = icon;
    if (isDefault != null) data['isDefault'] = isDefault;
    if (isActive != null) data['isActive'] = isActive;
    return data;
  }
}
// لانريد اي بيانات افتراضيه
/// أنواع المهام الافتراضية
// class DefaultTaskTypes {
//   static const List<Map<String, dynamic>> types = [
//     {
//       'name': 'تطوير',
//       'description': 'مهام التطوير والبرمجة',
//       'color': '#2196F3',
//       'icon': 'code',
//     },
//     {
//       'name': 'تصميم',
//       'description': 'مهام التصميم والجرافيك',
//       'color': '#E91E63',
//       'icon': 'design_services',
//     },
//     {
//       'name': 'اختبار',
//       'description': 'مهام الاختبار وضمان الجودة',
//       'color': '#FF9800',
//       'icon': 'bug_report',
//     },
//     {
//       'name': 'توثيق',
//       'description': 'مهام التوثيق والكتابة',
//       'color': '#4CAF50',
//       'icon': 'description',
//     },
//     {
//       'name': 'اجتماع',
//       'description': 'الاجتماعات والمناقشات',
//       'color': '#9C27B0',
//       'icon': 'meeting_room',
//     },
//     {
//       'name': 'بحث',
//       'description': 'مهام البحث والتحليل',
//       'color': '#607D8B',
//       'icon': 'search',
//     },
//     {
//       'name': 'صيانة',
//       'description': 'مهام الصيانة والإصلاح',
//       'color': '#795548',
//       'icon': 'build',
//     },
//     {
//       'name': 'مراجعة',
//       'description': 'مراجعة الكود والمحتوى',
//       'color': '#FF5722',
//       'icon': 'rate_review',
//     },
//   ];

//   /// الحصول على نوع المهمة الافتراضي
//   static Map<String, dynamic> getDefaultType(String name) {
//     return types.firstWhere(
//       (type) => type['name'] == name,
//       orElse: () => types.first,
//     );
//   }

//   /// الحصول على جميع أسماء الأنواع الافتراضية
//   static List<String> get typeNames => 
//       types.map((type) => type['name'] as String).toList();
// }
