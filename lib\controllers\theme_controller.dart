import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/storage_service.dart';

/// متحكم السمة
class ThemeController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();

  // حالة السمة
  final RxBool _isDarkMode = false.obs;
  final RxBool _isSystemTheme = true.obs;

  // Getters
  bool get isDarkMode => _isDarkMode.value;
  bool get isSystemTheme => _isSystemTheme.value;
  
  // للتوافق مع الكود الموجود
  RxBool get isDarkMode => _isDarkMode;
  RxBool get isSystemTheme => _isSystemTheme;

  @override
  void onInit() {
    super.onInit();
    _loadThemeSettings();
  }

  /// تحميل إعدادات السمة من التخزين المحلي
  Future<void> _loadThemeSettings() async {
    try {
      final isDark = await _storageService.getBool('is_dark_mode') ?? false;
      final isSystem = await _storageService.getBool('is_system_theme') ?? true;
      
      _isDarkMode.value = isDark;
      _isSystemTheme.value = isSystem;
      
      debugPrint('تم تحميل إعدادات السمة: isDark=$isDark, isSystem=$isSystem');
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات السمة: $e');
    }
  }

  /// تغيير السمة إلى الوضع الداكن/الفاتح
  Future<void> toggleDarkMode() async {
    try {
      _isDarkMode.value = !_isDarkMode.value;
      _isSystemTheme.value = false; // إيقاف السمة التلقائية عند التغيير اليدوي
      
      await _storageService.setBool('is_dark_mode', _isDarkMode.value);
      await _storageService.setBool('is_system_theme', _isSystemTheme.value);
      
      // تطبيق السمة الجديدة
      Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
      
      debugPrint('تم تغيير السمة إلى: ${_isDarkMode.value ? 'داكن' : 'فاتح'}');
    } catch (e) {
      debugPrint('خطأ في تغيير السمة: $e');
    }
  }

  /// تفعيل/إيقاف السمة التلقائية (حسب النظام)
  Future<void> toggleSystemTheme() async {
    try {
      _isSystemTheme.value = !_isSystemTheme.value;
      
      await _storageService.setBool('is_system_theme', _isSystemTheme.value);
      
      if (_isSystemTheme.value) {
        // استخدام سمة النظام
        Get.changeThemeMode(ThemeMode.system);
        debugPrint('تم تفعيل السمة التلقائية');
      } else {
        // استخدام السمة المحددة يدوياً
        Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
        debugPrint('تم إيقاف السمة التلقائية');
      }
    } catch (e) {
      debugPrint('خطأ في تغيير إعداد السمة التلقائية: $e');
    }
  }

  /// تعيين السمة الداكنة
  Future<void> setDarkMode(bool isDark) async {
    try {
      _isDarkMode.value = isDark;
      _isSystemTheme.value = false;
      
      await _storageService.setBool('is_dark_mode', isDark);
      await _storageService.setBool('is_system_theme', false);
      
      Get.changeThemeMode(isDark ? ThemeMode.dark : ThemeMode.light);
      
      debugPrint('تم تعيين السمة إلى: ${isDark ? 'داكن' : 'فاتح'}');
    } catch (e) {
      debugPrint('خطأ في تعيين السمة: $e');
    }
  }

  /// تعيين السمة التلقائية
  Future<void> setSystemTheme(bool useSystem) async {
    try {
      _isSystemTheme.value = useSystem;
      
      await _storageService.setBool('is_system_theme', useSystem);
      
      if (useSystem) {
        Get.changeThemeMode(ThemeMode.system);
      } else {
        Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
      }
      
      debugPrint('تم تعيين السمة التلقائية إلى: $useSystem');
    } catch (e) {
      debugPrint('خطأ في تعيين السمة التلقائية: $e');
    }
  }

  /// الحصول على وضع السمة الحالي
  ThemeMode get currentThemeMode {
    if (_isSystemTheme.value) {
      return ThemeMode.system;
    }
    return _isDarkMode.value ? ThemeMode.dark : ThemeMode.light;
  }

  /// التحقق من كون السمة الحالية داكنة
  bool get isCurrentlyDark {
    if (_isSystemTheme.value) {
      // التحقق من سمة النظام
      final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }
    return _isDarkMode.value;
  }

  /// إعادة تعيين إعدادات السمة إلى القيم الافتراضية
  Future<void> resetToDefaults() async {
    try {
      _isDarkMode.value = false;
      _isSystemTheme.value = true;
      
      await _storageService.setBool('is_dark_mode', false);
      await _storageService.setBool('is_system_theme', true);
      
      Get.changeThemeMode(ThemeMode.system);
      
      debugPrint('تم إعادة تعيين إعدادات السمة إلى القيم الافتراضية');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين إعدادات السمة: $e');
    }
  }
}
