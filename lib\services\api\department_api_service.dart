import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../models/department_model.dart';
import 'api_service.dart';

/// خدمة API للأقسام
class DepartmentApiService {
  final ApiService _apiService = Get.find<ApiService>();

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final response = await _apiService.get('/departments');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final response = await _apiService.get('/departments/$id');
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل القسم $id: $e');
      return null;
    }
  }

  /// الحصول على الأقسام النشطة فقط
  Future<List<Department>> getActiveDepartments() async {
    try {
      final response = await _apiService.get('/departments/active');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام النشطة: $e');
      return [];
    }
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String query) async {
    try {
      final response = await _apiService.get('/departments/search?q=$query');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن الأقسام: $e');
      return [];
    }
  }

  /// إنشاء قسم جديد
  Future<Department?> createDepartment(CreateDepartmentRequest request) async {
    try {
      final response = await _apiService.post(
        '/departments',
        body: request.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<Department?> updateDepartment(
    int id,
    UpdateDepartmentRequest request,
  ) async {
    try {
      final response = await _apiService.put(
        '/departments/$id',
        body: request.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث القسم $id: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    try {
      final response = await _apiService.delete('/departments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف القسم $id: $e');
      return false;
    }
  }

  /// تفعيل قسم
  Future<bool> activateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/departments/$id/activate',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل القسم $id: $e');
      return false;
    }
  }

  /// إلغاء تفعيل قسم
  Future<bool> deactivateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/departments/$id/deactivate',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل القسم $id: $e');
      return false;
    }
  }

  /// تعيين مدير قسم
  Future<bool> assignManager(int departmentId, int managerId) async {
    try {
      final response = await _apiService.put(
        '/departments/$departmentId/manager',
        body: {'managerId': managerId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تعيين مدير القسم $departmentId: $e');
      return false;
    }
  }

  /// إزالة مدير قسم
  Future<bool> removeManager(int departmentId) async {
    try {
      final response = await _apiService.delete('/departments/$departmentId/manager');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة مدير القسم $departmentId: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الأقسام
  Future<List<DepartmentStats>> getDepartmentStats() async {
    try {
      final response = await _apiService.get('/departments/stats');
      return _apiService.handleListResponse<DepartmentStats>(
        response,
        (json) => DepartmentStats.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات الأقسام: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات قسم معين
  Future<DepartmentStats?> getDepartmentStatsById(int id) async {
    try {
      final response = await _apiService.get('/departments/$id/stats');
      return _apiService.handleResponse<DepartmentStats>(
        response,
        (json) => DepartmentStats.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات القسم $id: $e');
      return null;
    }
  }

  /// الحصول على موظفي قسم معين
  Future<List<Map<String, dynamic>>> getDepartmentEmployees(int departmentId) async {
    try {
      final response = await _apiService.get('/departments/$departmentId/employees');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = _apiService.parseResponse(response);
        return (data as List).cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل موظفي القسم $departmentId: $e');
      return [];
    }
  }

  /// الحصول على مهام قسم معين
  Future<List<Map<String, dynamic>>> getDepartmentTasks(int departmentId) async {
    try {
      final response = await _apiService.get('/departments/$departmentId/tasks');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = _apiService.parseResponse(response);
        return (data as List).cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل مهام القسم $departmentId: $e');
      return [];
    }
  }

  /// نقل موظف إلى قسم آخر
  Future<bool> transferEmployee(int userId, int newDepartmentId) async {
    try {
      final response = await _apiService.put(
        '/departments/transfer-employee',
        body: {
          'userId': userId,
          'newDepartmentId': newDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل الموظف $userId إلى القسم $newDepartmentId: $e');
      return false;
    }
  }

  /// الحصول على تقرير أداء الأقسام
  Future<Map<String, dynamic>?> getDepartmentPerformanceReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var url = '/departments/performance-report';
      final params = <String, String>{};
      
      if (startDate != null) {
        params['startDate'] = (startDate.millisecondsSinceEpoch ~/ 1000).toString();
      }
      if (endDate != null) {
        params['endDate'] = (endDate.millisecondsSinceEpoch ~/ 1000).toString();
      }

      if (params.isNotEmpty) {
        url += '?${params.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }

      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return _apiService.parseResponse(response) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل تقرير أداء الأقسام: $e');
      return null;
    }
  }
}
