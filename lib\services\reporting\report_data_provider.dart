import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';


import '../../models/task_model.dart';


/// مزود بيانات التقارير
///
/// يوفر وظائف للحصول على بيانات التقارير من مصادر مختلفة
class ReportDataProvider extends GetxService {
  final TaskRepository _taskRepository = TaskRepository();
  final UserRepository _userRepository = UserRepository();

  /// تحويل حالة المهمة إلى نص
  String taskStatusToString(TaskStatus status) {
    return status.displayNameAr;
  }

  /// تنفيذ تقرير
  Future<ReportResult> executeReport({
    required EnhancedReport report,
    required DateTimeRange dateRange,
  }) async {
    try {
      // تنفيذ التقرير حسب النوع
      switch (report.type) {
        case ReportType.taskStatus:
          return await _executeTaskStatusReport(report, dateRange);
        case ReportType.userPerformance:
          return await _executeUserPerformanceReport(report, dateRange);
        case ReportType.departmentPerformance:
          return await _executeDepartmentPerformanceReport(report, dateRange);
        case ReportType.timeTracking:
          return await _executeTimeTrackingReport(report, dateRange);
        case ReportType.taskProgress:
          return await _executeTaskProgressReport(report, dateRange);
        case ReportType.taskDetails:
          return await _executeTaskDetailsReport(report, dateRange);
        case ReportType.taskCompletion:
          return await _executeTaskCompletionReport(report, dateRange);
        case ReportType.userActivity:
          return await _executeUserActivityReport(report, dateRange);
        case ReportType.departmentWorkload:
          return await _executeDepartmentWorkloadReport(report, dateRange);
        case ReportType.projectStatus:
          return await _executeProjectStatusReport(report, dateRange);
        case ReportType.custom:
          return await _executeCustomReport(report, dateRange);
        default:
          return ReportResult(
            reportId: report.id,
            isSuccess: false,
            errorMessages: ['نوع التقرير غير مدعوم'],
            executedAt: DateTime.now(),
          );
      }
    } catch (e) {
      debugPrint('خطأ في تنفيذ التقرير: $e');
      return ReportResult(
        reportId: report.id,
        isSuccess: false,
        errorMessages: ['حدث خطأ أثناء تنفيذ التقرير: ${e.toString()}'],
        executedAt: DateTime.now(),
      );
    }
  }

  /// تنفيذ تقرير حالة المهام
  Future<ReportResult> _executeTaskStatusReport(EnhancedReport report, DateTimeRange dateRange) async {
    // الحصول على المهام
    final tasks = await _taskRepository.getAllTasks();

    // تطبيق الفلاتر
    final filteredTasks = _applyFilters(tasks, report.filters, dateRange);

    // تجميع البيانات حسب الحالة
    final Map<TaskStatus, int> tasksByStatus = {};
    for (var status in TaskStatus.values) {
      tasksByStatus[status] = 0;
    }

    for (var task in filteredTasks) {
      tasksByStatus[task.status] = (tasksByStatus[task.status] ?? 0) + 1;
    }

    // إنشاء بيانات التقرير
    final List<Map<String, dynamic>> data = [];
    for (var entry in tasksByStatus.entries) {
      data.add({
        'status': taskStatusToString(entry.key),
        'count': entry.value,
        'percentage': filteredTasks.isNotEmpty ? (entry.value / filteredTasks.length * 100).toStringAsFixed(1) : '0.0',
      });
    }

    // إنشاء ملخص التقرير
    final Map<String, dynamic> summary = {
      'totalTasks': filteredTasks.length,
      'completedTasks': tasksByStatus[TaskStatus.completed] ?? 0,
      'inProgressTasks': tasksByStatus[TaskStatus.inProgress] ?? 0,
      'pendingTasks': tasksByStatus[TaskStatus.pending] ?? 0,
      'cancelledTasks': tasksByStatus[TaskStatus.cancelled] ?? 0,
      'completionRate': filteredTasks.isNotEmpty
          ? ((tasksByStatus[TaskStatus.completed] ?? 0) / filteredTasks.length * 100).toStringAsFixed(1)
          : '0.0',
      'period': {
        'start': DateFormat('yyyy-MM-dd').format(dateRange.start),
        'end': DateFormat('yyyy-MM-dd').format(dateRange.end),
      },
    };

    // إنشاء بيانات التصور المرئي
    final Map<String, List<Map<String, dynamic>>> visualizationData = {};

    // بيانات المخطط الدائري
    visualizationData['pieChart'] = data;

    // بيانات المخطط الشريطي
    visualizationData['barChart'] = data;

    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: data,
      summary: summary,
      totalRecords: data.length,
      executedAt: DateTime.now(),
      visualizationData: visualizationData,
    );
  }

  /// تنفيذ تقرير أداء المستخدم
  Future<ReportResult> _executeUserPerformanceReport(EnhancedReport report, DateTimeRange dateRange) async {
    // الحصول على المهام والمستخدمين
    final tasks = await _taskRepository.getAllTasks();
    final users = await _userRepository.getAllUsers();

    // تطبيق الفلاتر
    final filteredTasks = _applyFilters(tasks, report.filters, dateRange);

    // تجميع البيانات حسب المستخدم
    final Map<String, Map<String, dynamic>> userPerformance = {};

    for (var user in users) {
      userPerformance[user.id] = {
        'userId': user.id,
        'userName': user.name,
        'totalTasks': 0,
        'completedTasks': 0,
        'inProgressTasks': 0,
        'pendingTasks': 0,
        'cancelledTasks': 0,
        'completionRate': '0.0',
        'averageCompletionTime': 0,
      };
    }

    for (var task in filteredTasks) {
      if (task.assigneeId != null && userPerformance.containsKey(task.assigneeId)) {
        final userData = userPerformance[task.assigneeId]!;

        // زيادة عدد المهام الكلي
        userData['totalTasks'] = (userData['totalTasks'] as int) + 1;

        // تصنيف حسب الحالة
        switch (task.status) {
          case TaskStatus.completed:
            userData['completedTasks'] = (userData['completedTasks'] as int) + 1;

            // حساب وقت الإكمال إذا كان متاحًا
            if (task.completedAt != null) {
              final completionTime = task.completedAt!.difference(task.createdAt).inHours;
              final currentTotal = userData['averageCompletionTime'] as int;
              final currentCompleted = userData['completedTasks'] as int;

              // تحديث متوسط وقت الإكمال
              userData['averageCompletionTime'] = (currentTotal * (currentCompleted - 1) + completionTime) / currentCompleted;
            }
            break;
          case TaskStatus.inProgress:
            userData['inProgressTasks'] = (userData['inProgressTasks'] as int) + 1;
            break;
          case TaskStatus.pending:
            userData['pendingTasks'] = (userData['pendingTasks'] as int) + 1;
            break;
          case TaskStatus.cancelled:
            userData['cancelledTasks'] = (userData['cancelledTasks'] as int) + 1;
            break;
          case TaskStatus.waitingForInfo:
            userData['pendingTasks'] = (userData['pendingTasks'] as int) + 1; // Counting as pending
            break;
          case TaskStatus.news:
            userData['pendingTasks'] = (userData['pendingTasks'] as int) + 1; // Counting as pending
            break;
        }

        // حساب معدل الإكمال
        final totalTasks = userData['totalTasks'] as int;
        final completedTasks = userData['completedTasks'] as int;

        if (totalTasks > 0) {
          userData['completionRate'] = (completedTasks / totalTasks * 100).toStringAsFixed(1);
        }
      }
    }

    // تحويل البيانات إلى قائمة
    final List<Map<String, dynamic>> data = userPerformance.values.toList();

    // ترتيب البيانات حسب معدل الإكمال (تنازليًا)
    data.sort((a, b) => double.parse(b['completionRate']).compareTo(double.parse(a['completionRate'])));

    // إنشاء ملخص التقرير
    final Map<String, dynamic> summary = {
      'totalUsers': users.length,
      'activeUsers': users.where((u) => u.isActive).length,
      'usersWithTasks': data.where((u) => (u['totalTasks'] as int) > 0).length,
      'topPerformer': data.isNotEmpty ? data.first['userName'] : null,
      'topPerformerRate': data.isNotEmpty ? data.first['completionRate'] : '0.0',
      'period': {
        'start': DateFormat('yyyy-MM-dd').format(dateRange.start),
        'end': DateFormat('yyyy-MM-dd').format(dateRange.end),
      },
    };

    // إنشاء بيانات التصور المرئي
    final Map<String, List<Map<String, dynamic>>> visualizationData = {};

    // بيانات المخطط الشريطي
    visualizationData['barChart'] = data.take(10).map((user) => {
      'name': user['userName'],
      'completionRate': double.parse(user['completionRate']),
      'totalTasks': user['totalTasks'],
    }).toList();

    // بيانات مخطط الخط
    visualizationData['lineChart'] = data.take(10).map((user) => {
      'name': user['userName'],
      'completedTasks': user['completedTasks'],
      'inProgressTasks': user['inProgressTasks'],
      'pendingTasks': user['pendingTasks'],
    }).toList();

    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: data,
      summary: summary,
      totalRecords: data.length,
      executedAt: DateTime.now(),
      visualizationData: visualizationData,
    );
  }

  /// تطبيق الفلاتر على المهام
  List<Task> _applyFilters(List<Task> tasks, List<ReportFilter> filters, DateTimeRange dateRange) {
    // تطبيق فلتر التاريخ أولاً
    var filteredTasks = tasks.where((task) {
      final taskDate = task.createdAt;
      return taskDate.isAfter(dateRange.start) && taskDate.isBefore(dateRange.end);
    }).toList();

    // تطبيق الفلاتر المخصصة
    for (var filter in filters.where((f) => f.isActive)) {
      filteredTasks = filteredTasks.where((task) {
        return _evaluateFilter(task, filter);
      }).toList();
    }

    return filteredTasks;
  }

  /// تقييم فلتر على مهمة
  bool _evaluateFilter(Task task, ReportFilter filter) {
    // الحصول على قيمة الحقل
    dynamic fieldValue;

    switch (filter.field) {
      case 'title':
        fieldValue = task.title;
        break;
      case 'description':
        fieldValue = task.description;
        break;
      case 'status':
        fieldValue = task.status;
        break;
      case 'priority':
        fieldValue = task.priority;
        break;
      case 'assigneeId':
        fieldValue = task.assigneeId;
        break;
      case 'departmentId':
        fieldValue = task.departmentId;
        break;
      case 'createdAt':
        fieldValue = task.createdAt;
        break;
      case 'dueDate':
        fieldValue = task.dueDate;
        break;
      case 'completedAt':
        fieldValue = task.completedAt;
        break;
      case 'completionPercentage':
        fieldValue = task.completionPercentage;
        break;
      default:
        return true; // إذا كان الحقل غير معروف، لا تطبق الفلتر
    }

    // تقييم العملية
    // Handle 'isIn' and 'notIn' cases separately
    if (filter.operator == FilterOperator.isIn) {
      return fieldValue != null && filter.values != null && filter.values!.contains(fieldValue);
    } else if (filter.operator == FilterOperator.notIn) {
      return fieldValue == null || filter.values == null || !filter.values!.contains(fieldValue);
    }

    switch (filter.operator) {
      case FilterOperator.equals:
        return fieldValue == filter.value;
      case FilterOperator.notEquals:
        return fieldValue != filter.value;
      case FilterOperator.contains:
        return fieldValue != null && fieldValue.toString().contains(filter.value.toString());
      case FilterOperator.notContains:
        return fieldValue == null || !fieldValue.toString().contains(filter.value.toString());
      case FilterOperator.startsWith:
        return fieldValue != null && fieldValue.toString().startsWith(filter.value.toString());
      case FilterOperator.endsWith:
        return fieldValue != null && fieldValue.toString().endsWith(filter.value.toString());
      case FilterOperator.greaterThan:
        return fieldValue != null && fieldValue > filter.value;
      case FilterOperator.lessThan:
        return fieldValue != null && fieldValue < filter.value;
      case FilterOperator.greaterOrEqual:
        return fieldValue != null && fieldValue >= filter.value;
      case FilterOperator.lessOrEqual:
        return fieldValue != null && fieldValue <= filter.value;
      case FilterOperator.between:
        return fieldValue != null && fieldValue >= filter.value && fieldValue <= filter.value2;
      case FilterOperator.isNull:
        return fieldValue == null;
      case FilterOperator.isNotNull:
        return fieldValue != null;
      case FilterOperator.before:
        return fieldValue != null && fieldValue.isBefore(filter.value);
      case FilterOperator.after:
        return fieldValue != null && fieldValue.isAfter(filter.value);
      case FilterOperator.onOrBefore:
        return fieldValue != null && (fieldValue.isBefore(filter.value) || fieldValue == filter.value);
      case FilterOperator.onOrAfter:
        return fieldValue != null && (fieldValue.isAfter(filter.value) || fieldValue == filter.value);
      case FilterOperator.inRange:
        return fieldValue != null && fieldValue.isAfter(filter.value) && fieldValue.isBefore(filter.value2);
      default:
        return true;
    }
  }

  // تنفيذ باقي أنواع التقارير
  Future<ReportResult> _executeDepartmentPerformanceReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeTimeTrackingReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeTaskProgressReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeTaskDetailsReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeTaskCompletionReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeUserActivityReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeDepartmentWorkloadReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeProjectStatusReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }

  Future<ReportResult> _executeCustomReport(EnhancedReport report, DateTimeRange dateRange) async {
    // سيتم تنفيذ هذا التقرير لاحقًا
    return ReportResult(
      reportId: report.id,
      isSuccess: true,
      data: [],
      summary: {},
      executedAt: DateTime.now(),
    );
  }
}
