import 'dart:io'
    if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../models/comment_model.dart';
import '../../models/task_history_model.dart';
import '../../models/task_message_model.dart';
import '../../models/message_attachment_model.dart';
import '../../models/user_model.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/task_type_controller.dart';
import '../../utils/date_formatter.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/user_selection_dialog.dart';
import '../../widgets/draggable_divider.dart';
import '../../widgets/task_transfer_button.dart';
import '../../widgets/task_access_manager.dart';
import '../../extensions/task_extensions.dart';
import 'inline_edit_task_screen.dart';
import 'subtasks_tab.dart';
import 'task_progress_tab.dart';
import 'task_attachments_tab.dart';
import 'task_transfer_history_tab.dart';
import 'task_overview_tab.dart';
import 'contributor_contributions_tab.dart';
import 'task_documents_tab.dart';

class TaskDetailScreen extends StatefulWidget {
  final String taskId;

  const TaskDetailScreen({
    super.key,
    required this.taskId,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _progressController = TextEditingController();
  final ScrollController _messageScrollController = ScrollController();
  bool _isLoading = false;
  bool _showSidebar = true;

  // متغير لتخزين الرسالة التي يتم الرد عليها
  TaskMessage? _replyToMessage;

  // متغير لتخزين معرف المستخدم المحدد لعرض مساهماته
  String? _selectedContributorId;

  // متغيرات للتحكم في حجم الأقسام
  double _leftPanelFlex = 3;
  double _rightPanelFlex = 2;
  final double _minPanelFlex = 1;
  final double _maxPanelFlex = 5;

  // متغير لتخزين مراقب التحديثات
  Worker? _messagesUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: 12, vsync: this); // زيادة عدد التبويبات إلى 12
    _tabController.addListener(_handleTabChange);
    // تسجيل متحكم التبويب مع وسم لاستخدامه في التبويبات الفرعية
    Get.put<TabController>(_tabController, tag: 'task_detail_tabs');
    _loadTaskDetails();

    // الاشتراك في إشعارات تحديث الرسائل
    final taskController = Get.find<TaskController>();
    _messagesUpdateSubscription = taskController.subscribeToMessagesUpdates(() {
      // تحديث الرسائل عند استلام إشعار
      _refreshMessages();
    });

    // مراقبة قائمة الرسائل للتمرير التلقائي عند إضافة رسائل جديدة
    taskController.taskMessages.listen((_) {
      // التمرير إلى أسفل بعد إضافة رسائل جديدة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_messageScrollController.hasClients) {
          _messageScrollController.animateTo(
            _messageScrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    });
  }

  /// تحديث الرسائل
  Future<void> _refreshMessages() async {
    final taskController = Get.find<TaskController>();

    // تحميل الرسائل الجديدة
    await taskController.loadTaskMessages(widget.taskId);

    // التمرير إلى الأسفل بعد تحميل الرسائل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_messageScrollController.hasClients) {
        _messageScrollController.animateTo(
          _messageScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _handleTabChange() {
    // Force a rebuild when tab changes to update the comment input visibility
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _commentController.dispose();
    _messageController.dispose();
    _progressController.dispose();
    _messageScrollController.dispose();

    // إلغاء الاشتراك في إشعارات تحديث الرسائل
    _messagesUpdateSubscription?.dispose();

    // إزالة متحكم التبويب من GetX
    Get.delete<TabController>(tag: 'task_detail_tabs');

    super.dispose();
  }

  /// Loads task details from the task controller
  Future<void> _loadTaskDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the task controller
      final taskController = Get.find<TaskController>();
      debugPrint(
          'جاري تحميل تفاصيل المهمة في TaskDetailScreen: ${widget.taskId}');
      await taskController.loadTaskDetails(widget.taskId);

      // التحقق من وجود أخطاء
      if (taskController.error.value.isNotEmpty) {
        debugPrint(
            'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error.value}');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error.value}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 5),
        );
      }

      // التحقق من وجود المهمة
      if (taskController.currentTask.value == null) {
        debugPrint('لم يتم العثور على المهمة: ${widget.taskId}');
        Get.snackbar(
          'خطأ',
          'لم يتم العثور على المهمة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        debugPrint(
            'تم تحميل تفاصيل المهمة بنجاح: ${taskController.currentTask.value!.title}');
      }
    } catch (e) {
      debugPrint('حدث خطأ غير متوقع أثناء تحميل تفاصيل المهمة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 5),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Builds the task detail screen UI
  @override
  Widget build(BuildContext context) {
    // Get controllers
    final taskController = Get.find<TaskController>();

    // Check if we're on a large screen (tablet or desktop)
    final isLargeScreen = ResponsiveHelper.isTablet(context) ||
        ResponsiveHelper.isDesktop(context);

    return Scaffold(
      appBar: AppBar(
        title: Obx(() =>
            Text(taskController.currentTask.value?.title ?? 'taskDetails'.tr)),
        actions: [
          // Toggle sidebar button (only on large screens)
          if (isLargeScreen)
            IconButton(
              icon:
                  Icon(_showSidebar ? Icons.chevron_right : Icons.chevron_left),
              tooltip: _showSidebar ? 'hideSidebar'.tr : 'showSidebar'.tr,
              onPressed: () {
                setState(() {
                  _showSidebar = !_showSidebar;
                });
              },
            ),
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'editTask'.tr,
            onPressed: () {
              // Navigate to inline edit task screen using GetX
              Get.to(() => InlineEditTaskScreen(taskId: widget.taskId))
                  ?.then((_) => _loadTaskDetails());
            },
          ),
          // Botón de transferencia de tareas
          Obx(() {
            final task = taskController.currentTask.value;
            if (task == null) return const SizedBox.shrink();

            return TaskTransferButton(
              task: task,
              showLabel: false,
              isSmall: true,
              onTransferComplete: _loadTaskDetails,
            );
          }),

          // Botón de actualización de progreso
          // IconButton(
          //   icon: const Icon(Icons.update),
          //   tooltip: 'updateProgress'.tr,
          //   onPressed: _showProgressUpdateDialog,
          // ),

          // Menú de opciones adicionales
          PopupMenuButton<String>(
            onSelected: (value) {
              // Handle menu actions
              switch (value) {
                case 'delete':
                  _showDeleteConfirmation();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'delete',
                textStyle: const TextStyle(color: AppColors.error),
                child: Text('deleteTask'.tr),
              ),
            ],
          ),
        ],
      ),
      body: GetBuilder<TaskController>(
          id: 'task_details',
          builder: (controller) {
            // عرض مؤشر التحميل إذا كانت حالة التحميل نشطة
            if (_isLoading || taskController.isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }

            // عرض رسالة خطأ إذا كان هناك خطأ
            if (taskController.error.value.isNotEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 64,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'تعذر الوصول إلى تفاصيل المهمة',
                        style: AppStyles.headingMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Text(
                          taskController.error.value,
                          style: AppStyles.bodyLarge.copyWith(
                            color: Colors.red.shade800,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _loadTaskDetails,
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة المحاولة'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: () => Get.back(),
                            icon: const Icon(Icons.arrow_back),
                            label: const Text('العودة'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }

            // عرض رسالة إذا لم يتم العثور على المهمة
            if (taskController.currentTask.value == null) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.search_off,
                        color: Colors.orange,
                        size: 64,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'لم يتم العثور على المهمة',
                        style: AppStyles.headingMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'المهمة غير موجودة أو تم حذفها',
                              style: AppStyles.bodyLarge.copyWith(
                                color: Colors.orange.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'تأكد من أن رقم المهمة صحيح وأن لديك صلاحية للوصول إليها',
                              style: AppStyles.bodyMedium.copyWith(
                                color: Colors.orange.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _loadTaskDetails,
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة المحاولة'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: () => Get.back(),
                            icon: const Icon(Icons.arrow_back),
                            label: const Text('العودة'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }

            final task = taskController.currentTask.value!;

            // For large screens, use a side-by-side layout
            if (isLargeScreen) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left side - Task details
                  Expanded(
                    flex: _leftPanelFlex.toInt(),
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTaskHeader(task),
                          const SizedBox(height: 16),
                          // Additional task details can be shown here
                        ],
                      ),
                    ),
                  ),

                  // فاصل قابل للسحب
                  if (_showSidebar)
                    DraggableDivider(
                      direction: Axis.vertical,
                      thickness: 8.0,
                      color: Colors.grey.shade200,
                      activeColor:
                          AppColors.primary.withAlpha(51), // 0.2 * 255 = ~51
                      onDrag: (delta) {
                        // تحديث حجم الأقسام بناءً على حركة السحب
                        setState(() {
                          // تحويل دلتا إلى تغيير في نسبة الـ flex
                          // قيمة موجبة تعني زيادة اليسار وتقليل اليمين

                          // تحسين معامل التحويل لجعل الحركة أكثر سلاسة
                          // استخدام معامل أصغر للشاشات الكبيرة
                          final screenWidth = MediaQuery.of(context).size.width;
                          final conversionFactor =
                              screenWidth > 1200 ? 80.0 : 40.0;
                          final flexDelta = delta / conversionFactor;

                          // التحقق من الحدود مع تحسين الدقة
                          if (_leftPanelFlex + flexDelta >= _minPanelFlex &&
                              _rightPanelFlex - flexDelta >= _minPanelFlex &&
                              _leftPanelFlex + flexDelta <= _maxPanelFlex &&
                              _rightPanelFlex - flexDelta <= _maxPanelFlex) {
                            // تطبيق التغيير مع تقريب القيم لتجنب الكسور الصغيرة جداً
                            _leftPanelFlex = (_leftPanelFlex + flexDelta)
                                .clamp(_minPanelFlex, _maxPanelFlex);
                            _rightPanelFlex = (_rightPanelFlex - flexDelta)
                                .clamp(_minPanelFlex, _maxPanelFlex);
                          }
                        });
                      },
                    ),

                  // Right side - Tabs (only shown if _showSidebar is true)
                  if (_showSidebar)
                    Expanded(
                      flex: _rightPanelFlex.toInt(),
                      child: Column(
                        children: [
                          // Tab bar
                          TabBar(
                            controller: _tabController,
                            isScrollable: true,
                            tabs: [
                              Tab(text: 'النظرة الشاملة'),
                              Tab(text: 'taskDetailsTab'.tr),
                              Tab(text: 'المهام الفرعية'),
                              Tab(text: 'تقدم المهمة'),
                              Tab(text: 'taskFilesTab'.tr),
                              Tab(text: 'المستندات'),
                              Tab(text: 'comments'.tr),
                              Tab(text: 'المحادثة'.tr),
                              Tab(text: 'taskActivityLog'.tr),
                              Tab(text: 'تحويلات المهمة'),
                              Tab(text: 'taskContributors'.tr),
                              Tab(text: 'تفاصيل المساهمات'),
                            ],
                          ),

                          // Tab content
                          Expanded(
                            child: TabBarView(
                              controller: _tabController,
                              children: [
                                // Overview tab (النظرة الشاملة)
                                const TaskOverviewTab(),

                                // Details tab
                                _buildDetailsTab(task),

                                // Subtasks tab
                                SubtasksTab(task: task),

                                // Progress tab
                                const TaskProgressTab(),

                                // Files tab
                                const TaskAttachmentsTab(),

                                // Documents tab
                                TaskDocumentsTab(task: task),

                                // Comments tab
                                _buildUpdatesTab(taskController.comments),

                                // Conversation tab
                                _buildConversationTab(
                                    taskController.taskMessages),

                                // Activity log tab
                                _buildActivityLogTab(
                                    taskController.taskHistory),

                                // Task transfer history tab
                                TaskTransferHistoryTab(taskId: task.id),

                                // Contributors tab
                                _buildContributorsTab(
                                    taskController.userContributions),

                                // تفاصيل المساهمات
                                ContributorContributionsTab(
                                  taskId: task.id,
                                  selectedUserId: _selectedContributorId,
                                  onContributorDeselected: () {
                                    setState(() {
                                      _selectedContributorId = null;
                                    });
                                    // العودة إلى تبويب المساهمين
                                    _tabController.animateTo(9);
                                  },
                                ),
                              ],
                            ),
                          ),

                          // Comment input (only shown in comments tab)
                          _tabController.index == 5
                              ? _buildCommentInput()
                              : const SizedBox.shrink(),

                          // Message input (only shown in conversation tab)
                          _tabController.index == 6
                              ? _buildMessageInput()
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                ],
              );
            }

            // For small screens, use a stacked layout with tabs
            return Column(
              children: [
                // Task header
                _buildTaskHeader(task),

                // Tab bar
                TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  labelColor: Get.isDarkMode ? Colors.white : Colors.blue,
                  unselectedLabelColor:
                      Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                  indicatorColor: Get.isDarkMode ? Colors.white : Colors.blue,
                  tabs: [
                    Tab(text: 'النظرة الشاملة'),
                    Tab(text: 'taskDetailsTab'.tr),
                    Tab(text: 'المهام الفرعية'),
                    Tab(text: 'تقدم المهمة'),
                    Tab(text: 'taskFilesTab'.tr),
                    Tab(text: 'المستندات'),
                    Tab(text: 'comments'.tr),
                    Tab(text: 'المحادثة'.tr),
                    Tab(text: 'taskActivityLog'.tr),
                    Tab(text: 'تحويلات المهمة'),
                    Tab(text: 'taskContributors'.tr),
                    Tab(text: 'تفاصيل المساهمات'),
                  ],
                ),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Overview tab (النظرة الشاملة)
                      const TaskOverviewTab(),

                      // Details tab
                      _buildDetailsTab(task),

                      // Subtasks tab
                      SubtasksTab(task: task),

                      // Progress tab
                      const TaskProgressTab(),

                      // Files tab
                      const TaskAttachmentsTab(),

                      // Documents tab
                      TaskDocumentsTab(task: task),

                      // Comments tab
                      _buildUpdatesTab(taskController.comments),

                      // Conversation tab
                      _buildConversationTab(taskController.taskMessages),

                      // Activity log tab
                      _buildActivityLogTab(taskController.taskHistory),

                      // Task transfer history tab
                      TaskTransferHistoryTab(taskId: task.id),

                      // Contributors tab
                      _buildContributorsTab(taskController.userContributions),

                      // تفاصيل المساهمات
                      ContributorContributionsTab(
                        taskId: task.id,
                        selectedUserId: _selectedContributorId,
                        onContributorDeselected: () {
                          setState(() {
                            _selectedContributorId = null;
                          });
                          // العودة إلى تبويب المساهمين
                          _tabController.animateTo(9);
                        },
                      ),
                    ],
                  ),
                ),

                // Comment input (only shown in comments tab)
                _tabController.index == 5
                    ? _buildCommentInput()
                    : const SizedBox.shrink(),

                // Message input (only shown in conversation tab)
                _tabController.index == 6
                    ? _buildMessageInput()
                    : const SizedBox.shrink(),
              ],
            );
          }),
    );
  }

  Widget _buildTaskHeader(Task task) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Task title and status
          Row(
            children: [
              Expanded(
                child: Text(
                  task.title,
                  style: AppStyles.headingMedium,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.getTaskStatusColor(task.status.index)
                      .withAlpha(50),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getStatusIcon(task.status),
                      size: 16,
                      color: AppColors.getTaskStatusColor(task.status.index),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getStatusText(task.status),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.getTaskStatusColor(task.status.index),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Task details
          Row(
            children: [
              // Due date
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ الاستحقاق',
                      style: AppStyles.labelMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getTimelineColor(task),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        task.dueDate != null
                            ? DateFormatter.formatShortDate(task.dueDate!)
                            : 'لا يوجد تاريخ استحقاق',
                        style: AppStyles.bodySmall.copyWith(
                          color: Colors
                              .white, // الأبيض مناسب هنا لأن الخلفية ملونة
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Priority
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الأولوية',
                      style: AppStyles.labelMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            AppColors.getTaskPriorityColor(task.priority.index)
                                .withAlpha(50),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _getPriorityText(task.priority),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.getTaskPriorityColor(
                              task.priority.index),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Assignee
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المسؤول',
                      style: AppStyles.labelMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 12,
                          backgroundColor: AppColors.primary,
                          child: const Icon(
                            Icons.person,
                            size: 16,
                            color: Colors
                                .white, // الأبيض مناسب هنا لأن الخلفية ملونة
                          ),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: task.assigneeId != null
                              ? FutureBuilder<String>(
                                  future: Get.find<UserController>()
                                      .getUserNameById(task.assigneeId!),
                                  builder: (context, snapshot) {
                                    return Text(
                                      snapshot.data ?? 'assignedUser'.tr,
                                      style: AppStyles.bodySmall,
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  },
                                )
                              : Text(
                                  'unassigned'.tr,
                                  style: AppStyles.bodySmall,
                                  overflow: TextOverflow.ellipsis,
                                ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Task description
          Text(
            'الوصف',
            style: AppStyles.labelMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            task.description.isNotEmpty ? task.description : 'لا يوجد وصف',
            style: AppStyles.bodyMedium,
          ),

          const SizedBox(height: 16),

          // عرض عدد المهام الفرعية
          FutureBuilder<int>(
            future: Get.find<TaskController>().getSubtasksCount(task.id),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const SizedBox.shrink();
              }

              final subtasksCount = snapshot.data ?? 0;
              if (subtasksCount > 0) {
                return FutureBuilder<int>(
                  future: Get.find<TaskController>()
                      .getCompletedSubtasksCount(task.id),
                  builder: (context, completedSnapshot) {
                    final completedCount = completedSnapshot.data ?? 0;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Use a Wrap widget to handle overflow
                        Wrap(
                          spacing: 8, // horizontal spacing
                          runSpacing: 8, // vertical spacing when wrapped
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            // Icon and title
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.checklist,
                                  size: 16,
                                  color: AppColors.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'المهام الفرعية',
                                  style: AppStyles.labelMedium.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),

                            // Completion counter
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withAlpha(50),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '$completedCount/$subtasksCount مكتملة',
                                style: AppStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                            // View all button
                            TextButton(
                              onPressed: () {
                                // الانتقال إلى تبويب المهام الفرعية (التبويب رقم 2)
                                _tabController.animateTo(2);
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                minimumSize: const Size(0, 0),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                'عرض الكل',
                                style: AppStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: subtasksCount > 0
                              ? completedCount / subtasksCount
                              : 0,
                          backgroundColor: Colors.grey.shade200,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(AppColors.primary),
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ],
                    );
                  },
                );
              }

              return const SizedBox.shrink();
            },
          ),

          const SizedBox(height: 16),

          // Progress bar
          // تحقق من حجم الشاشة لتطبيق تصميم متجاوب
          LayoutBuilder(
            builder: (context, constraints) {
              // تحقق من عرض الشاشة وتكييف التخطيط وفقًا لذلك
              final isSmallScreen = constraints.maxWidth < 400;

              return isSmallScreen
                  // تخطيط عمودي للشاشات الصغيرة
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'التقدم: ${task.completionPercentage.toInt()}%',
                          style: AppStyles.labelMedium,
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: task.completionPercentage / 100,
                          backgroundColor: Get.isDarkMode
                              ? const Color(0xFF424242)
                              : const Color(0xFFEEEEEE),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.getTaskStatusColor(task.status.index),
                          ),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _showProgressUpdateDialog(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors
                                  .white, // الأبيض مناسب هنا لأن الخلفية ملونة
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                            child: Text('updateProgress'.tr),
                          ),
                        ),
                      ],
                    )
                  // تخطيط أفقي للشاشات الكبيرة
                  : Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'التقدم: ${task.completionPercentage.toInt()}%',
                                style: AppStyles.labelMedium,
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: task.completionPercentage / 100,
                                backgroundColor: Get.isDarkMode
                                    ? const Color(0xFF424242)
                                    : const Color(0xFFEEEEEE),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.getTaskStatusColor(
                                      task.status.index),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () => _showProgressUpdateDialog(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors
                                .white, // الأبيض مناسب هنا لأن الخلفية ملونة
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                          ),
                          child: Text('updateProgress'.tr),
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  // تم نقل هذه الوظيفة إلى TaskAttachmentsTab

  /// Builds the updates tab content
  Widget _buildUpdatesTab(RxList<Comment> comments) {
    if (comments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Get.isDarkMode
                  ? const Color(0xFF424242)
                  : const Color(0xFFDDDDDD),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تحديثات بعد',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'شارك التقدم، أشر إلى زميل،\nأو قم برفع ملف للبدء',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        final comment = comments[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Comment header
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: AppColors.primary,
                      child: const Icon(
                        Icons.person,
                        size: 20,
                        color:
                            Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FutureBuilder<String>(
                            future: Get.find<UserController>()
                                .getUserNameById(comment.userId),
                            builder: (context, snapshot) {
                              return Text(
                                snapshot.data ?? 'user'.tr,
                                style: AppStyles.titleSmall,
                              );
                            },
                          ),
                          Text(
                            DateFormatter.formatDateTime(comment.createdAt),
                            style: AppStyles.labelSmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        // TODO: Handle comment actions
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف'),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Comment content
                Text(
                  comment.content,
                  style: AppStyles.bodyMedium,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Builds the activity log tab content
  Widget _buildActivityLogTab(RxList<TaskHistory> history) {
    if (history.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Get.isDarkMode
                  ? const Color(0xFF424242)
                  : const Color(0xFFDDDDDD),
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد نشاط بعد',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: history.length,
      itemBuilder: (context, index) {
        final item = history[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Timeline dot and line
              Column(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getActivityColor(item.actionType),
                      shape: BoxShape.circle,
                    ),
                  ),
                  if (index < history.length - 1)
                    Container(
                      width: 2,
                      height: 40,
                      color: Get.isDarkMode
                          ? const Color(0xFF424242)
                          : const Color(0xFFDDDDDD),
                    ),
                ],
              ),

              const SizedBox(width: 16),

              // Activity content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getActivityText(item),
                      style: AppStyles.bodyMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      DateFormatter.formatDateTime(item.timestamp),
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (item.description != null &&
                        item.description!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Get.isDarkMode
                              ? const Color(0xFF2A2A2A)
                              : const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          item.description!,
                          style: AppStyles.bodySmall,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds the comment input field
  Widget _buildCommentInput() {
    // Get controllers
    final taskController = Get.find<TaskController>();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          top: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'اكتب تحديثًا وأشر إلى الآخرين باستخدام @',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.getBorderColor()),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: 3,
              minLines: 1,
            ),
          ),
          const SizedBox(width: 16),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: () {
              // Submit comment
              if (_commentController.text.trim().isNotEmpty) {
                // Add comment using the task controller
                taskController.addComment(
                  widget.taskId,
                  _commentController.text.trim(),
                );
                _commentController.clear();
              }
            },
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Color _getTimelineColor(Task task) {
    if (task.status == TaskStatus.completed) {
      return AppColors.success;
    }

    if (task.dueDate == null) {
      return AppColors.statusPending;
    }

    if (task.isOverdue()) {
      return AppColors.error;
    }

    // Due soon (within 3 days)
    final daysUntilDue = task.dueDate!.difference(DateTime.now()).inDays;
    if (daysUntilDue <= 3) {
      return AppColors.warning;
    }

    return AppColors.info;
  }

  // تم نقل هذه الوظيفة إلى TaskAttachmentsTab

  // تم نقل هذه الوظائف إلى TaskAttachmentsTab

  /// بناء محتوى تبويب المحادثة
  /// يعرض قائمة الرسائل المرتبطة بالمهمة الحالية
  /// @param messages قائمة الرسائل المراد عرضها
  /// @return واجهة عرض المحادثة
  /// تم إضافة دعم لعرض المرفقات (صور، ملفات) في المحادثة
  /// تم إضافة دعم للرد على رسائل محددة
  /// TODO: إضافة إمكانية تحميل المزيد من الرسائل عند التمرير لأعلى
  Widget _buildConversationTab(RxList<TaskMessage> messages) {
    if (messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد محادثات بعد',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ محادثة حول هذه المهمة',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Get the current user
    final currentUser = Get.find<AuthController>().currentUser.value;
    if (currentUser == null) {
      return const Center(child: Text('يجب تسجيل الدخول لعرض المحادثات'));
    }

    return ListView.builder(
      controller: _messageScrollController,
      padding: const EdgeInsets.all(16),
      // استخدام addAutomaticKeepAlives لضمان الاحتفاظ بحالة التمرير
      addAutomaticKeepAlives: false,
      // استخدام cacheExtent أكبر لتحسين الأداء
      cacheExtent: 1000,
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final isCurrentUser = message.senderId == currentUser.id;
        final showAvatar =
            index == 0 || messages[index - 1].senderId != message.senderId;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            mainAxisAlignment:
                isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar for other users
              if (!isCurrentUser && showAvatar)
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.accent,
                  child: FutureBuilder<String>(
                    future: Get.find<UserController>()
                        .getUserNameById(message.senderId),
                    builder: (context, snapshot) {
                      final initial = snapshot.data?.isNotEmpty == true
                          ? snapshot.data!.substring(0, 1).toUpperCase()
                          : '?';
                      return Text(
                        initial,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12),
                      );
                    },
                  ),
                ),
              if (!isCurrentUser && showAvatar) const SizedBox(width: 8),

              // Message content
              Flexible(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: isCurrentUser
                        ? AppColors.primary.withAlpha(25)
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(
                      color: isCurrentUser
                          ? AppColors.primary.withAlpha(75)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (!isCurrentUser && showAvatar)
                        FutureBuilder<String>(
                          future: Get.find<UserController>()
                              .getUserNameById(message.senderId),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? 'user'.tr,
                              style: AppStyles.labelSmall.copyWith(
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      if (!isCurrentUser && showAvatar)
                        const SizedBox(height: 4),
                      // عرض الرسالة التي تم الرد عليها إذا وجدت
                      if (message.replyToMessageId != null)
                        FutureBuilder<TaskMessage?>(
                          future: Get.find<TaskController>()
                              .getMessageById(message.replyToMessageId!),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const SizedBox(
                                height: 20,
                                width: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              );
                            }

                            final replyMessage = snapshot.data;
                            if (replyMessage != null) {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 8),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.grey.shade200),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.reply,
                                          size: 14,
                                          color: AppColors.textSecondary,
                                        ),
                                        const SizedBox(width: 4),
                                        FutureBuilder<String>(
                                          future: Get.find<UserController>()
                                              .getUserNameById(
                                                  replyMessage.senderId),
                                          builder: (context, snapshot) {
                                            return Text(
                                              'رد على ${snapshot.data ?? 'مستخدم'}',
                                              style:
                                                  AppStyles.labelSmall.copyWith(
                                                color: AppColors.textSecondary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      replyMessage.content,
                                      style: AppStyles.bodySmall.copyWith(
                                        color: AppColors.textSecondary,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              );
                            }

                            return const SizedBox.shrink();
                          },
                        ),

                      // عرض محتوى الرسالة مع تمييز المستخدمين المذكورين
                      _buildMessageContent(message),
                      const SizedBox(height: 4),

                      // عرض الوقت وأزرار التفاعل
                      Row(
                        mainAxisAlignment: isCurrentUser
                            ? MainAxisAlignment.end
                            : MainAxisAlignment.start,
                        children: [
                          Text(
                            DateFormatter.formatTime(message.createdAt),
                            style: AppStyles.labelSmall.copyWith(
                              color: AppColors.textSecondary,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 8),
                          // زر الرد على الرسالة
                          InkWell(
                            onTap: () {
                              setState(() {
                                _replyToMessage = message;
                              });
                              // التركيز على حقل الإدخال
                              FocusScope.of(context).requestFocus(FocusNode());
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                FocusScope.of(context)
                                    .requestFocus(FocusNode());
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.reply,
                                    size: 14,
                                    color: AppColors.textSecondary,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    'رد',
                                    style: AppStyles.labelSmall.copyWith(
                                      color: AppColors.textSecondary,
                                      fontSize: 10,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Avatar for current user
              if (isCurrentUser && showAvatar) const SizedBox(width: 8),
              if (isCurrentUser && showAvatar)
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    currentUser.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// بناء حقل إدخال الرسائل
  /// يعرض حقل نصي لإدخال الرسالة وزر للإرسال وزر لإرفاق الملفات
  Widget _buildMessageInput() {
    // الحصول على وحدات التحكم
    final taskController = Get.find<TaskController>();
    final userController = Get.find<UserController>();

    // قائمة المستخدمين المذكورين في الرسالة
    final List<String> mentionedUserIds = [];

    // استخدام StatefulBuilder لإدارة حالة المرفقات والمستخدمين المذكورين
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setAttachmentState) {
        // قائمة الملفات المرفقة المؤقتة (داخل StatefulBuilder)
        final List<dynamic> tempAttachments = [];
        final List<String> tempAttachmentNames = [];

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: AppColors.border),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عرض الرسالة التي يتم الرد عليها
              _replyToMessage != null
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.reply,
                                size: 16,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'الرد على رسالة',
                                style: AppStyles.labelMedium.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: const Icon(Icons.close, size: 16),
                                onPressed: () {
                                  setState(() {
                                    _replyToMessage = null;
                                  });
                                },
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                color: Colors.grey.shade700,
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          FutureBuilder<String>(
                            future: Get.find<UserController>()
                                .getUserNameById(_replyToMessage!.senderId),
                            builder: (context, snapshot) {
                              return Text(
                                snapshot.data ?? 'مستخدم',
                                style: AppStyles.labelSmall.copyWith(
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _replyToMessage!.content,
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),

              // عرض المستخدمين المذكورين
              if (_replyToMessage != null && mentionedUserIds.isNotEmpty)
                const SizedBox(height: 8),
              mentionedUserIds.isNotEmpty
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المستخدمون المذكورون (${mentionedUserIds.length})',
                            style: AppStyles.labelMedium.copyWith(
                              color: AppColors.primary,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: mentionedUserIds.map((userId) {
                              return FutureBuilder<String>(
                                future: Get.find<UserController>()
                                    .getUserNameById(userId),
                                builder: (context, snapshot) {
                                  final userName = snapshot.data ?? 'مستخدم';
                                  return Chip(
                                    avatar: CircleAvatar(
                                      backgroundColor: AppColors.primary,
                                      child: Text(
                                        userName.isNotEmpty
                                            ? userName[0].toUpperCase()
                                            : '?',
                                        style: const TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ),
                                    label: Text(
                                      userName,
                                      style: AppStyles.bodySmall,
                                    ),
                                    deleteIcon:
                                        const Icon(Icons.close, size: 16),
                                    onDeleted: () {
                                      setAttachmentState(() {
                                        mentionedUserIds.remove(userId);
                                      });
                                    },
                                  );
                                },
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),

              // عرض الملفات المرفقة المؤقتة
              if (tempAttachments.isNotEmpty) const SizedBox(height: 8),
              tempAttachments.isNotEmpty
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المرفقات (${tempAttachments.length})',
                            style: AppStyles.labelMedium,
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: List.generate(
                              tempAttachments.length,
                              (index) => Chip(
                                label: Text(
                                  tempAttachmentNames[index],
                                  style: AppStyles.bodySmall,
                                ),
                                deleteIcon: const Icon(Icons.close, size: 16),
                                onDeleted: () {
                                  setAttachmentState(() {
                                    tempAttachments.removeAt(index);
                                    tempAttachmentNames.removeAt(index);
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),

              const SizedBox(height: 8),

              // حقل إدخال الرسالة وأزرار الإرسال
              LayoutBuilder(
                builder: (context, constraints) {
                  // تحقق من عرض الشاشة وتكييف التخطيط وفقًا لذلك
                  final isSmallScreen = constraints.maxWidth < 400;

                  return isSmallScreen
                      // تخطيط عمودي للشاشات الصغيرة
                      ? Column(
                          children: [
                            // صف أزرار الإرفاق
                            Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.attach_file),
                                  onPressed: () {
                                    _showAttachmentOptions();
                                  },
                                  color: AppColors.primary,
                                  tooltip: 'إرفاق ملف',
                                ),
                                const Spacer(),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // صف حقل الإدخال وزر الإرسال
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _messageController,
                                    decoration: InputDecoration(
                                      hintText:
                                          'اكتب رسالة... استخدم @ للإشارة',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(24),
                                        borderSide: BorderSide.none,
                                      ),
                                      filled: true,
                                      fillColor: Colors.grey.shade100,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 12),
                                    ),
                                    textInputAction: TextInputAction.send,
                                    onSubmitted: (_) =>
                                        _sendMessage(mentionedUserIds),
                                    onChanged: (value) {
                                      // التحقق من وجود علامة @ في النص
                                      if (value.endsWith('@')) {
                                        Future.microtask(() async {
                                          try {
                                            final departmentRepo = Get.find<
                                                DepartmentRepository>();
                                            final departments =
                                                await departmentRepo
                                                    .getAllDepartments();
                                            final users = userController.users;

                                            if (!mounted) return;

                                            final selectedUserId =
                                                await showSingleUserSelectionDialog(
                                              context: Get.context!,
                                              users: users,
                                              departments: departments,
                                              title: 'اختر مستخدم للإشارة إليه',
                                            );

                                            if (selectedUserId != null &&
                                                mounted) {
                                              final user = users.firstWhere(
                                                (u) => u.id == selectedUserId,
                                                orElse: () => User(
                                                  id: selectedUserId,
                                                  name: 'مستخدم',
                                                  email: '',
                                                  password: '',
                                                  role: UserRole.employee,
                                                  createdAt: DateTime.now(),
                                                ),
                                              );

                                              setAttachmentState(() {
                                                if (!mentionedUserIds
                                                    .contains(selectedUserId)) {
                                                  mentionedUserIds
                                                      .add(selectedUserId);
                                                }
                                              });

                                              final currentText =
                                                  _messageController.text;
                                              final newText =
                                                  "${currentText.substring(0, currentText.length - 1)}@${user.name} ";
                                              _messageController.value =
                                                  TextEditingValue(
                                                text: newText,
                                                selection:
                                                    TextSelection.collapsed(
                                                        offset: newText.length),
                                              );
                                            }
                                          } catch (e) {
                                            Get.snackbar(
                                              'خطأ',
                                              'حدث خطأ أثناء محاولة الإشارة إلى مستخدم: $e',
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              backgroundColor:
                                                  Colors.red.shade100,
                                              colorText: Colors.red.shade800,
                                            );
                                          }
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Obx(() => taskController.isSendingMessage.value
                                    ? const SizedBox(
                                        width: 48,
                                        height: 48,
                                        child: Padding(
                                          padding: EdgeInsets.all(12.0),
                                          child: CircularProgressIndicator(
                                              strokeWidth: 2),
                                        ),
                                      )
                                    : IconButton(
                                        icon: const Icon(Icons.send),
                                        onPressed: () {
                                          _sendMessage(mentionedUserIds);
                                          if (tempAttachments.isNotEmpty) {
                                            setAttachmentState(() {
                                              tempAttachments.clear();
                                              tempAttachmentNames.clear();
                                            });
                                          }
                                          setAttachmentState(() {
                                            mentionedUserIds.clear();
                                          });
                                        },
                                        color: AppColors.primary,
                                        iconSize: 24,
                                        padding: const EdgeInsets.all(12),
                                        tooltip: 'إرسال الرسالة',
                                      )),
                              ],
                            ),
                          ],
                        )
                      // تخطيط أفقي للشاشات الكبيرة
                      : Row(
                          children: [
                            // زر إرفاق الملفات
                            IconButton(
                              icon: const Icon(Icons.attach_file),
                              onPressed: () {
                                _showAttachmentOptions();
                              },
                              color: AppColors.primary,
                              tooltip: 'إرفاق ملف',
                            ),

                            Expanded(
                              child: TextField(
                                controller: _messageController,
                                decoration: InputDecoration(
                                  hintText:
                                      'اكتب رسالة... استخدم @ للإشارة إلى مستخدم',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(24),
                                    borderSide: BorderSide.none,
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey.shade100,
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                ),
                                textInputAction: TextInputAction.send,
                                onSubmitted: (_) =>
                                    _sendMessage(mentionedUserIds),
                                onChanged: (value) {
                                  if (value.endsWith('@')) {
                                    Future.microtask(() async {
                                      try {
                                        final departmentRepo =
                                            Get.find<DepartmentRepository>();
                                        final departments = await departmentRepo
                                            .getAllDepartments();
                                        final users = userController.users;

                                        if (!mounted) return;

                                        final selectedUserId =
                                            await showSingleUserSelectionDialog(
                                          context: Get.context!,
                                          users: users,
                                          departments: departments,
                                          title: 'اختر مستخدم للإشارة إليه',
                                        );

                                        if (selectedUserId != null && mounted) {
                                          final user = users.firstWhere(
                                            (u) => u.id == selectedUserId,
                                            orElse: () => User(
                                              id: selectedUserId,
                                              name: 'مستخدم',
                                              email: '',
                                              password: '',
                                              role: UserRole.employee,
                                              createdAt: DateTime.now(),
                                            ),
                                          );

                                          setAttachmentState(() {
                                            if (!mentionedUserIds
                                                .contains(selectedUserId)) {
                                              mentionedUserIds
                                                  .add(selectedUserId);
                                            }
                                          });

                                          final currentText =
                                              _messageController.text;
                                          final newText =
                                              "${currentText.substring(0, currentText.length - 1)}@${user.name} ";
                                          _messageController.value =
                                              TextEditingValue(
                                            text: newText,
                                            selection: TextSelection.collapsed(
                                                offset: newText.length),
                                          );
                                        }
                                      } catch (e) {
                                        Get.snackbar(
                                          'خطأ',
                                          'حدث خطأ أثناء محاولة الإشارة إلى مستخدم: $e',
                                          snackPosition: SnackPosition.BOTTOM,
                                          backgroundColor: Colors.red.shade100,
                                          colorText: Colors.red.shade800,
                                        );
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Obx(() => taskController.isSendingMessage.value
                                ? const SizedBox(
                                    width: 48,
                                    height: 48,
                                    child: Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                  )
                                : IconButton(
                                    icon: const Icon(Icons.send),
                                    onPressed: () {
                                      _sendMessage(mentionedUserIds);
                                      if (tempAttachments.isNotEmpty) {
                                        setAttachmentState(() {
                                          tempAttachments.clear();
                                          tempAttachmentNames.clear();
                                        });
                                      }
                                      setAttachmentState(() {
                                        mentionedUserIds.clear();
                                      });
                                    },
                                    color: AppColors.primary,
                                    iconSize: 24,
                                    padding: const EdgeInsets.all(12),
                                    tooltip: 'إرسال الرسالة',
                                  )),
                          ],
                        );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// إرسال رسالة
  /// يتحقق من المحتوى ويرسل الرسالة إلى المهمة الحالية
  /// يمسح حقل الإدخال ويمرر إلى أسفل المحادثة بعد الإرسال
  /// يدعم الإشارة إلى المستخدمين في الرسائل والرد على رسائل محددة
  void _sendMessage(List<String> mentionedUserIds) {
    // التحقق من محتوى الرسالة
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    // الحصول على وحدات التحكم
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    // التحقق من تسجيل دخول المستخدم
    if (authController.currentUser.value == null) {
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول لإرسال رسائل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // إرسال الرسالة مع قائمة المستخدمين المذكورين والرد على رسالة إذا وجدت
    taskController
        .sendTaskMessage(
      widget.taskId,
      content,
      mentionedUserIds: mentionedUserIds.isNotEmpty ? mentionedUserIds : null,
      replyToMessageId: _replyToMessage?.id,
    )
        .then((message) {
      if (message != null) {
        // مسح حقل الإدخال وإعادة تعيين الرسالة التي يتم الرد عليها
        _messageController.clear();
        setState(() {
          _replyToMessage = null;
        });

        // التمرير إلى أسفل المحادثة بعد الإرسال
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_messageScrollController.hasClients) {
            _messageScrollController.animateTo(
              _messageScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
  }

  /// عرض خيارات إرفاق الملفات
  void _showAttachmentOptions() {
    final taskController = Get.find<TaskController>();

    // التحقق من وجود رسالة حالية
    if (taskController.currentTask.value == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('إرفاق ملف'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text('صورة من المعرض'.tr),
              onTap: () async {
                Get.back();
                await _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text('التقاط صورة'.tr),
              onTap: () async {
                Get.back();
                await _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file),
              title: Text('ملف'.tr),
              onTap: () async {
                Get.back();
                await _pickFile();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        await _uploadMessageAttachment(
            image.path, 'image/${image.name.split('.').last}', image.name);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        await _uploadMessageAttachment(
            image.path, 'image/${image.name.split('.').last}', image.name);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء التقاط الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// اختيار ملف
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path != null) {
          final fileType = file.extension != null
              ? 'application/${file.extension}'
              : 'application/octet-stream';

          await _uploadMessageAttachment(file.path!, fileType, file.name);
        } else {
          Get.snackbar(
            'تنبيه'.tr,
            'لا يمكن الوصول إلى مسار الملف. قد يكون هذا بسبب قيود المتصفح.'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.amber.shade100,
            colorText: Colors.amber.shade800,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// رفع مرفق للرسالة
  Future<void> _uploadMessageAttachment(
      String filePath, String fileType, String fileName) async {
    final taskController = Get.find<TaskController>();

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // إنشاء رسالة جديدة أولاً إذا لم تكن هناك رسالة
      TaskMessage? message;

      // إرسال رسالة جديدة مع المرفق
      final content = _messageController.text.trim().isNotEmpty
          ? _messageController.text.trim()
          : 'مرفق: $fileName';

      // تحديد نوع المحتوى بناءً على نوع الملف
      final contentType = fileType.startsWith('image/')
          ? TaskMessageContentType.image
          : TaskMessageContentType.file;

      // إرسال الرسالة
      message = await taskController.sendTaskMessage(
        widget.taskId,
        content,
        contentType: contentType,
      );

      if (message != null) {
        // مسح حقل الإدخال
        _messageController.clear();

        // رفع المرفق للرسالة
        final file = File(filePath);
        final attachment = await taskController.addMessageAttachment(
          message.id,
          file,
          fileName,
          fileType,
        );

        // إغلاق مؤشر التحميل
        Get.back();

        if (attachment != null) {
          Get.snackbar(
            'نجاح'.tr,
            'تم إرسال المرفق بنجاح'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );

          // التمرير إلى أسفل المحادثة بعد الإرسال
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_messageScrollController.hasClients) {
              _messageScrollController.animateTo(
                _messageScrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        } else {
          Get.snackbar(
            'خطأ'.tr,
            'فشل رفع المرفق'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      } else {
        // إغلاق مؤشر التحميل
        Get.back();

        Get.snackbar(
          'خطأ'.tr,
          'فشل إرسال الرسالة'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء رفع المرفق: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// بناء محتوى الرسالة مع تمييز المستخدمين المذكورين وعرض المرفقات
  /// @param message الرسالة المراد عرض محتواها
  /// @return واجهة عرض محتوى الرسالة مع تمييز المستخدمين المذكورين والمرفقات
  Widget _buildMessageContent(TaskMessage message) {
    final taskController = Get.find<TaskController>();
    final userController = Get.find<UserController>();

    // قائمة بالعناصر التي سيتم عرضها (النص والمرفقات)
    List<Widget> contentItems = [];

    // إضافة محتوى الرسالة النصي
    Widget textContent;

    // إذا لم تكن هناك إشارات لمستخدمين، نعرض النص العادي
    if (message.mentionedUserIds == null || message.mentionedUserIds!.isEmpty) {
      textContent = Text(
        message.content,
        style: AppStyles.bodyMedium,
      );
    } else {
      // تحليل النص للبحث عن إشارات المستخدمين
      final content = message.content;

      // قائمة بأجزاء النص والمستخدمين المذكورين
      List<InlineSpan> spans = [];

      // البحث عن الإشارات بتنسيق @اسم_المستخدم
      final regex = RegExp(r'@(\S+)');
      final matches = regex.allMatches(content);

      int lastIndex = 0;

      // إضافة أجزاء النص والإشارات
      for (final match in matches) {
        // إضافة النص قبل الإشارة
        if (match.start > lastIndex) {
          spans.add(TextSpan(
            text: content.substring(lastIndex, match.start),
            style: AppStyles.bodyMedium,
          ));
        }

        // الحصول على اسم المستخدم من النص
        final mentionText = content.substring(match.start, match.end);

        // إضافة الإشارة بتنسيق مميز مع تفاعل عند النقر
        spans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: InkWell(
              onTap: () {
                // عرض معلومات المستخدم عند النقر على الإشارة
                final mentionedUserId = message.mentionedUserIds!.firstWhere(
                  (id) => mentionText.contains(userController.users
                      .firstWhere(
                        (u) => u.id == id,
                        orElse: () => User(
                          id: id,
                          name: 'مستخدم',
                          email: '',
                          password: '',
                          role: UserRole.employee,
                          createdAt: DateTime.now(),
                        ),
                      )
                      .name),
                  orElse: () => '',
                );

                if (mentionedUserId.isNotEmpty) {
                  Get.snackbar(
                    'معلومات المستخدم',
                    'تم الإشارة إلى المستخدم في هذه الرسالة',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.blue.shade100,
                    colorText: Colors.blue.shade800,
                  );
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(25),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  mentionText,
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        );

        lastIndex = match.end;
      }

      // إضافة النص المتبقي بعد آخر إشارة
      if (lastIndex < content.length) {
        spans.add(TextSpan(
          text: content.substring(lastIndex),
          style: AppStyles.bodyMedium,
        ));
      }

      // عرض النص بالتنسيق المطلوب
      textContent = RichText(
        text: TextSpan(
          style: AppStyles.bodyMedium,
          children: spans,
        ),
      );
    }

    // إضافة محتوى النص إلى قائمة العناصر
    contentItems.add(textContent);

    // إضافة المرفقات إذا وجدت
    if (message.attachmentIds != null && message.attachmentIds!.isNotEmpty) {
      // البحث عن المرفقات في قائمة المرفقات
      final attachments = taskController.messageAttachments
          .where((attachment) => message.attachmentIds!.contains(attachment.id))
          .toList();

      if (attachments.isNotEmpty) {
        // إضافة فاصل بين النص والمرفقات
        contentItems.add(const SizedBox(height: 8));

        // إضافة المرفقات
        for (final attachment in attachments) {
          contentItems.add(
            _buildAttachmentItem(attachment),
          );

          // إضافة مسافة بين المرفقات
          if (attachment != attachments.last) {
            contentItems.add(const SizedBox(height: 4));
          }
        }
      }
    }

    // إرجاع عمود يحتوي على النص والمرفقات
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentItems,
    );
  }

  /// بناء عنصر المرفق
  /// @param attachment المرفق المراد عرضه
  /// @return واجهة عرض المرفق
  Widget _buildAttachmentItem(MessageAttachment attachment) {
    // تحديد نوع المرفق
    final isImage = attachment.fileType.startsWith('image/');

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: InkWell(
        onTap: () {
          // فتح المرفق عند النقر عليه
          _openAttachment(attachment);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              // أيقونة المرفق
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isImage ? Colors.transparent : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: isImage
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.network(
                          attachment.filePath,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            );
                          },
                        ),
                      )
                    : Icon(
                        _getFileIcon(attachment.fileName),
                        color: Colors.grey.shade700,
                      ),
              ),
              const SizedBox(width: 8),
              // معلومات المرفق
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      attachment.fileName,
                      style: AppStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _formatFileSize(attachment.fileSize),
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              // زر التنزيل
              IconButton(
                icon: const Icon(Icons.download),
                onPressed: () {
                  _downloadAttachment(attachment);
                },
                tooltip: 'تنزيل'.tr,
                iconSize: 20,
                color: AppColors.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// فتح المرفق
  /// @param attachment المرفق المراد فتحه
  void _openAttachment(MessageAttachment attachment) {
    // تحديد نوع المرفق
    final isImage = attachment.fileType.startsWith('image/');

    if (isImage) {
      // عرض الصورة في نافذة منبثقة
      Get.dialog(
        Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: Text(attachment.fileName),
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () {
                      _downloadAttachment(attachment);
                    },
                    tooltip: 'تنزيل'.tr,
                  ),
                ],
              ),
              Flexible(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: Image.network(
                    attachment.filePath,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'فشل تحميل الصورة'.tr,
                              style: AppStyles.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // فتح الملف في المتصفح
      if (attachment.filePath.isNotEmpty) {
        launchUrl(Uri.parse(attachment.filePath));
      } else {
        Get.snackbar(
          'خطأ'.tr,
          'لا يمكن فتح الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }

  /// تنزيل المرفق
  /// @param attachment المرفق المراد تنزيله
  void _downloadAttachment(MessageAttachment attachment) {
    // تنزيل الملف
    if (attachment.filePath.isNotEmpty) {
      launchUrl(Uri.parse(attachment.filePath));
    } else {
      Get.snackbar(
        'خطأ'.tr,
        'لا يمكن تنزيل الملف'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// الحصول على أيقونة الملف بناءً على نوع الملف
  /// @param fileName اسم الملف
  /// @return أيقونة الملف
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'mkv':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'aac':
      case 'flac':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// تنسيق حجم الملف
  /// @param bytes حجم الملف بالبايت
  /// @return حجم الملف بتنسيق مناسب
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // تم نقل هذه الوظيفة إلى FileProcessor

  String _getStatusText(TaskStatus status) {
    // استخدام الامتدادات للحصول على الاسم العربي
    return status.displayNameAr;
  }

  IconData _getStatusIcon(TaskStatus status) {
    // استخدام الامتدادات للحصول على الأيقونة
    return status.icon;
  }

  String _getPriorityText(TaskPriority priority) {
    // استخدام الامتدادات للحصول على الاسم العربي
    return priority.displayNameAr;
  }

  Color _getActivityColor(TaskActionType actionType) {
    switch (actionType) {
      case TaskActionType.created:
        return AppColors.success;
      case TaskActionType.assigned:
        return AppColors.info;
      case TaskActionType.statusChanged:
        return AppColors.warning;
      case TaskActionType.priorityChanged:
        return const Color(0xFF9C27B0); // Purple
      case TaskActionType.commented:
        return const Color(0xFF009688); // Teal
      case TaskActionType.fileAttached:
        return const Color(0xFF3F51B5); // Indigo
      case TaskActionType.fileRemoved:
        return AppColors.error;
      case TaskActionType.transferred:
        return const Color(0xFF673AB7); // Deep Purple
      case TaskActionType.infoRequested:
        return const Color(0xFFFFC107); // Amber
      case TaskActionType.infoProvided:
        return const Color(0xFF03A9F4); // Light Blue
      case TaskActionType.completed:
        return AppColors.success;
      case TaskActionType.cancelled:
        return AppColors.statusCancelled;
      case TaskActionType.progressUpdated:
        return const Color(0xFF00BCD4); // Cyan
      case TaskActionType.deleted:
        return const Color(0xFFD32F2F); // Red 700
      case TaskActionType.restored:
        return const Color(0xFF388E3C); // Green 700
      default:
        return AppColors
            .statusPending; // Default color for any new action types
    }
  }

  String _getActivityText(TaskHistory item) {
    switch (item.actionType) {
      case TaskActionType.created:
        return 'taskCreated'.tr;
      case TaskActionType.assigned:
        return 'taskAssigned'.tr;
      case TaskActionType.statusChanged:
        return '${'statusChanged'.tr} ${item.previousValue} ${'to'.tr} ${item.newValue}';
      case TaskActionType.priorityChanged:
        return '${'priorityChanged'.tr} ${item.previousValue} ${'to'.tr} ${item.newValue}';
      case TaskActionType.commented:
        return 'commentAdded'.tr;
      case TaskActionType.fileAttached:
        return 'fileAttached'.tr;
      case TaskActionType.fileRemoved:
        return 'fileRemoved'.tr;
      case TaskActionType.transferred:
        return 'taskTransferred'.tr;
      case TaskActionType.infoRequested:
        return 'infoRequested'.tr;
      case TaskActionType.infoProvided:
        return 'infoProvided'.tr;
      case TaskActionType.completed:
        return 'taskCompleted'.tr;
      case TaskActionType.cancelled:
        return 'taskCancelled'.tr;
      case TaskActionType.progressUpdated:
        return '${'progressUpdated'.tr} ${item.previousValue}% ${'to'.tr} ${item.newValue}%';
      case TaskActionType.deleted:
        return 'taskDeleted'.tr;
      case TaskActionType.restored:
        return 'taskRestored'.tr;
      default:
        return 'activityRecorded'.tr; // Default text for any new action types
    }
  }

  /// Builds the details tab content
  Widget _buildDetailsTab(Task task) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Task description
          Text(
            'taskDescription'.tr,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.border),
            ),
            width: double.infinity,
            child: Text(
              task.description.isNotEmpty
                  ? task.description
                  : 'noDescription'.tr,
              style: AppStyles.bodyMedium,
            ),
          ),
          const SizedBox(height: 24),

          // Task type (if available)
          if (task.taskTypeId != null)
            FutureBuilder<Widget>(
              future: _buildTaskTypeInfo(task.taskTypeId!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const SizedBox(
                    height: 50,
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                if (snapshot.hasData) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نوع المهمة',
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      snapshot.data!,
                      const SizedBox(height: 24),
                    ],
                  );
                }

                return const SizedBox.shrink();
              },
            ),

          // Task dates
          Text(
            'taskDates'.tr,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInfoRow(
                    'createdAt'.tr,
                    DateFormatter.formatDateTime(task.createdAt),
                    Icons.calendar_today,
                  ),
                  if (task.startDate != null)
                    _buildInfoRow(
                      'startDate'.tr,
                      DateFormatter.formatDateTime(task.startDate!),
                      Icons.play_circle_outline,
                    ),
                  if (task.dueDate != null)
                    _buildInfoRow(
                      'dueDate'.tr,
                      DateFormatter.formatDateTime(task.dueDate!),
                      Icons.event,
                      textColor: task.isOverdue() ? AppColors.error : null,
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // المهام الفرعية
          Text(
            'المهام الفرعية',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          FutureBuilder<int>(
            future: Get.find<TaskController>().getSubtasksCount(task.id),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final subtasksCount = snapshot.data ?? 0;
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.checklist,
                                color: AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'عدد المهام الفرعية: $subtasksCount',
                                style: AppStyles.titleSmall,
                              ),
                            ],
                          ),
                          ElevatedButton.icon(
                            onPressed: () {
                              // الانتقال إلى تبويب المهام الفرعية (التبويب رقم 2)
                              _tabController.animateTo(2);
                            },
                            icon: const Icon(Icons.visibility, size: 16),
                            label: const Text('عرض المهام الفرعية'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                          ),
                        ],
                      ),
                      if (subtasksCount > 0) ...[
                        const SizedBox(height: 16),
                        FutureBuilder<int>(
                          future: Get.find<TaskController>()
                              .getCompletedSubtasksCount(task.id),
                          builder: (context, completedSnapshot) {
                            final completedCount = completedSnapshot.data ?? 0;
                            final completionPercentage = subtasksCount > 0
                                ? (completedCount / subtasksCount) * 100
                                : 0.0;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'المهام المكتملة: $completedCount من $subtasksCount',
                                      style: AppStyles.bodyMedium,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: AppColors.primary.withAlpha(50),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${completionPercentage.toInt()}%',
                                        style: AppStyles.bodySmall.copyWith(
                                          color: AppColors.primary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                LinearProgressIndicator(
                                  value: completionPercentage / 100,
                                  backgroundColor: Colors.grey.shade200,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.primary),
                                  minHeight: 8,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),

          // Task progress
          Text(
            'progress'.tr,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${task.completionPercentage.toInt()}%',
                    style: AppStyles.headingLarge.copyWith(
                      color: AppColors.getTaskStatusColor(task.status.index),
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: task.completionPercentage / 100,
                    backgroundColor: Get.isDarkMode
                        ? const Color(0xFF424242)
                        : const Color(0xFFEEEEEE),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.getTaskStatusColor(task.status.index),
                    ),
                    minHeight: 10,
                  ),
                  const SizedBox(height: 16),
                  // تحقق من حجم الشاشة لتطبيق تصميم متجاوب للزر
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final isVerySmallScreen = constraints.maxWidth < 150;

                      return isVerySmallScreen
                          // استخدام زر بدون أيقونة للشاشات الصغيرة جدًا
                          ? SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () => _showProgressUpdateDialog(),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 8),
                                ),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text('updateProgress'.tr),
                                ),
                              ),
                            )
                          // استخدام زر مع أيقونة للشاشات الأكبر
                          : SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () => _showProgressUpdateDialog(),
                                icon: const Icon(Icons.update, size: 18),
                                label: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text('updateProgress'.tr),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 8),
                                ),
                              ),
                            );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على عدد المساهمات لمستخدم في مهمة
  Future<int> _getContributionCount(
      {required String taskId, required String userId}) async {
    try {
      // الحصول على متحكم المهام
      final taskController = Get.find<TaskController>();

      // الحصول على سجلات تتبع التقدم للمهمة
      final progressTrackers = taskController.progressTrackers;

      // حساب عدد المساهمات للمستخدم المحدد
      return progressTrackers
          .where((tracker) => tracker.userId == userId)
          .length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المساهمات: $e');
      return 0;
    }
  }

  /// Builds the contributors tab content
  Widget _buildContributorsTab(RxMap<String, double> userContributions) {
    // الحصول على متحكم المهام
    final taskController = Get.find<TaskController>();
    final task = taskController.currentTask.value;

    if (task == null) {
      return const Center(child: Text('لا يمكن تحميل بيانات المهمة'));
    }

    return Column(
      children: [
        // زر إدارة الوصول للمهمة
        // Padding(
        //   padding: const EdgeInsets.all(16.0),
        //   child: ElevatedButton.icon(
        //     onPressed: () {
        //       // فتح شاشة إدارة الوصول للمهمة
        //       Get.to(() => TaskAccessManager(
        //             taskId: task.id,
        //             taskTitle: task.title,
        //           ));
        //     },
        //     icon: const Icon(Icons.security),
        //     label: const Text('إدارة صلاحيات الوصول للمهمة'),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: AppColors.primary,
        //       foregroundColor: Colors.white,
        //       minimumSize: const Size(double.infinity, 48),
        //     ),
        //   ),
        // ),

        // عرض المساهمين
        Expanded(
          child: userContributions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.people_outline,
                        size: 64,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'noContributors'.tr,
                        style: AppStyles.titleMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                )
              : _buildContributorsList(userContributions),
        ),
      ],
    );
  }

  /// بناء قائمة المساهمين
  Widget _buildContributorsList(RxMap<String, double> userContributions) {
    // الحصول على متحكم المهام
    final taskController = Get.find<TaskController>();

    // Sort contributors by contribution percentage (descending)
    final sortedContributors = userContributions.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedContributors.length,
      itemBuilder: (context, index) {
        final entry = sortedContributors[index];
        final userId = entry.key;
        final contribution = entry.value;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: () {
              // عند النقر على المساهم، حدد المساهم وانتقل إلى تبويب تفاصيل المساهمات
              setState(() {
                _selectedContributorId = userId;
              });
              // انتقل إلى تبويب تفاصيل المساهمات (التبويب رقم 10)
              _tabController.animateTo(10);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: AppColors.primary,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            FutureBuilder<String>(
                              future: Get.find<UserController>()
                                  .getUserNameById(userId),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? userId,
                                  style: AppStyles.titleMedium,
                                );
                              },
                            ),
                            FutureBuilder<int>(
                              future: _getContributionCount(
                                  taskId: taskController.currentTask.value!.id,
                                  userId: userId),
                              builder: (context, snapshot) {
                                final contributionCount = snapshot.data ?? 0;
                                return Text(
                                  '${'contribution'.tr}: ${contribution.toInt()}% ($contributionCount ${'مساهمة'})',
                                  style: AppStyles.bodyMedium,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      // إضافة أيقونة للإشارة إلى إمكانية النقر
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: contribution / 100,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.primary,
                    ),
                    minHeight: 8,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds an info row for the details tab
  Widget _buildInfoRow(String label, String value, IconData icon,
      {Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$label:',
                  style: AppStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: AppStyles.bodyMedium.copyWith(
                    color: textColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات نوع المهمة
  Future<Widget> _buildTaskTypeInfo(String taskTypeId) async {
    try {
      // التحقق من وجود متحكم أنواع المهام أو إنشاؤه
      if (!Get.isRegistered<TaskTypeController>()) {
        Get.put(TaskTypeController());
      }

      final taskTypeController = Get.find<TaskTypeController>();

      // تحميل أنواع المهام إذا لم تكن محملة بالفعل
      if (taskTypeController.taskTypes.isEmpty) {
        await taskTypeController.loadTaskTypes();
      }

      // البحث عن نوع المهمة
      final taskType = taskTypeController.getTaskTypeById(taskTypeId);

      if (taskType != null) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // لون نوع المهمة (إذا كان متاحًا)
                if (taskType.color != null)
                  Container(
                    width: 24,
                    height: 24,
                    margin: const EdgeInsets.only(left: 16),
                    decoration: BoxDecoration(
                      color: _parseColor(taskType.color!),
                      shape: BoxShape.circle,
                    ),
                  ),

                // أيقونة نوع المهمة (إذا كانت متاحة)
                if (taskType.icon != null)
                  Icon(
                    Icons
                        .category, // استخدام أيقونة افتراضية، يمكن تحسينها لاحقًا
                    color: taskType.color != null
                        ? _parseColor(taskType.color!)
                        : AppColors.primary,
                    size: 24,
                  ),

                const SizedBox(width: 16),

                // معلومات نوع المهمة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        taskType.name,
                        style: AppStyles.titleSmall,
                      ),
                      if (taskType.description.isNotEmpty)
                        Text(
                          taskType.description,
                          style: AppStyles.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.category, color: Colors.grey),
                const SizedBox(width: 16),
                Text(
                  'نوع المهمة غير متاح',
                  style: AppStyles.bodyMedium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          ),
        );
      }
    } catch (e) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.error_outline, color: AppColors.error),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'حدث خطأ أثناء تحميل نوع المهمة: $e',
                  style: AppStyles.bodyMedium.copyWith(color: AppColors.error),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// تحويل قيمة اللون النصية إلى لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return AppColors.primary;
    } catch (e) {
      return AppColors.statusPending;
    }
  }

  /// Shows a dialog to update task progress
  void _showProgressUpdateDialog() {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();
    final task = taskController.currentTask.value;

    if (task == null) return;

    // Set initial value
    _progressController.text = task.completionPercentage.toInt().toString();

    Get.dialog(
      AlertDialog(
        title: Text('updateProgress'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('enterCompletionPercentage'.tr),
            const SizedBox(height: 16),
            TextField(
              controller: _progressController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'percentage'.tr,
                suffixText: '%',
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () async {
              // Validate input
              final percentageText = _progressController.text.trim();
              if (percentageText.isEmpty) return;

              int percentage;
              try {
                percentage = int.parse(percentageText);
                if (percentage < 0) percentage = 0;
                if (percentage > 100) percentage = 100;
              } catch (e) {
                Get.snackbar(
                  'error'.tr,
                  'invalidPercentage'.tr,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }

              // Close dialog
              Get.back();

              // Show loading
              Get.dialog(
                const Center(child: CircularProgressIndicator()),
                barrierDismissible: false,
              );

              // Update task progress
              try {
                final result = await taskController.updateTaskProgress(
                  task.id,
                  authController.currentUser.value!.id,
                  percentage.toDouble(),
                );

                // Close loading dialog
                Get.back();

                if (result) {
                  Get.snackbar(
                    'success'.tr,
                    'progressUpdatedSuccess'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green.shade100,
                    colorText: Colors.green.shade800,
                  );
                } else {
                  Get.snackbar(
                    'error'.tr,
                    taskController.error.value,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              } catch (e) {
                // Close loading dialog
                Get.back();

                Get.snackbar(
                  'error'.tr,
                  e.toString(),
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            child: Text('update'.tr),
          ),
        ],
      ),
    );
  }

  // El método _showTransferDialog ha sido reemplazado por el componente TaskTransferButton

  /// Shows a confirmation dialog to delete the task
  void _showDeleteConfirmation() {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();
    final task = taskController.currentTask.value;

    if (task == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('deleteTask'.tr),
        content: Text('deleteTaskConfirmation'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () async {
              // Close dialog
              Get.back();

              // Show loading
              Get.dialog(
                const Center(child: CircularProgressIndicator()),
                barrierDismissible: false,
              );

              // Delete task
              try {
                final result = await taskController.deleteTask(
                  task.id,
                  authController.currentUser.value!.id,
                );

                // Close loading dialog
                Get.back();

                if (result) {
                  // Navigate back to tasks screen
                  Get.back();

                  Get.snackbar(
                    'success'.tr,
                    'taskDeletedSuccess'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green.shade100,
                    colorText: Colors.green.shade800,
                  );
                } else {
                  Get.snackbar(
                    'error'.tr,
                    taskController.error.value,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              } catch (e) {
                // Close loading dialog
                Get.back();

                Get.snackbar(
                  'error'.tr,
                  e.toString(),
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text('delete'.tr),
          ),
        ],
      ),
    );
  }
}
