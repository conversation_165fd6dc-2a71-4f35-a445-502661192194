import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../models/report_model.dart';
import '../../models/report_schedule_model.dart';
import '../../database/report_schedule_repository.dart';
import '../../utils/responsive_helper.dart';

/// شاشة جدولة التقارير
///
/// تتيح للمستخدم إنشاء وإدارة جدولة التقارير
class ReportSchedulerScreen extends StatefulWidget {
  final String reportId;

  const ReportSchedulerScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ReportSchedulerScreen> createState() => _ReportSchedulerScreenState();
}

class _ReportSchedulerScreenState extends State<ReportSchedulerScreen> {
  final ReportRepository _reportRepository = ReportRepository();
  final ReportScheduleRepository _reportScheduleRepository = ReportScheduleRepository();
  final UserRepository _userRepository = UserRepository();
  final AuthController _authController = Get.find<AuthController>();
  final Uuid _uuid = const Uuid();

  Report? _report;
  List<ReportSchedule> _schedules = [];

  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل التقرير
      final report = await _reportRepository.getReportById(widget.reportId);
      if (report == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'التقرير غير موجود';
        });
        return;
      }

      // تحميل جدولات التقرير
      final schedules = await _reportScheduleRepository.getSchedulesForReport(widget.reportId);

      // تحميل المستخدمين
      await _userRepository.getAllUsers();

      setState(() {
        _report = report;
        _schedules = schedules;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// عرض مربع حوار إنشاء جدولة جديدة
  void _showCreateScheduleDialog() {
    final formKey = GlobalKey<FormState>();
    String title = '';
    String? description;
    ReportScheduleFrequency frequency = ReportScheduleFrequency.daily;
    List<int> daysOfWeek = [];
    int? dayOfMonth;
    List<int> monthsOfYear = [];
    int executionHour = 8;
    int executionMinute = 0;
    DateTime startDate = DateTime.now();
    DateTime? endDate;
    ReportFormat format = ReportFormat.pdf;
    ReportDeliveryMethod deliveryMethod = ReportDeliveryMethod.email;
    List<String> recipientEmails = [];
    List<String> recipientUserIds = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء جدولة جديدة'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'عنوان الجدولة',
                    hintText: 'أدخل عنوان الجدولة',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال عنوان الجدولة';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    title = value;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'وصف الجدولة (اختياري)',
                    hintText: 'أدخل وصف الجدولة',
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    description = value.isEmpty ? null : value;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ReportScheduleFrequency>(
                  decoration: const InputDecoration(
                    labelText: 'تكرار الجدولة',
                  ),
                  value: frequency,
                  items: ReportScheduleFrequency.values.map((freq) {
                    return DropdownMenuItem<ReportScheduleFrequency>(
                      value: freq,
                      child: Text(_getFrequencyName(freq)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      frequency = value;
                    }
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'ساعة التنفيذ',
                          hintText: '0-23',
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: executionHour.toString(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'مطلوب';
                          }
                          final hour = int.tryParse(value);
                          if (hour == null || hour < 0 || hour > 23) {
                            return 'ساعة غير صالحة';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          executionHour = int.tryParse(value) ?? 0;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'دقيقة التنفيذ',
                          hintText: '0-59',
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: executionMinute.toString(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'مطلوب';
                          }
                          final minute = int.tryParse(value);
                          if (minute == null || minute < 0 || minute > 59) {
                            return 'دقيقة غير صالحة';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          executionMinute = int.tryParse(value) ?? 0;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ReportFormat>(
                  decoration: const InputDecoration(
                    labelText: 'تنسيق التقرير',
                  ),
                  value: format,
                  items: ReportFormat.values.map((fmt) {
                    return DropdownMenuItem<ReportFormat>(
                      value: fmt,
                      child: Text(_getFormatName(fmt)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      format = value;
                    }
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ReportDeliveryMethod>(
                  decoration: const InputDecoration(
                    labelText: 'طريقة التوصيل',
                  ),
                  value: deliveryMethod,
                  items: ReportDeliveryMethod.values.map((method) {
                    return DropdownMenuItem<ReportDeliveryMethod>(
                      value: method,
                      child: Text(_getDeliveryMethodName(method)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      deliveryMethod = value;
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.of(context).pop();

                // إنشاء جدولة التقرير
                final schedule = ReportSchedule(
                  id: _uuid.v4(),
                  title: title,
                  description: description,
                  reportId: widget.reportId,
                  createdById: _authController.currentUser.value!.id,
                  createdAt: DateTime.now(),
                  frequency: frequency,
                  daysOfWeek: daysOfWeek.isEmpty ? null : daysOfWeek,
                  dayOfMonth: dayOfMonth,
                  monthsOfYear: monthsOfYear.isEmpty ? null : monthsOfYear,
                  executionHour: executionHour,
                  executionMinute: executionMinute,
                  startDate: startDate,
                  endDate: endDate,
                  format: format,
                  deliveryMethod: deliveryMethod,
                  recipientEmails: recipientEmails.isEmpty ? null : recipientEmails,
                  recipientUserIds: recipientUserIds.isEmpty ? null : recipientUserIds,
                  isActive: true,
                );

                // حساب موعد التنفيذ التالي
                final nextExecutionDate = await _reportScheduleRepository.calculateNextExecutionDate(schedule.id);

                // تحديث موعد التنفيذ التالي
                final updatedSchedule = schedule.copyWith(
                  nextExecutionDate: nextExecutionDate,
                );

                // حفظ الجدولة
                await _reportScheduleRepository.createReportSchedule(updatedSchedule);

                // تحديث القائمة
                _loadData();
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تفعيل/تعطيل الجدولة
  void _showToggleScheduleDialog(ReportSchedule schedule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(schedule.isActive ? 'تعطيل الجدولة' : 'تفعيل الجدولة'),
        content: Text(
          schedule.isActive
              ? 'هل أنت متأكد من تعطيل جدولة "${schedule.title}"؟'
              : 'هل أنت متأكد من تفعيل جدولة "${schedule.title}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store the message before async operation
              final message = schedule.isActive
                  ? 'تم تعطيل الجدولة بنجاح'
                  : 'تم تفعيل الجدولة بنجاح';

              // Store context reference before async operation
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              Navigator.of(context).pop();
              await _reportScheduleRepository.toggleScheduleActive(schedule.id, !schedule.isActive);
              _loadData();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(message),
                  ),
                );
              }
            },
            child: Text(schedule.isActive ? 'تعطيل' : 'تفعيل'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار حذف الجدولة
  void _showDeleteScheduleDialog(ReportSchedule schedule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الجدولة'),
        content: Text('هل أنت متأكد من حذف جدولة "${schedule.title}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context reference before async operation
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              Navigator.of(context).pop();
              await _reportScheduleRepository.deleteReportSchedule(schedule.id);
              _loadData();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الجدولة بنجاح'),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم تكرار الجدولة
  String _getFrequencyName(ReportScheduleFrequency frequency) {
    switch (frequency) {
      case ReportScheduleFrequency.daily:
        return 'يومي';
      case ReportScheduleFrequency.weekly:
        return 'أسبوعي';
      case ReportScheduleFrequency.monthly:
        return 'شهري';
      case ReportScheduleFrequency.quarterly:
        return 'ربع سنوي';
      case ReportScheduleFrequency.yearly:
        return 'سنوي';
      case ReportScheduleFrequency.custom:
        return 'مخصص';
    }
  }

  /// الحصول على اسم تنسيق التقرير
  String _getFormatName(ReportFormat format) {
    switch (format) {
      case ReportFormat.pdf:
        return 'PDF';
      case ReportFormat.excel:
        return 'Excel';
      case ReportFormat.csv:
        return 'CSV';
      case ReportFormat.json:
        return 'JSON';
      case ReportFormat.powerBI:
        return 'Power BI';
    }
  }

  /// الحصول على اسم طريقة التوصيل
  String _getDeliveryMethodName(ReportDeliveryMethod method) {
    switch (method) {
      case ReportDeliveryMethod.email:
        return 'بريد إلكتروني';
      case ReportDeliveryMethod.notification:
        return 'إشعار';
      case ReportDeliveryMethod.download:
        return 'تنزيل';
      case ReportDeliveryMethod.dashboard:
        return 'لوحة المعلومات';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_report?.title != null ? 'جدولة تقرير: ${_report!.title}' : 'جدولة التقرير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _report == null
                  ? const Center(child: Text('التقرير غير موجود'))
                  : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateScheduleDialog,
        tooltip: 'إنشاء جدولة جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent() {
    if (_schedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.schedule,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد جدولات لهذا التقرير',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'انقر على زر "+" لإنشاء جدولة جديدة',
              style: AppStyles.bodyMedium,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ResponsiveHelper.isDesktop(context) || ResponsiveHelper.isTablet(context)
          ? _buildGridView()
          : _buildListView(),
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _schedules.length,
      itemBuilder: (context, index) {
        final schedule = _schedules[index];
        return _buildScheduleCard(schedule);
      },
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _schedules.length,
      itemBuilder: (context, index) {
        final schedule = _schedules[index];
        return _buildScheduleListItem(schedule);
      },
    );
  }

  Widget _buildScheduleCard(ReportSchedule schedule) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    schedule.title,
                    style: AppStyles.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Switch(
                  value: schedule.isActive,
                  onChanged: (value) {
                    _showToggleScheduleDialog(schedule);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (schedule.description != null) ...[
              Text(
                schedule.description!,
                style: AppStyles.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
            ],
            const Spacer(),
            Row(
              children: [
                Icon(
                  Icons.repeat,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _getFrequencyName(schedule.frequency),
                  style: AppStyles.captionMedium,
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${schedule.executionHour.toString().padLeft(2, '0')}:${schedule.executionMinute.toString().padLeft(2, '0')}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'التنفيذ القادم: ${schedule.nextExecutionDate != null ? DateFormat('yyyy-MM-dd').format(schedule.nextExecutionDate!) : 'غير محدد'}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.delete, size: 20),
                  tooltip: 'حذف',
                  onPressed: () => _showDeleteScheduleDialog(schedule),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleListItem(ReportSchedule schedule) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(schedule.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (schedule.description != null) ...[
              Text(
                schedule.description!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
            ],
            Text(
              '${_getFrequencyName(schedule.frequency)} - ${schedule.executionHour.toString().padLeft(2, '0')}:${schedule.executionMinute.toString().padLeft(2, '0')}',
              style: AppStyles.captionSmall,
            ),
            Text(
              'التنفيذ القادم: ${schedule.nextExecutionDate != null ? DateFormat('yyyy-MM-dd').format(schedule.nextExecutionDate!) : 'غير محدد'}',
              style: AppStyles.captionSmall,
            ),
          ],
        ),
        leading: CircleAvatar(
          backgroundColor: schedule.isActive ? Colors.green.withAlpha(51) : Colors.grey.withAlpha(51), // 0.2 * 255 = ~51
          child: Icon(
            Icons.schedule,
            color: schedule.isActive ? Colors.green : Colors.grey,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: schedule.isActive,
              onChanged: (value) {
                _showToggleScheduleDialog(schedule);
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف',
              onPressed: () => _showDeleteScheduleDialog(schedule),
            ),
          ],
        ),
      ),
    );
  }
}
