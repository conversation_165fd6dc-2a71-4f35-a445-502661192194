import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../../utils/app_config.dart';
import '../../models/auth_models.dart';
import '../storage_service.dart';

/// خدمة API الأساسية للتعامل مع ASP.NET Core API
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final StorageService _storageService = Get.find<StorageService>();
  
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiresAt;

  /// الحصول على رمز الوصول الحالي
  String? get accessToken => _accessToken;

  /// تهيئة الخدمة وتحميل الرموز المحفوظة
  Future<void> initialize() async {
    await _loadTokensFromStorage();
  }

  /// تحميل الرموز من التخزين المحلي
  Future<void> _loadTokensFromStorage() async {
    try {
      final sessionData = await _storageService.getSessionData();
      if (sessionData != null && !sessionData.isExpired) {
        _accessToken = sessionData.accessToken;
        _refreshToken = sessionData.refreshToken;
        _tokenExpiresAt = sessionData.expiresAt;
        debugPrint('تم تحميل الرموز من التخزين المحلي');
      } else {
        debugPrint('لا توجد جلسة صالحة في التخزين المحلي');
        await clearTokens();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الرموز: $e');
      await clearTokens();
    }
  }

  /// حفظ الرموز في التخزين المحلي
  Future<void> _saveTokensToStorage(AuthResponse authResponse) async {
    if (authResponse.isValid) {
      _accessToken = authResponse.accessToken;
      _refreshToken = authResponse.refreshToken;
      _tokenExpiresAt = authResponse.expiresAt;

      final sessionData = SessionData(
        accessToken: authResponse.accessToken!,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt!,
        user: authResponse.user!,
        savedAt: DateTime.now(),
      );

      await _storageService.saveSessionData(sessionData);
      debugPrint('تم حفظ الرموز في التخزين المحلي');
    }
  }

  /// مسح الرموز
  Future<void> clearTokens() async {
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiresAt = null;
    await _storageService.clearSessionData();
    debugPrint('تم مسح الرموز');
  }

  /// التحقق من صحة الرمز وتحديثه إذا لزم الأمر
  Future<bool> _ensureValidToken() async {
    if (_accessToken == null) {
      debugPrint('لا يوجد رمز وصول');
      return false;
    }

    // التحقق من انتهاء صلاحية الرمز
    if (_tokenExpiresAt != null && DateTime.now().isAfter(_tokenExpiresAt!)) {
      debugPrint('انتهت صلاحية الرمز، محاولة التحديث...');
      return await _refreshAccessToken();
    }

    // التحقق من الحاجة لتحديث الرمز (قبل انتهاء الصلاحية بـ 5 دقائق)
    if (_tokenExpiresAt != null) {
      final refreshTime = _tokenExpiresAt!.subtract(const Duration(minutes: 5));
      if (DateTime.now().isAfter(refreshTime)) {
        debugPrint('الرمز يحتاج للتحديث قريباً، محاولة التحديث...');
        await _refreshAccessToken(); // لا نرجع false إذا فشل التحديث
      }
    }

    return true;
  }

  /// تحديث رمز الوصول
  Future<bool> _refreshAccessToken() async {
    if (_refreshToken == null) {
      debugPrint('لا يوجد رمز تحديث');
      return false;
    }

    try {
      final request = RefreshTokenRequest(refreshToken: _refreshToken!);
      final response = await _makeRequest(
        'POST',
        '/auth/refresh-token',
        body: request.toJson(),
        requireAuth: false, // لا نحتاج مصادقة لتحديث الرمز
      );

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(jsonDecode(response.body));
        if (authResponse.isValid) {
          await _saveTokensToStorage(authResponse);
          debugPrint('تم تحديث الرمز بنجاح');
          return true;
        }
      }

      debugPrint('فشل في تحديث الرمز: ${response.statusCode}');
      await clearTokens();
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث الرمز: $e');
      await clearTokens();
      return false;
    }
  }

  /// إنشاء طلب HTTP
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    bool requireAuth = true,
  }) async {
    // التحقق من الرمز إذا كان مطلوباً
    if (requireAuth && !await _ensureValidToken()) {
      throw ApiException('غير مصرح - يرجى تسجيل الدخول مرة أخرى', 401);
    }

    final uri = Uri.parse('${AppConfig.apiUrl}$endpoint');
    
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...?headers,
    };

    // إضافة رمز المصادقة إذا كان متوفراً ومطلوباً
    if (requireAuth && _accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $_accessToken';
    }

    http.Response response;
    final bodyJson = body != null ? jsonEncode(body) : null;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'PUT':
          response = await http.put(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw ApiException('طريقة HTTP غير مدعومة: $method', 400);
      }

      debugPrint('API Request: $method $endpoint - Status: ${response.statusCode}');
      
      return response;
    } on SocketException {
      throw ApiException('لا يمكن الاتصال بالخادم. تحقق من اتصال الإنترنت.', 0);
    } on HttpException catch (e) {
      throw ApiException('خطأ في الشبكة: ${e.message}', 0);
    } catch (e) {
      throw ApiException('خطأ غير متوقع: $e', 0);
    }
  }

  /// طلب GET
  Future<http.Response> get(String endpoint, {Map<String, String>? headers}) {
    return _makeRequest('GET', endpoint, headers: headers);
  }

  /// طلب POST
  Future<http.Response> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    bool requireAuth = true,
  }) {
    return _makeRequest('POST', endpoint, 
        body: body, headers: headers, requireAuth: requireAuth);
  }

  /// طلب PUT
  Future<http.Response> put(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) {
    return _makeRequest('PUT', endpoint, body: body, headers: headers);
  }

  /// طلب DELETE
  Future<http.Response> delete(String endpoint, {Map<String, String>? headers}) {
    return _makeRequest('DELETE', endpoint, headers: headers);
  }

  /// معالجة استجابة API وإرجاع البيانات أو رمي استثناء
  T handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return fromJson(data);
      } catch (e) {
        throw ApiException('خطأ في تحليل البيانات: $e', response.statusCode);
      }
    } else {
      String errorMessage = 'خطأ في الخادم';
      try {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = errorData['message'] ?? errorMessage;
      } catch (e) {
        // تجاهل خطأ تحليل رسالة الخطأ
      }
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// معالجة استجابة قائمة من API
  List<T> handleListResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final data = jsonDecode(response.body) as List<dynamic>;
        return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
      } catch (e) {
        throw ApiException('خطأ في تحليل البيانات: $e', response.statusCode);
      }
    } else {
      String errorMessage = 'خطأ في الخادم';
      try {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = errorData['message'] ?? errorMessage;
      } catch (e) {
        // تجاهل خطأ تحليل رسالة الخطأ
      }
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// حفظ بيانات المصادقة من AuthResponse
  Future<void> saveAuthResponse(AuthResponse authResponse) async {
    await _saveTokensToStorage(authResponse);
  }

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _accessToken != null && 
      (_tokenExpiresAt == null || DateTime.now().isBefore(_tokenExpiresAt!));
}

/// استثناء API
class ApiException implements Exception {
  final String message;
  final int statusCode;

  const ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';

  /// التحقق من كون الخطأ خطأ مصادقة
  bool get isAuthError => statusCode == 401 || statusCode == 403;

  /// التحقق من كون الخطأ خطأ شبكة
  bool get isNetworkError => statusCode == 0;
}
