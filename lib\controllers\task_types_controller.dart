import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_type_models.dart';
import '../services/api/task_types_api_service.dart';

/// متحكم أنواع المهام
class TaskTypesController extends GetxController {
  final TaskTypesApiService _apiService = TaskTypesApiService();

  // قوائم أنواع المهام
  final RxList<TaskType> _allTypes = <TaskType>[].obs;
  final RxList<TaskType> _filteredTypes = <TaskType>[].obs;

  // نوع المهمة الحالي
  final Rx<TaskType?> _currentType = Rx<TaskType?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskType> get allTypes => _allTypes;
  List<TaskType> get filteredTypes => _filteredTypes;
  TaskType? get currentType => _currentType.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTypes();
  }

  /// تحميل جميع أنواع المهام
  Future<void> loadAllTypes() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final types = await _apiService.getAllTypes();
      _allTypes.assignAll(types);
      _applyFilters();
      debugPrint('تم تحميل ${types.length} نوع مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أنواع المهام: $e';
      debugPrint('خطأ في تحميل أنواع المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على نوع مهمة بالمعرف
  Future<void> getTypeById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final type = await _apiService.getTypeById(id);
      _currentType.value = type;
      debugPrint('تم تحميل نوع المهمة: ${type.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل نوع المهمة: $e';
      debugPrint('خطأ في تحميل نوع المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<bool> createType(TaskType type) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newType = await _apiService.createType(type);
      _allTypes.add(newType);
      _applyFilters();
      debugPrint('تم إنشاء نوع مهمة جديد: ${newType.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء نوع المهمة: $e';
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث نوع مهمة
  Future<bool> updateType(int id, TaskType type) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateType(id, type);
      final index = _allTypes.indexWhere((t) => t.id == id);
      if (index != -1) {
        _allTypes[index] = type;
        _applyFilters();
      }
      debugPrint('تم تحديث نوع المهمة: ${type.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث نوع المهمة: $e';
      debugPrint('خطأ في تحديث نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteType(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteType(id);
      _allTypes.removeWhere((t) => t.id == id);
      _applyFilters();
      debugPrint('تم حذف نوع المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف نوع المهمة: $e';
      debugPrint('خطأ في حذف نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأنواع النشطة فقط
  List<TaskType> get activeTypes {
    return _allTypes.where((type) => type.isActive).toList();
  }

  /// الحصول على نوع افتراضي
  TaskType? get defaultType {
    return _allTypes.firstWhereOrNull((type) => type.isDefault);
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allTypes.where((type) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!type.name.toLowerCase().contains(query) &&
            !type.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !type.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredTypes.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllTypes();
  }
}
