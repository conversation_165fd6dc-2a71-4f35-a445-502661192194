@echo off
echo ========================================
echo    تشغيل خادم ASP.NET Core API
echo ========================================
echo.

cd /d "%~dp0webApi\webApi"

echo التحقق من وجود ملفات المشروع...
if not exist "webApi.csproj" (
    echo خطأ: لم يتم العثور على ملف webApi.csproj
    echo تأكد من أن المسار صحيح
    pause
    exit /b 1
)

echo بدء تشغيل خادم ASP.NET Core API...
echo المنفذ: http://localhost:5175
echo المنفذ الآمن: https://localhost:7111
echo Swagger UI: http://localhost:5175/swagger
echo.

dotnet run --urls "http://localhost:5175;https://localhost:7111"

pause
