using System.ComponentModel.DataAnnotations;

namespace webApi.Models.Auth;

/// <summary>
/// نموذج طلب تسجيل الدخول
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// اسم المستخدم أو البريد الإلكتروني
    /// </summary>
    [Required(ErrorMessage = "اسم المستخدم أو البريد الإلكتروني مطلوب")]
    public string UsernameOrEmail { get; set; } = string.Empty;

    /// <summary>
    /// كلمة المرور
    /// </summary>
    [Required(ErrorMessage = "كلمة المرور مطلوبة")]
    [MinLength(6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// تذكرني - للحفاظ على الجلسة لفترة أطول
    /// </summary>
    public bool RememberMe { get; set; } = false;
}
