import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/app_styles.dart';
import '../controllers/user_controller.dart';

/// مكون عرض تاريخ تحويلات المهمة
/// يعرض تاريخ تحويلات المهمة بشكل مرئي مع تفاصيل كل تحويل
class TaskTransferHistoryWidget extends StatelessWidget {
  final List<Map<String, dynamic>> transferHistory;
  final bool showTitle;
  final bool isCompact;

  const TaskTransferHistoryWidget({
    super.key,
    required this.transferHistory,
    this.showTitle = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    if (transferHistory.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              if (showTitle) ...[
                Row(
                  children: [
                    const Icon(Icons.history, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'تاريخ التحويلات',
                      style: AppStyles.headingMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              const Center(
                child: Text(
                  'لا توجد تحويلات لهذه المهمة',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Row(
                children: [
                  const Icon(Icons.history, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تاريخ التحويلات',
                    style: AppStyles.headingMedium,
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            SizedBox(
              height: isCompact ? 200 : 400,
              child: isCompact 
                  ? _buildCompactTransferHistory()
                  : _buildDetailedTransferHistory(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عرض مفصل لتاريخ التحويلات
  Widget _buildDetailedTransferHistory() {
    // ترتيب التحويلات من الأحدث إلى الأقدم
    final sortedHistory = List<Map<String, dynamic>>.from(transferHistory)
      ..sort((a, b) => (b['timestamp'] as DateTime).compareTo(a['timestamp'] as DateTime));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedHistory.length,
      itemBuilder: (context, index) {
        final transfer = sortedHistory[index];
        final isLast = index == sortedHistory.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // خط الزمن
            Column(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 60,
                    color: Colors.grey[300],
                  ),
              ],
            ),
            const SizedBox(width: 16),
            
            // محتوى التحويل
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات التحويل
                    Row(
                      children: [
                        const Icon(Icons.person, size: 16, color: AppColors.primary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            'من: ${transfer['fromUserName'] ?? 'غير معروف'}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    FutureBuilder<String>(
                      future: _getUserName(transfer['userId']?.toString() ?? ''),
                      builder: (context, snapshot) {
                        return Text(
                          'إلى: ${snapshot.data ?? 'مستخدم غير معروف'}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    
                    // التاريخ والوقت
                    Row(
                      children: [
                        const Icon(Icons.access_time, size: 14, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          _formatDateTime(transfer['timestamp'] as DateTime),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    
                    // التعليق إذا كان موجوداً
                    if (transfer['comment']?.toString().isNotEmpty == true) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          transfer['comment'].toString(),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                    
                    // المرفقات إذا كانت موجودة
                    if (transfer['attachments'] != null && 
                        (transfer['attachments'] as List).isNotEmpty) ...[
                      const SizedBox(height: 8),
                      _buildAttachmentsList(transfer['attachments'] as List),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء قائمة المرفقات
  Widget _buildAttachmentsList(List attachmentIds) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (var attachmentId in attachmentIds)
          Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.attachment, size: 14, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  'مرفق ${attachmentId.toString()}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء عرض مختصر لتاريخ التحويلات
  Widget _buildCompactTransferHistory() {
    // ترتيب التحويلات من الأحدث إلى الأقدم
    final sortedHistory = List<Map<String, dynamic>>.from(transferHistory)
      ..sort((a, b) => (b['timestamp'] as DateTime).compareTo(a['timestamp'] as DateTime));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedHistory.length,
      itemBuilder: (context, index) {
        final transfer = sortedHistory[index];

        return ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppColors.primary,
            child: Icon(Icons.send, color: Colors.white, size: 16),
          ),
          title: Row(
            children: [
              FutureBuilder<String>(
                future: _getUserName(transfer['userId']?.toString() ?? ''),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data ?? 'مستخدم غير معروف',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
              const Text(' ← '),
              FutureBuilder<String>(
                future: _getUserName(transfer['details']?['newAssigneeId']?.toString() ?? ''),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data ?? 'مستخدم غير معروف',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
            ],
          ),
          subtitle: Text(
            _formatDateTime(transfer['timestamp'] as DateTime),
            style: const TextStyle(fontSize: 12),
          ),
          trailing: transfer['comment']?.toString().isNotEmpty == true
              ? const Icon(Icons.comment, size: 16, color: Colors.grey)
              : null,
        );
      },
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على اسم المستخدم
  Future<String> _getUserName(String userId) async {
    try {
      final userController = Get.find<UserController>();
      final user = userController.users.firstWhere(
        (u) => u.id.toString() == userId,
        orElse: () => throw Exception('User not found'),
      );
      return user.name;
    } catch (e) {
      return 'مستخدم غير معروف';
    }
  }
}
