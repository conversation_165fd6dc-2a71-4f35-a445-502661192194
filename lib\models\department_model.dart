import 'user_model.dart';

/// نموذج القسم المتوافق مع ASP.NET Core API
class Department {
  final int id;
  final String name;
  final String? description;
  final int? managerId;
  final bool isActive;
  final int createdAt; // Unix timestamp

  // Navigation properties
  final User? manager;

  const Department({
    required this.id,
    required this.name,
    this.description,
    this.managerId,
    this.isActive = true,
    required this.createdAt,
    this.manager,
  });

  factory Department.fromJson(Map<String, dynamic> json) {
    return Department(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      managerId: json['managerId'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      manager: json['manager'] != null 
          ? User.fromJson(json['manager'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'managerId': managerId,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  Department copyWith({
    int? id,
    String? name,
    String? description,
    int? managerId,
    bool? isActive,
    int? createdAt,
    User? manager,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      manager: manager ?? this.manager,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'Department(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Department && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء قسم جديد
class CreateDepartmentRequest {
  final String name;
  final String? description;
  final int? managerId;
  final bool isActive;

  const CreateDepartmentRequest({
    required this.name,
    this.description,
    this.managerId,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'managerId': managerId,
      'isActive': isActive,
    };
  }
}

/// نموذج طلب تحديث قسم
class UpdateDepartmentRequest {
  final String? name;
  final String? description;
  final int? managerId;
  final bool? isActive;

  const UpdateDepartmentRequest({
    this.name,
    this.description,
    this.managerId,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (name != null) json['name'] = name;
    if (description != null) json['description'] = description;
    if (managerId != null) json['managerId'] = managerId;
    if (isActive != null) json['isActive'] = isActive;
    return json;
  }
}

/// إحصائيات القسم
class DepartmentStats {
  final int departmentId;
  final String departmentName;
  final int totalEmployees;
  final int activeEmployees;
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final int overdueTasks;

  const DepartmentStats({
    required this.departmentId,
    required this.departmentName,
    required this.totalEmployees,
    required this.activeEmployees,
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.overdueTasks,
  });

  factory DepartmentStats.fromJson(Map<String, dynamic> json) {
    return DepartmentStats(
      departmentId: json['departmentId'] as int,
      departmentName: json['departmentName'] as String,
      totalEmployees: json['totalEmployees'] as int,
      activeEmployees: json['activeEmployees'] as int,
      totalTasks: json['totalTasks'] as int,
      completedTasks: json['completedTasks'] as int,
      pendingTasks: json['pendingTasks'] as int,
      overdueTasks: json['overdueTasks'] as int,
    );
  }

  /// نسبة إنجاز المهام
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// نسبة المهام المتأخرة
  double get overdueRate {
    if (totalTasks == 0) return 0.0;
    return (overdueTasks / totalTasks) * 100;
  }

  /// نسبة الموظفين النشطين
  double get activeEmployeeRate {
    if (totalEmployees == 0) return 0.0;
    return (activeEmployees / totalEmployees) * 100;
  }
}
