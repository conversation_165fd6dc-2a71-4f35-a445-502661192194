//===================================================================================
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';

import 'package:flutter_application_2/routes/app_routes.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';


import '../../services/chart_export_service.dart';
import '../../widgets/charts/enhanced_pie_chart.dart';
import '../../widgets/charts/enhanced_radar_chart.dart';
import '../../widgets/charts/unified_filter_export_widget.dart';
import '../../widgets/charts/enhanced_bar_chart.dart';
import '../../widgets/charts/enhanced_line_chart.dart';
import '../../widgets/charts/enhanced_bubble_chart.dart';
import '../../widgets/charts/enhanced_heatmap_chart.dart';
import '../../widgets/charts/enhanced_treemap_chart.dart';
import '../../widgets/charts/enhanced_funnel_chart.dart';
import '../../widgets/charts/enhanced_sankey_chart.dart';
import '../../widgets/charts/gantt_chart.dart';
import '../../widgets/charts/user_tasks_status_chart.dart';
import '../../widgets/charts/draggable_dashboard_layout.dart' as draggable;
import '../../widgets/charts/chart_customization_panel.dart';
import '../../widgets/charts/resizable_chart_container.dart';
import '../../widgets/dashboard/monday_style_add_widget_dialog.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';

/// نموذج مهمة لمخطط Gantt
class CustomGanttTask {
  /// معرف المهمة
  final String id;

  /// عنوان المهمة
  final String title;

  /// تاريخ بداية المهمة
  final DateTime startDate;

  /// تاريخ نهاية المهمة
  final DateTime endDate;

  /// نسبة إكمال المهمة (0-100)
  final double completionPercentage;

  /// لون المهمة (اختياري)
  final Color? color;

  /// إنشاء مهمة لمخطط Gantt
  const CustomGanttTask({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.completionPercentage = 0,
    this.color,
  });

  /// تحويل إلى GanttTask من مكتبة gantt_chart.dart
  GanttTask toGanttTask() {
    return GanttTask(
      id: id,
      title: title,
      startDate: startDate,
      endDate: endDate,
      completionPercentage: completionPercentage,
      color: color,
    );
  }
}

/// رسام الشبكة المستخدم في وضع التحرير
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color color;

  GridPainter({required this.gridSize, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 0.5;

    // رسم الخطوط الأفقية
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // رسم الخطوط الرأسية
    for (double x = 0; x < size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // رسم نقاط التقاطع بشكل أكثر وضوحاً في وضع التحرير
    final dotPaint = Paint()
      ..color = Colors.grey.shade500
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    for (double x = 0; x < size.width; x += gridSize) {
      for (double y = 0; y < size.height; y += gridSize) {
        canvas.drawCircle(Offset(x, y), 1.5, dotPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant GridPainter oldDelegate) {
    return oldDelegate.gridSize != gridSize || oldDelegate.color != color;
  }
}

/// مكون التقويم المصغر
class MiniCalendarWidget extends StatelessWidget {
  final DateTime initialDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime) onDateSelected;

  const MiniCalendarWidget({
    super.key,
    required this.initialDate,
    this.startDate,
    this.endDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCalendarHeader(),
            const SizedBox(height: 8),
            _buildWeekdaysRow(),
            const SizedBox(height: 8),
            _buildCalendarGrid(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس التقويم مع أزرار التنقل
  Widget _buildCalendarHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          icon: const Icon(Icons.chevron_left),
          onPressed: () {
            // منطق التنقل إلى الشهر السابق
            final previousMonth =
                DateTime(initialDate.year, initialDate.month - 1, 1);
            onDateSelected(previousMonth);
          },
        ),
        Text(
          DateFormat.yMMMM('ar').format(initialDate),
          style: AppStyles.titleMedium,
        ),
        IconButton(
          icon: const Icon(Icons.chevron_right),
          onPressed: () {
            // منطق التنقل إلى الشهر التالي
            final nextMonth =
                DateTime(initialDate.year, initialDate.month + 1, 1);
            onDateSelected(nextMonth);
          },
        ),
      ],
    );
  }

  /// بناء صف أيام الأسبوع
  Widget _buildWeekdaysRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت']
          .map((day) => Expanded(
                child: Text(
                  day,
                  style: AppStyles.caption,
                  textAlign: TextAlign.center,
                ),
              ))
          .toList(),
    );
  }

  /// بناء شبكة التقويم
  Widget _buildCalendarGrid() {
    // حساب عدد أيام الشهر
    final daysInMonth =
        DateTime(initialDate.year, initialDate.month + 1, 0).day;

    // حساب اليوم الأول من الشهر
    final firstDayOfMonth = DateTime(initialDate.year, initialDate.month, 1);
    final firstWeekdayOfMonth =
        firstDayOfMonth.weekday % 7; // تعديل لجعل الأحد هو اليوم 0

    // إجمالي عدد الخلايا المطلوبة (الأيام السابقة + أيام الشهر)
    final totalCells = firstWeekdayOfMonth + daysInMonth;

    // عدد الصفوف المطلوبة
    final rowCount = (totalCells / 7).ceil();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: rowCount * 7, // عدد الخلايا في الشبكة
      itemBuilder: (context, index) {
        // حساب اليوم المقابل للخلية
        final adjustedIndex = index - firstWeekdayOfMonth;

        // التحقق مما إذا كانت الخلية خارج نطاق الشهر الحالي
        if (adjustedIndex < 0 || adjustedIndex >= daysInMonth) {
          return const SizedBox.shrink();
        }

        final day = adjustedIndex + 1;
        final currentDate = DateTime(initialDate.year, initialDate.month, day);

        // التحقق مما إذا كان اليوم ضمن النطاق المحدد
        final inRange =
            (startDate == null || !currentDate.isBefore(startDate!)) &&
                (endDate == null || !currentDate.isAfter(endDate!));

        final isToday = currentDate.year == DateTime.now().year &&
            currentDate.month == DateTime.now().month &&
            currentDate.day == DateTime.now().day;

        final isSelected = currentDate.day == initialDate.day;

        return InkWell(
          onTap: inRange ? () => onDateSelected(currentDate) : null,
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected
                  ? AppColors.primary
                  : (isToday
                      ? AppColors.primary.withValues(
                          red: AppColors.primary.r.toDouble(),
                          green: AppColors.primary.g.toDouble(),
                          blue: AppColors.primary.b.toDouble(),
                          alpha: 51.0)
                      : null), // 0.2 opacity = 51/255
            ),
            child: Text(
              day.toString(),
              style: TextStyle(
                fontSize: 12,
                color: isSelected
                    ? Colors.white
                    : inRange
                        ? AppColors.textPrimary
                        : AppColors.textSecondary,
                fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      },
    );
  }
}

// تم نقل نموذج GanttTask إلى ملف gantt_chart.dart

/// مكون مخطط جانت المحسن
class EnhancedGanttChart extends StatelessWidget {
  final List<GanttTask> tasks;
  final DateTime startDate;
  final DateTime endDate;
  final String title;

  const EnhancedGanttChart({
    super.key,
    required this.tasks,
    required this.startDate,
    required this.endDate,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // حساب إجمالي عدد الأيام في النطاق
    final totalDays = endDate.difference(startDate).inDays + 1;

    // تحديد ارتفاع كل مهمة
    final taskHeight = 40.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط
        if (title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              title,
              style: AppStyles.titleMedium,
            ),
          ),

        // شريط التاريخ
        _buildDateHeader(totalDays),

        // قائمة المهام
        Expanded(
          child: _buildTasksList(totalDays, taskHeight),
        ),
      ],
    );
  }

  /// بناء شريط التاريخ
  Widget _buildDateHeader(int totalDays) {
    return SizedBox(
      height: 30,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: totalDays,
        itemBuilder: (context, index) {
          final date = startDate.add(Duration(days: index));
          final isWeekend =
              date.weekday == 5 || date.weekday == 6; // الجمعة والسبت

          return Container(
            width: 60,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              color: isWeekend ? Colors.grey.shade200 : Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  DateFormat('d').format(date),
                  style: const TextStyle(fontSize: 12),
                ),
                Text(
                  DateFormat('MMM', 'ar').format(date),
                  style: const TextStyle(fontSize: 10),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء قائمة المهام
  Widget _buildTasksList(int totalDays, double taskHeight) {
    return ListView.builder(
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];

        // حساب موضع وعرض شريط المهمة
        final taskStartDiff = task.startDate.difference(startDate).inDays;
        final taskDuration = task.endDate.difference(task.startDate).inDays + 1;

        // التأكد من أن المهمة ضمن النطاق المعروض
        if (taskStartDiff + taskDuration < 0 || taskStartDiff > totalDays) {
          return const SizedBox.shrink();
        }

        // تعديل الموضع والعرض للمهام التي تبدأ قبل أو تنتهي بعد النطاق
        final adjustedStart = taskStartDiff < 0 ? 0 : taskStartDiff;
        final adjustedWidth = taskStartDiff < 0
            ? taskDuration + taskStartDiff
            : (taskStartDiff + taskDuration > totalDays
                ? totalDays - taskStartDiff
                : taskDuration);

        return _buildTaskItem(task, adjustedStart, adjustedWidth, taskHeight);
      },
    );
  }

  /// بناء عنصر المهمة
  Widget _buildTaskItem(
      GanttTask task, int adjustedStart, int adjustedWidth, double taskHeight) {
    return Container(
      height: taskHeight,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Stack(
        children: [
          // اسم المهمة
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            width: 150,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              alignment: Alignment.centerRight,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                color: Colors.grey.shade100,
              ),
              child: Text(
                task.title,
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // شريط المهمة
          Positioned(
            left: 150 + (adjustedStart * 60),
            top: 0,
            height: taskHeight,
            width: adjustedWidth * 60,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: task.color ?? AppColors.primary,
              ),
              child: Center(
                child: Text(
                  task.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),

          // مؤشر نسبة الإنجاز
          if (task.completionPercentage > 0)
            Positioned(
              left: 150 + (adjustedStart * 60),
              top: taskHeight - 5,
              height: 3,
              width: adjustedWidth * 60 * (task.completionPercentage / 100),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Colors.white.withValues(
                      red: Colors.white.r.toDouble(),
                      green: Colors.white.g.toDouble(),
                      blue: Colors.white.b.toDouble(),
                      alpha: 178.5), // 0.7 opacity = 178.5/255
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// لوحة التحكم الرئيسية
class DashboardTab extends StatefulWidget {
  const DashboardTab({super.key});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab> with RouteAware {
  // المتحكمات
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  late final DashboardController _dashboardController;
  late final ChartExportService _chartExportService;
  late final RouteObserver<PageRoute<dynamic>> _routeObserver;
  // تم التعليق على هذا المتغير لأنه غير مستخدم حاليًا
  // late final DatabaseHelper _databaseHelper;
  late final DashboardLayoutController _dashboardLayoutController;

  // متحكم التمرير للوحة المعلومات
  final ScrollController _scrollController = ScrollController();

  // عناصر لوحة التحكم
  List<DashboardItem> _dashboardItems = [];

  // حالة التحميل والخطأ
  bool _isLoading = false;
  String? _errorMessage;

  // تم تعليق هذا المتغير لأنه غير مستخدم حاليًا
  // final int _selectedItemSize = 1;

  // متغيرات التصفية
  final Map<String, DateTime?> _chartStartDates = {};
  final Map<String, DateTime?> _chartEndDates = {};
  final Map<String, TimeFilterType> _chartFilterTypes = {};
  final Map<String, ChartType> _chartTypes = {};
  final Map<String, AdvancedFilterOptions> _advancedFilterOptions = {};

  // مفاتيح عامة للمخططات لاستخدامها في تصدير الصور
  final Map<String, GlobalKey> _chartKeys = {};

  // تم إزالة متغير _isEditMode لأنه لم يعد مستخدمًا

  // متغير للتحكم في حالة تهيئة المخططات
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات أولاً
    _initializeControllers();

    // تهيئة الفلاتر الافتراضية
    _initializeDefaultFilters();

    // تهيئة قائمة فارغة لعناصر لوحة المعلومات
    _dashboardItems = [];

    // استخدام addPostFrameCallback بدلاً من التأخير الطويل
    // هذا يضمن أن الشاشة قد تم بناؤها بالكامل قبل تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // تحميل البيانات مباشرة بعد بناء الشاشة
        _loadData();
        debugPrint('تم بدء تحميل البيانات بعد بناء الشاشة');
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تسجيل المراقبة للتنقل بين الصفحات
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }

    // تحديث المحتوى عند تغيير التبعيات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateDashboardItemSizes();
        _updateDashboardItemsContent();
      }
    });
  }

  @override
  void dispose() {
    _routeObserver.unsubscribe(this);
    _scrollController.dispose();
    super.dispose();
  }

  /// تهيئة المتحكمات
  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
    _chartExportService = Get.find<ChartExportService>();
    _routeObserver = Get.find<RouteObserver<PageRoute>>();
    // تم تعليق هذا السطر لأن المتغير غير مستخدم حاليًا
    // _databaseHelper = Get.find<DatabaseHelper>();

    // تهيئة متحكم لوحة المعلومات
    try {
      _dashboardController = Get.find<DashboardController>();
    } catch (e) {
      _dashboardController = Get.put(DashboardController());
    }

    // تهيئة متحكم تخطيط لوحة المعلومات
    try {
      _dashboardLayoutController = Get.find<DashboardLayoutController>();
    } catch (e) {
      _dashboardLayoutController = Get.put(DashboardLayoutController());
    }

    // التأكد من تسجيل مستودع لوحة المعلومات
    if (!Get.isRegistered<DashboardRepository>()) {
      Get.put(DashboardRepository());
    }
  }

  /// الحصول على مفتاح المخطط أو إنشاؤه إذا لم يكن موجودًا
  /// هذه الطريقة تضمن استقرار المفاتيح وتجنب إعادة إنشائها بشكل متكرر
  GlobalKey getOrCreateChartKey(String itemId) {
    if (!_chartKeys.containsKey(itemId)) {
      _chartKeys[itemId] = GlobalKey(debugLabel: 'chart_${itemId}_fixed');
    }
    return _chartKeys[itemId]!;
  }

  /// تنظيف وتحديث مفاتيح المخططات
  /// تستخدم هذه الدالة في _updateSingleChart لتحديث مفتاح مخطط محدد
  void _cleanupChartKeys() {
    // إنشاء مجموعة من معرفات العناصر الحالية
    final currentItemIds = _dashboardItems.map((item) => item.id).toSet();

    // إزالة المفاتيح التي لم تعد مستخدمة
    _chartKeys.removeWhere((key, _) => !currentItemIds.contains(key));

    debugPrint('تم تنظيف مفاتيح المخططات: ${_chartKeys.length} مفتاح');
  }

  /// تهيئة الفلاتر الافتراضية
  void _initializeDefaultFilters() {
    // تعيين فلاتر افتراضية لجميع المخططات
    final defaultChartKeys = [
      'taskStatus',
      'monthlyTasks',
      'priority',
      'department',
      'departmentPerformance',
      'timeEffort',
      'activityHeatmap',
      'tasksTreemap',
      'userTasksStatus'
    ];

    for (final key in defaultChartKeys) {
      // تعيين نوع الفلتر الافتراضي (الشهر الحالي)
      _chartFilterTypes[key] = TimeFilterType.month;

      // حساب تواريخ البداية والنهاية بناءً على نوع الفلتر
      final now = DateTime.now();
      final firstDayOfMonth = DateTime(now.year, now.month, 1);
      final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

      _chartStartDates[key] = firstDayOfMonth;
      _chartEndDates[key] = lastDayOfMonth;

      // تعيين نوع المخطط الافتراضي
      _chartTypes[key] = _getDefaultChartType(key);

      // تهيئة خيارات الفلترة المتقدمة
      _advancedFilterOptions[key] = AdvancedFilterOptions();
    }
  }

  /// الحصول على نوع المخطط الافتراضي بناءً على المفتاح
  ChartType _getDefaultChartType(String key) {
    switch (key) {
      case 'taskStatus':
        return ChartType.pie;
      case 'monthlyTasks':
        return ChartType.bar;
      case 'priority':
        return ChartType.pie;
      case 'department':
        return ChartType.bar;
      case 'departmentPerformance':
        return ChartType.radar;
      case 'timeEffort':
        return ChartType.bubble;
      case 'activityHeatmap':
        return ChartType.heatmap;
      case 'tasksTreemap':
        return ChartType.treemap;
      case 'userTasksStatus':
        return ChartType.bar;
      case 'workflow':
        return ChartType.funnel;
      case 'projectTimeline':
        return ChartType.gantt;
      case 'dataFlow':
        return ChartType.sankey;
      default:
        return ChartType.bar;
    }
  }

  /// تحميل البيانات بشكل متوازي لتحسين الأداء
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _isInitializing = true;
    });

    try {
      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;

      if (userId == null) {
        throw Exception('لم يتم العثور على معرف المستخدم الحالي');
      }

      debugPrint('بدء تحميل البيانات للمستخدم: $userId');

      // تحميل البيانات بشكل متوازي لتحسين الأداء
      // استخدام loadTasksByUserPermissions للحصول على البيانات المناسبة حسب صلاحيات المستخدم
      // هذا يضمن أن المدير يرى جميع المهام والمستخدمين العاديين يرون مهامهم فقط
      await Future.wait([
        _taskController.loadTasksByUserPermissions(userId),
        _userController.loadAllUsers(),
        _departmentController.loadDepartments(),
      ]);

      debugPrint('تم تحميل البيانات الأساسية:');
      debugPrint('- المهام: ${_taskController.tasks.length} مهمة');
      debugPrint('- المستخدمين: ${_userController.users.length} مستخدم');
      debugPrint('- الأقسام: ${_departmentController.departments.length} قسم');

      // تحميل تخطيط لوحة المعلومات
      await _loadDashboardLayout(userId);
      debugPrint(
          'تم تحميل تخطيط لوحة المعلومات: ${_dashboardItems.length} عنصر');

      // تحديث أحجام وعرض العناصر
      if (mounted) {
        // تحديث أحجام العناصر أولاً
        _updateDashboardItemSizes();

        // تحديث محتوى العناصر
        await _updateDashboardItemsContent();
        debugPrint('تم تحديث محتوى عناصر لوحة المعلومات');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        });
      }
      debugPrint('خطأ في تحميل البيانات: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitializing = false;
        });
        debugPrint('اكتمل تحميل البيانات');
      }
    }
  }

  /// تحميل تخطيط لوحة المعلومات
  Future<void> _loadDashboardLayout(String userId) async {
    try {
      // محاولة تحميل التخطيط باستخدام المتحكم أولاً
      await _dashboardLayoutController.loadSavedLayout();

      if (_dashboardLayoutController.dashboardItems.isNotEmpty) {
        // تحديث عناصر لوحة المعلومات من المتحكم
        final dashboardItems =
            _dashboardLayoutController.dashboardItems.map((item) {
          return DashboardItem(
            id: item.id,
            title: item.title,
            content: Container(), // سيتم استبداله لاحقًا
            size: item.size,
            position: item.position,
            chartType: item.chartType,
            filterOptions: item.filterOptions,
            gridSize: item.gridSize,
          );
        }).toList();

        setState(() {
          _dashboardItems = dashboardItems;
        });

        debugPrint(
            'تم تحميل تخطيط لوحة المعلومات من المتحكم: ${dashboardItems.length} عنصر');
        return;
      }

      // إذا لم يتم العثور على تخطيط في المتحكم، نحاول تحميله من قاعدة البيانات
      final dashboardRepository = Get.find<DashboardRepository>();
      final layoutData = await dashboardRepository.getDashboardLayout(userId);

      if (layoutData.isNotEmpty) {
        // تحويل البيانات إلى تنسيق مناسب
        final dashboardItems = layoutData.map((item) {
          // استخراج نوع المخطط إذا كان موجودًا
          ChartType? chartType;
          if (item['chartType'] != null) {
            chartType = ChartType.values[item['chartType']];
          }

          // استخراج خيارات التصفية إذا كانت موجودة
          AdvancedFilterOptions? filterOptions;
          if (item['filterOptions'] != null) {
            filterOptions =
                AdvancedFilterOptions.fromJson(item['filterOptions']);
          }

          return DashboardItem(
            id: item['id'],
            title: item['title'],
            content: Container(), // سيتم استبداله لاحقًا
            size: Size(
              item['size']['width'].toDouble(),
              item['size']['height'].toDouble(),
            ),
            position: Offset(
              item['position']['dx'].toDouble(),
              item['position']['dy'].toDouble(),
            ),
            chartType: chartType,
            filterOptions: filterOptions,
            gridSize: item['gridSize']?.toDouble() ?? 20.0,
          );
        }).toList();

        setState(() {
          _dashboardItems = dashboardItems;
        });

        // تحديث المتحكم أيضًا
        await _dashboardLayoutController.saveDashboardLayout(
            _dashboardItems.map((item) => item.toJson()).toList());

        // محاولة تحميل معايير التصفية المحفوظة
        await _loadSavedFilterOptions();

        debugPrint(
            'تم تحميل تخطيط لوحة المعلومات من قاعدة البيانات: ${dashboardItems.length} عنصر');
      } else {
        debugPrint('لم يتم العثور على تخطيط لوحة المعلومات في قاعدة البيانات');
        // إنشاء تخطيط افتراضي
        _createDefaultDashboardLayout();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل تخطيط لوحة المعلومات: $e';
      });
      debugPrint('خطأ في تحميل تخطيط لوحة المعلومات: $e');

      // إنشاء تخطيط افتراضي في حالة الخطأ
      _createDefaultDashboardLayout();
    }
  }

  /// إنشاء تخطيط افتراضي للوحة المعلومات
  void _createDefaultDashboardLayout() {
    // الحصول على حجم الشاشة
    final screenWidth = MediaQuery.of(context).size.width;

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    int columns = 1;
    if (screenWidth >= 1200) {
      columns = 3;
    } else if (screenWidth >= 800) {
      columns = 2;
    }

    // حساب عرض العنصر
    final padding = 8.0;
    final spacing = 8.0;
    final itemWidth = (screenWidth / columns) -
        (padding * 2) -
        (spacing * (columns - 1) / columns);
    final itemHeight = 350.0;

    // حساب مواقع الأعمدة
    final columnPositions = List.generate(columns, (index) {
      return padding + (index * (itemWidth + spacing));
    });

    // إنشاء العناصر الافتراضية
    final defaultItems = [
      _createDashboardItem('توزيع المهام حسب الحالة', ChartType.pie, 0,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('المهام حسب الشهر', ChartType.bar, 1,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('توزيع المهام حسب الأولوية', ChartType.pie, 2,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('المهام حسب القسم', ChartType.bar, 3,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('تحليل أداء الأقسام', ChartType.radar, 4,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('تحليل الوقت والجهد', ChartType.bubble, 5,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('خريطة حرارية للنشاط', ChartType.heatmap, 6,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('توزيع المهام بالخريطة الشجرية', ChartType.treemap,
          7, columnPositions, itemWidth, itemHeight),
      _createDashboardItem('مهام المستخدمين حسب الحالة', ChartType.bar, 8,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('تدفق سير العمل', ChartType.funnel, 9,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('الجدول الزمني للمشاريع', ChartType.gantt, 10,
          columnPositions, itemWidth, itemHeight),
      _createDashboardItem('تدفق البيانات بين الأقسام', ChartType.sankey, 11,
          columnPositions, itemWidth, itemHeight),
      // إضافة بطاقة عدد المهام الملغاة
      _createCancelledTasksCard(12, columnPositions, itemWidth, 150.0),
    ];

    setState(() {
      _dashboardItems = defaultItems;
    });

    // حفظ التخطيط الافتراضي
    _dashboardLayoutController.saveDashboardLayout(
        _dashboardItems.map((item) => item.toJson()).toList());
  }

  /// إنشاء بطاقة لعرض عدد المهام الملغاة
  DashboardItem _createCancelledTasksCard(int index,
      List<double> columnPositions, double itemWidth, double itemHeight) {
    // حساب الموقع بناءً على الترتيب وعدد الأعمدة
    final columnCount = columnPositions.length;
    final column = index % columnCount;
    final row = index ~/ columnCount;

    return DashboardItem(
      id: const Uuid().v4(),
      title: 'عدد المهام الملغاة',
      content: _buildCancelledTasksContent(),
      position: Offset(
        columnPositions[column],
        20 + (row * (itemHeight + 8)),
      ),
      size: Size(itemWidth, itemHeight),
      chartType: null, // لا نستخدم نوع مخطط لهذه البطاقة
      filterOptions: AdvancedFilterOptions(),
      gridSize: 20,
    );
  }

  /// بناء محتوى بطاقة عدد المهام الملغاة
  Widget _buildCancelledTasksContent() {
    return Obx(() {
      // الحصول على عدد المهام الملغاة
      final cancelledTasks = _taskController.tasks
          .where((task) => task.status == TaskStatus.cancelled)
          .length;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(
                  red: Colors.grey.r.toDouble(),
                  green: Colors.grey.g.toDouble(),
                  blue: Colors.grey.b.toDouble(),
                  alpha: 25.5), // 0.1 opacity = 25.5/255
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.cancel_outlined,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '$cancelledTasks',
              style: const TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'مهمة ملغاة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// إنشاء عنصر لوحة معلومات جديد
  DashboardItem _createDashboardItem(
      String title,
      ChartType chartType,
      int index,
      List<double> columnPositions,
      double itemWidth,
      double itemHeight) {
    // حساب الموقع بناءً على الترتيب وعدد الأعمدة
    final columnCount = columnPositions.length;
    final column = index % columnCount;
    final row = index ~/ columnCount;

    return DashboardItem(
      id: const Uuid().v4(),
      title: title,
      content: Container(), // سيتم استبداله لاحقًا
      position: Offset(
        columnPositions[column],
        20 + (row * (itemHeight + 8)),
      ),
      size: Size(itemWidth, itemHeight),
      chartType: chartType,
      filterOptions: AdvancedFilterOptions(),
      gridSize: 20,
    );
  }

  /// تحميل معايير التصفية المحفوظة
  Future<void> _loadSavedFilterOptions() async {
    try {
      final dashboardRepository = Get.find<DashboardRepository>();
      final chartKeys = [
        'taskStatus',
        'monthlyTasks',
        'priority',
        'department',
        'departmentPerformance',
        'timeEffort',
        'activityHeatmap',
        'tasksTreemap',
        'workflow',
        'projectTimeline',
        'dataFlow'
      ];

      for (final chartKey in chartKeys) {
        // تحميل معايير التصفية العادية
        final filterData =
            await dashboardRepository.getChartData('${chartKey}_filter');
        if (filterData != null) {
          // استعادة معايير التصفية
          if (filterData['startDate'] != null) {
            _chartStartDates[chartKey] = DateTime.fromMillisecondsSinceEpoch(
                filterData['startDate'] as int);
          }

          if (filterData['endDate'] != null) {
            _chartEndDates[chartKey] = DateTime.fromMillisecondsSinceEpoch(
                filterData['endDate'] as int);
          }

          if (filterData['filterType'] != null) {
            _chartFilterTypes[chartKey] =
                TimeFilterType.values[filterData['filterType'] as int];
          }

          if (filterData['chartType'] != null) {
            _chartTypes[chartKey] =
                ChartType.values[filterData['chartType'] as int];
          }
        }

        // تحميل معايير التصفية المتقدمة
        final advancedFilterData = await dashboardRepository
            .getChartData('${chartKey}_advanced_filter');
        if (advancedFilterData != null) {
          _advancedFilterOptions[chartKey] =
              AdvancedFilterOptions.fromJson(advancedFilterData);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل معايير التصفية المحفوظة: $e');
    }
  }

  /// تحديث أحجام عناصر لوحة المعلومات بشكل متجاوب
  void _updateDashboardItemSizes() {
    if (_dashboardItems.isEmpty) return;

    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // تقليل الهوامش والمسافات
    final padding = 8.0;
    final spacing = 12.0;

    // حساب عدد الأعمدة بناءً على حجم الشاشة
    int columns = 1;
    if (screenWidth >= 1400) {
      columns = 4; // شاشة كبيرة جداً: 4 أعمدة
    } else if (screenWidth >= 1200) {
      columns = 3; // شاشة كبيرة: 3 أعمدة
    } else if (screenWidth >= 800) {
      columns = 2; // شاشة متوسطة: 2 عمود
    }

    // حساب عرض العنصر
    final itemWidth = (screenWidth / columns) -
        (padding * 2) -
        (spacing * (columns - 1) / columns);

    // حساب ارتفاع العنصر بناءً على ارتفاع الشاشة
    final itemHeight = screenHeight * (columns == 1 ? 0.35 : 0.45);

    debugPrint(
        'تحديث أحجام العناصر: عرض الشاشة=$screenWidth، ارتفاع الشاشة=$screenHeight');
    debugPrint(
        'عدد الأعمدة=$columns، عرض العنصر=$itemWidth، ارتفاع العنصر=$itemHeight');

    // تحديث أحجام ومواقع العناصر
    setState(() {
      for (int i = 0; i < _dashboardItems.length; i++) {
        final item = _dashboardItems[i];

        // حساب الموقع الجديد بناءً على عدد الأعمدة
        final row = i ~/ columns;
        final col = i % columns;
        final xPos = padding + (col * (itemWidth + spacing));
        final yPos = padding + (row * (itemHeight + spacing));

        _dashboardItems[i] = DashboardItem(
          id: item.id,
          title: item.title,
          content: item.content,
          position: Offset(xPos, yPos),
          size: Size(itemWidth, itemHeight),
          chartType: item.chartType,
          filterOptions: item.filterOptions,
          gridSize: item.gridSize,
        );
      }
    });
  }

  /// تحديث محتوى عناصر لوحة المعلومات
  Future<void> _updateDashboardItemsContent() async {
    if (_dashboardItems.isEmpty) return;

    // استخدام قائمة مؤقتة لتجنب تحديث الحالة أثناء التكرار
    final updatedItems = <DashboardItem>[];

    for (int i = 0; i < _dashboardItems.length; i++) {
      final item = _dashboardItems[i];

      try {
        // التأكد من وجود خيارات التصفية المتقدمة
        final advancedFilterOptions =
            item.filterOptions ?? AdvancedFilterOptions();

        // تحديث محتوى العنصر بناءً على العنوان
        Widget content;

        // التحقق إذا كانت البطاقة هي بطاقة المهام الملغاة
        if (item.title == 'عدد المهام الملغاة') {
          content = _buildCancelledTasksContent();
        } else {
          // تحديث محتوى العنصر بناءً على نوع المخطط
          content = _buildChartContent(item);
        }

        updatedItems.add(DashboardItem(
          id: item.id,
          title: item.title,
          content: content,
          position: item.position,
          size: item.size,
          chartType: item.chartType,
          filterOptions:
              advancedFilterOptions, // استخدام خيارات التصفية المحدثة
          gridSize: item.gridSize,
        ));
      } catch (e) {
        // في حالة حدوث خطأ، استخدم محتوى فارغ مع رسالة خطأ
        debugPrint('خطأ في بناء محتوى المخطط ${item.title}: $e');

        // إضافة أزرار تغيير نوع المخطط في حالة الخطأ
        updatedItems.add(DashboardItem(
          id: item.id,
          title: item.title,
          content: Container(
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ في تحميل المخطط: $e',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                const Text(
                  'يمكنك تغيير نوع المخطط بالنقر المزدوج على المخطط',
                  style: TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _updateChartType(item.id, ChartType.bar),
                      icon: const Icon(Icons.bar_chart),
                      label: const Text('شريطي'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _updateChartType(item.id, ChartType.pie),
                      icon: const Icon(Icons.pie_chart),
                      label: const Text('دائري'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          position: item.position,
          size: item.size,
          chartType: item.chartType,
          filterOptions: item.filterOptions ?? AdvancedFilterOptions(),
          gridSize: item.gridSize,
        ));
      }
    }

    // تحديث الحالة مرة واحدة بعد الانتهاء من التكرار
    if (mounted) {
      setState(() {
        _dashboardItems = updatedItems;
      });
    }
  }

  /// بناء محتوى المخطط بناءً على نوع المخطط
  Widget _buildChartContent(DashboardItem item) {
    // الحصول على نوع المخطط
    final chartType = item.chartType ?? ChartType.bar;

    // الحصول على معايير التصفية
    final chartKey = _getChartKeyFromTitle(item.title);
    final startDate = _chartStartDates[chartKey];
    final endDate = _chartEndDates[chartKey];
    final filterType = _chartFilterTypes[chartKey] ?? TimeFilterType.month;

    // التأكد من وجود خيارات التصفية المتقدمة
    final advancedFilterOptions = item.filterOptions ??
        _advancedFilterOptions[chartKey] ??
        AdvancedFilterOptions();

    // بناء المخطط بناءً على نوع المخطط
    switch (chartType) {
      case ChartType.pie:
        return _buildPieChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.bar:
        // إذا كان العنوان يشير إلى مخطط مهام المستخدمين حسب الحالة
        if (item.title == 'مهام المستخدمين حسب الحالة') {
          return _buildUserTasksStatusChart(item, chartKey, startDate, endDate,
              filterType, advancedFilterOptions);
        } else {
          return _buildBarChart(item, chartKey, startDate, endDate, filterType,
              advancedFilterOptions);
        }
      case ChartType.line:
        return _buildLineChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.radar:
        return _buildRadarChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.bubble:
        return _buildBubbleChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.heatmap:
        return _buildHeatmapChart(item, chartKey, startDate, endDate,
            filterType, advancedFilterOptions);
      case ChartType.treemap:
        return _buildTreemapChart(item, chartKey, startDate, endDate,
            filterType, advancedFilterOptions);
      case ChartType.funnel:
        return _buildFunnelChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.gantt:
        return _buildGanttChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.sankey:
        return _buildSankeyChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      default:
        // عرض رسالة خطأ مع أزرار تغيير نوع المخطط
        return Column(
          children: [
            // مكون الفلترة والتصدير
            UnifiedFilterExportWidget(
              title: item.title,
              chartKey: chartKey,
              startDate: startDate,
              endDate: endDate,
              filterType: filterType,
              chartType: chartType,
              advancedFilterOptions: advancedFilterOptions,
              onFilterChanged: (start, end, type, chartKey) {
                _updateChartFilter(
                    chartKey, start, end, type, advancedFilterOptions);
              },
              onChartTypeChanged: (type, chartKey) {
                _updateChartType(item.id, type);
              },
              onExport: (format, title) {
                _exportChart(item.id, title);
              },
            ),

            // رسالة الخطأ
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'نوع المخطط غير مدعوم',
                      style: TextStyle(color: Colors.red, fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'يرجى اختيار نوع مخطط آخر من الأزرار أعلاه',
                      style: TextStyle(fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        _updateChartType(item.id, ChartType.bar);
                      },
                      icon: const Icon(Icons.bar_chart),
                      label: const Text('تغيير إلى مخطط شريطي'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
    }
  }

  /// الحصول على مفتاح المخطط من العنوان
  String _getChartKeyFromTitle(String title) {
    switch (title) {
      case 'توزيع المهام حسب الحالة':
        return 'taskStatus';
      case 'المهام حسب الشهر':
        return 'monthlyTasks';
      case 'توزيع المهام حسب الأولوية':
        return 'priority';
      case 'المهام حسب القسم':
        return 'department';
      case 'تحليل أداء الأقسام':
        return 'departmentPerformance';
      case 'تحليل الوقت والجهد':
        return 'timeEffort';
      case 'خريطة حرارية للنشاط':
        return 'activityHeatmap';
      case 'توزيع المهام بالخريطة الشجرية':
        return 'tasksTreemap';
      case 'مهام المستخدمين حسب الحالة':
        return 'userTasksStatus';
      case 'تدفق سير العمل':
        return 'workflow';
      case 'الجدول الزمني للمشاريع':
        return 'projectTimeline';
      case 'تدفق البيانات بين الأقسام':
        return 'dataFlow';
      default:
        return 'unknown';
    }
  }

  /// بناء مخطط دائري
  Widget _buildPieChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.pie,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الدائري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToPieChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الدائري: ${chartData.length} عنصر');

              return EnhancedPieChart(
                // استخدام ValueKey بدلاً من GlobalKey
                key: ValueKey('pie_chart_${item.id}'),
                title: '',
                data: chartData,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.pie,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الشريطي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToPieChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الشريطي: ${chartData.length} عنصر');

              return EnhancedBarChart(
                key: ValueKey('bar_chart_${item.id}'),
                title: '',
                data: chartData,
                showGrid: true,
                showValues: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط خطي
  Widget _buildLineChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.line,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الخطي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToLineChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الخطي: ${chartData.length} سلسلة');

              return EnhancedLineChart(
                key: ValueKey('line_chart_${item.id}'),
                title: '',
                data: chartData,
                showDots: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.line,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط راداري
  Widget _buildRadarChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.radar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الراداري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // تحضير البيانات مع التأكد من وجود 3 عناصر على الأقل
              final rawData = _convertToRadarChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              final Map<String, List<double>> radarData =
                  _ensureMinimumRadarData(rawData);
              debugPrint('بيانات المخطط الراداري: ${radarData.length} سلسلة');

              return EnhancedRadarChart(
                key: ValueKey('radar_chart_${item.id}'),
                title: '',
                data: radarData,
                axisLabels: ['محور 1', 'محور 2', 'محور 3', 'محور 4', 'محور 5'],
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.radar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط فقاعي
  Widget _buildBubbleChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bubble,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الفقاعي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final bubbleData = _convertToBubbleChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الفقاعي: ${bubbleData.length} سلسلة');

              return EnhancedBubbleChart(
                key: ValueKey('bubble_chart_${item.id}'),
                title: '',
                data: bubbleData,
                showGrid: true,
                showLabels: true,
                showLegend: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                horizontalInterval: 10.0,
                verticalInterval: 10.0,
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bubble,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط حراري
  Widget _buildHeatmapChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.heatmap,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الحراري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // تحضير البيانات مع التأكد من وجود بيانات كافية
              final rawData = _convertToHeatmapData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              final heatmapData = _ensureValidHeatmapData(rawData);
              debugPrint('بيانات المخطط الحراري: ${heatmapData.length} صفوف');

              return EnhancedHeatmapChart(
                key: ValueKey('heatmap_chart_${item.id}'),
                title: '',
                data: heatmapData,
                xLabels: [
                  'الأحد',
                  'الاثنين',
                  'الثلاثاء',
                  'الأربعاء',
                  'الخميس',
                  'الجمعة',
                  'السبت'
                ],
                yLabels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                xAxisTitle: 'أيام الأسبوع',
                yAxisTitle: 'الأسابيع',
                showValues: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.heatmap,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط شجري
  Widget _buildTreemapChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.treemap,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الشجري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final treemapData = _convertToTreemapData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الشجري: ${treemapData.length} عنصر');

              return EnhancedTreemapChart(
                key: ValueKey('treemap_chart_${item.id}'),
                title: '',
                data: treemapData,
                showValues: true,
                showPercentages: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                // إضافة المعلمات المطلوبة
                chartType: ChartType.treemap,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط مهام المستخدمين حسب الحالة
  Widget _buildUserTasksStatusChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // مخطط مهام المستخدمين حسب الحالة
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              return UserTasksStatusChart(
                key: ValueKey('user_tasks_status_chart_${item.id}'),
                title: '',
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(chartKey, start, end, TimeFilterType.month,
                      advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// الحصول على بيانات المخطط
  Map<String, dynamic> _getChartData(String chartKey, DateTime? startDate,
      DateTime? endDate, AdvancedFilterOptions advancedFilterOptions) {
    // التأكد من وجود تواريخ البداية والنهاية
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    // الحصول على المهام المصفاة
    final filteredTasks = _taskController.tasks.where((task) {
      // تصفية حسب التاريخ
      final taskDate = task.createdAt;
      final inDateRange = taskDate.isAfter(start) &&
          taskDate.isBefore(end.add(const Duration(days: 1)));

      // تصفية حسب الخيارات المتقدمة
      bool matchesAdvancedFilters = true;

      if (advancedFilterOptions.departmentIds != null &&
          advancedFilterOptions.departmentIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.departmentIds!.contains(task.departmentId);
      }

      if (advancedFilterOptions.assigneeIds != null &&
          advancedFilterOptions.assigneeIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.assigneeIds!.contains(task.assigneeId);
      }

      if (advancedFilterOptions.creatorIds != null &&
          advancedFilterOptions.creatorIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.creatorIds!.contains(task.creatorId);
      }

      if (advancedFilterOptions.priorityFilter != null &&
          advancedFilterOptions.priorityFilter!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.priorityFilter!.contains(task.priority);
      }

      if (advancedFilterOptions.statusFilter != null &&
          advancedFilterOptions.statusFilter!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.statusFilter!.contains(task.status);
      }

      return inDateRange && matchesAdvancedFilters;
    }).toList();

    // بناء بيانات المخطط بناءً على نوع المخطط
    switch (chartKey) {
      case 'taskStatus':
        return _buildTaskStatusData(filteredTasks);
      case 'monthlyTasks':
        return _buildMonthlyTasksData(filteredTasks, start, end);
      case 'priority':
        return _buildPriorityData(filteredTasks);
      case 'department':
        return _buildDepartmentData(filteredTasks);
      case 'departmentPerformance':
        return _buildDepartmentPerformanceData(filteredTasks);
      case 'timeEffort':
        return _buildTimeEffortData(filteredTasks);
      case 'activityHeatmap':
        return _buildActivityHeatmapData(filteredTasks, start, end);
      case 'tasksTreemap':
        return _buildTasksTreemapData(filteredTasks);
      case 'workflow':
        return _buildWorkflowData(filteredTasks);
      case 'projectTimeline':
        return _buildProjectTimelineData(filteredTasks);
      case 'dataFlow':
        return _buildDataFlowData(filteredTasks);
      default:
        return {'labels': [], 'values': []};
    }
  }

  /// بناء بيانات توزيع المهام حسب الحالة
  Map<String, dynamic> _buildTaskStatusData(List<dynamic> tasks) {
    // تجميع المهام حسب الحالة
    final Map<String, int> statusCounts = {
      'قيد الانتظار': 0,
      'قيد التنفيذ': 0,
      'مكتملة': 0,
      'ملغاة': 0,
    };

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final status = task.status.toString().split('.').last;
        final statusName = _getStatusName(status);
        statusCounts[statusName] = (statusCounts[statusName] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = statusCounts.keys.toList();
    final values = statusCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات المهام حسب الشهر
  Map<String, dynamic> _buildMonthlyTasksData(
      List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء قائمة بالأشهر في النطاق
    final months = <DateTime>[];
    DateTime current = DateTime(start.year, start.month, 1);

    while (current.isBefore(end) ||
        current.year == end.year && current.month == end.month) {
      months.add(current);
      current = DateTime(current.year, current.month + 1, 1);
    }

    // التأكد من وجود 6 أشهر على الأقل للعرض
    if (months.length < 6) {
      final lastMonth = months.isNotEmpty ? months.last : DateTime.now();
      for (int i = 1; months.length < 6; i++) {
        months.add(DateTime(lastMonth.year, lastMonth.month + i, 1));
      }
    }

    // تجميع المهام حسب الشهر
    final Map<String, int> monthlyCounts = {};

    for (final month in months) {
      final monthName = DateFormat.yMMM('ar').format(month);
      monthlyCounts[monthName] = 0;
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final taskDate = task.createdAt ?? DateTime.now();
        final monthName = DateFormat.yMMM('ar')
            .format(DateTime(taskDate.year, taskDate.month, 1));

        if (monthlyCounts.containsKey(monthName)) {
          monthlyCounts[monthName] = (monthlyCounts[monthName] ?? 0) + 1;
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = monthlyCounts.keys.toList();
    final values = monthlyCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات توزيع المهام حسب الأولوية
  Map<String, dynamic> _buildPriorityData(List<dynamic> tasks) {
    // تجميع المهام حسب الأولوية
    final Map<String, int> priorityCounts = {
      'منخفضة': 0,
      'متوسطة': 0,
      'عالية': 0,
      'عاجلة': 0,
    };

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final priority = task.priority.toString().split('.').last;
        final priorityName = _getPriorityName(priority);
        priorityCounts[priorityName] = (priorityCounts[priorityName] ?? 0) + 1;
      }
    } else {
      // إضافة بيانات افتراضية إذا كانت القائمة فارغة
      priorityCounts['منخفضة'] = 8;
      priorityCounts['متوسطة'] = 15;
      priorityCounts['عالية'] = 10;
      priorityCounts['عاجلة'] = 5;

      debugPrint('تم استخدام بيانات افتراضية لتوزيع المهام حسب الأولوية');
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = priorityCounts.keys.toList();
    final values = priorityCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات المهام حسب القسم
  Map<String, dynamic> _buildDepartmentData(List<dynamic> tasks) {
    // تجميع المهام حسب القسم
    final Map<String, int> departmentCounts = {};

    // إضافة جميع الأقسام الموجودة أولاً
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        departmentCounts[dept.name] = 0;
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      departmentCounts['قسم الموارد البشرية'] = 0;
      departmentCounts['قسم تقنية المعلومات'] = 0;
      departmentCounts['قسم المالية'] = 0;
      departmentCounts['قسم التسويق'] = 0;
      departmentCounts['قسم المبيعات'] = 0;
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final departmentId = task.departmentId;
        final department = _departmentController.departments.firstWhere(
          (dept) => dept.id == departmentId,
          orElse: () =>
              Department(id: '', name: 'غير محدد', createdAt: DateTime.now()),
        );

        final departmentName = department.name;
        departmentCounts[departmentName] =
            (departmentCounts[departmentName] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = departmentCounts.keys.toList();
    final values = departmentCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات أداء الأقسام
  Map<String, dynamic> _buildDepartmentPerformanceData(List<dynamic> tasks) {
    // تجميع المهام المكتملة حسب القسم
    final Map<String, int> completedTasks = {};
    final Map<String, int> totalTasks = {};

    // إضافة جميع الأقسام الموجودة أولاً
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        completedTasks[dept.name] = 0;
        totalTasks[dept.name] = 0;
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      final defaultDepts = [
        'قسم الموارد البشرية',
        'قسم تقنية المعلومات',
        'قسم المالية',
        'قسم التسويق',
        'قسم المبيعات'
      ];

      for (final dept in defaultDepts) {
        completedTasks[dept] = 0;
        totalTasks[dept] = 0;
      }
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final departmentId = task.departmentId;
        final department = _departmentController.departments.firstWhere(
          (dept) => dept.id == departmentId,
          orElse: () =>
              Department(id: '', name: 'غير محدد', createdAt: DateTime.now()),
        );

        final departmentName = department.name;
        totalTasks[departmentName] = (totalTasks[departmentName] ?? 0) + 1;

        if (task.status == TaskStatus.completed) {
          completedTasks[departmentName] =
              (completedTasks[departmentName] ?? 0) + 1;
        }
      }
    }

    // حساب نسبة الإنجاز لكل قسم
    final Map<String, double> completionRates = {};

    for (final department in totalTasks.keys) {
      final completed = completedTasks[department] ?? 0;
      final total = totalTasks[department] ?? 0;
      completionRates[department] = total > 0 ? (completed / total) * 100 : 0;
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = completionRates.keys.toList();
    final values = completionRates.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات الوقت والجهد
  Map<String, dynamic> _buildTimeEffortData(List<dynamic> tasks) {
    // تجميع المهام حسب الوقت والجهد
    final List<Map<String, dynamic>> bubbleData = [];

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          // استخدام الوقت المقدر كمحور س
          final estimatedTime = task.estimatedTime ?? 0;

          // استخدام الوقت الفعلي كمحور ص
          final actualTime = task.actualTime ?? 0;

          // استخدام الأولوية كحجم الفقاعة
          final priority = task.priority.toString().split('.').last;
          final priorityValue = _getPriorityValue(priority);

          // استخدام الحالة كلون الفقاعة
          final status = task.status.toString().split('.').last;

          bubbleData.add({
            'x': estimatedTime.toDouble(),
            'y': actualTime.toDouble(),
            'size': priorityValue.toDouble(),
            'category': _getStatusName(status),
            'name': task.title,
          });
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة: $e');
          // تجاهل المهمة التي تسبب الخطأ
        }
      }
    }

    // لا نضيف بيانات افتراضية، نعرض رسالة "لا توجد بيانات" بدلاً من ذلك

    return {
      'bubbleData': bubbleData,
    };
  }

  /// بناء بيانات خريطة النشاط الحرارية
  Map<String, dynamic> _buildActivityHeatmapData(
      List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء مصفوفة ثنائية الأبعاد لتمثيل النشاط
    final int daysInRange = end.difference(start).inDays + 1;
    final int weeks = (daysInRange / 7).ceil();

    // التأكد من وجود 4 أسابيع على الأقل
    final int finalWeeks = weeks < 4 ? 4 : weeks;

    // تهيئة مصفوفة البيانات
    final List<List<int>> heatmapData = List.generate(
      7, // أيام الأسبوع
      (_) => List.generate(finalWeeks, (_) => 0),
    );

    // إنشاء تسميات الأيام والأسابيع
    final List<String> dayLabels = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];

    final List<String> weekLabels = [];
    DateTime currentWeekStart = start;
    for (int i = 0; i < finalWeeks; i++) {
      weekLabels.add(DateFormat.MMMd('ar').format(currentWeekStart));
      currentWeekStart = currentWeekStart.add(const Duration(days: 7));
    }

    if (tasks.isNotEmpty) {
      // تجميع المهام حسب اليوم
      for (final task in tasks) {
        final taskDate = task.createdAt ?? DateTime.now();

        // التحقق من أن التاريخ ضمن النطاق
        if (taskDate.isBefore(start) || taskDate.isAfter(end)) continue;

        // حساب الموقع في المصفوفة
        final daysSinceStart = taskDate.difference(start).inDays;
        final weekIndex = daysSinceStart ~/ 7;
        final dayOfWeek = taskDate.weekday - 1; // 0 = الاثنين، 6 = الأحد

        // التأكد من أن المؤشرات ضمن النطاق
        if (weekIndex >= 0 &&
            weekIndex < finalWeeks &&
            dayOfWeek >= 0 &&
            dayOfWeek < 7) {
          heatmapData[dayOfWeek][weekIndex]++;
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final List<List<Map<String, dynamic>>> formattedData = [];

    for (int day = 0; day < 7; day++) {
      final List<Map<String, dynamic>> dayData = [];
      for (int week = 0; week < finalWeeks; week++) {
        dayData.add({
          'value': heatmapData[day][week],
          'day': day,
          'week': week,
        });
      }
      formattedData.add(dayData);
    }

    return {
      'data': heatmapData,
      'formattedData': formattedData,
      'dayLabels': dayLabels,
      'weekLabels': weekLabels,
    };
  }

  /// بناء بيانات الخريطة الشجرية
  Map<String, dynamic> _buildTasksTreemapData(List<dynamic> tasks) {
    // تجميع المهام حسب القسم والحالة
    final Map<String, Map<String, int>> treemapData = {};

    // إضافة جميع الأقسام الموجودة أولاً
    final List<String> departments = [];
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        departments.add(dept.name);
        treemapData[dept.name] = {};
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      departments.addAll([
        'قسم الموارد البشرية',
        'قسم تقنية المعلومات',
        'قسم المالية',
        'قسم التسويق',
        'قسم المبيعات'
      ]);

      for (final dept in departments) {
        treemapData[dept] = {};
      }
    }

    // إضافة جميع الحالات لكل قسم
    final List<String> statuses = [
      'قيد الانتظار',
      'قيد التنفيذ',
      'مكتملة',
      'ملغاة'
    ];

    for (final dept in departments) {
      for (final status in statuses) {
        treemapData[dept]![status] = 0;
      }
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhere(
            (dept) => dept.id == departmentId,
            orElse: () =>
                Department(id: '', name: 'غير محدد', createdAt: DateTime.now()),
          );

          final departmentName = department.name;
          final status = task.status.toString().split('.').last;
          final statusName = _getStatusName(status);

          if (!treemapData.containsKey(departmentName)) {
            treemapData[departmentName] = {};
            for (final s in statuses) {
              treemapData[departmentName]![s] = 0;
            }
          }

          treemapData[departmentName]![statusName] =
              (treemapData[departmentName]![statusName] ?? 0) + 1;
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة للخريطة الشجرية: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final List<Map<String, dynamic>> treemapItems = [];

    for (final department in treemapData.keys) {
      for (final status in treemapData[department]!.keys) {
        if (treemapData[department]![status]! > 0) {
          treemapItems.add({
            'name': '$department - $status',
            'parent': department,
            'value': treemapData[department]![status],
            'category': status,
          });
        }
      }
    }

    return {
      'items': treemapItems,
    };
  }

  /// الحصول على اسم الحالة
  String _getStatusName(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'inprogress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغاة';
      case 'new':
      case 'news':
        return 'جديدة';
      case 'review':
      case 'waitingforinfo':
        return 'مراجعة';
      default:
        return status;
    }
  }

  /// الحصول على اسم الأولوية
  String _getPriorityName(String priority) {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على قيمة الأولوية
  int _getPriorityValue(String priority) {
    switch (priority) {
      case 'low':
        return 1;
      case 'medium':
        return 2;
      case 'high':
        return 3;
      case 'urgent':
        return 4;
      default:
        return 1;
    }
  }

  /// بناء مخطط آمن مع معالجة الأخطاء
  /// تم تحسين هذه الدالة لتوفير معالجة أخطاء أفضل وتسجيل أكثر تفصيلاً
  Widget _buildSafeChart(String itemId, Widget Function() builder) {
    try {
      // محاولة بناء المخطط
      return builder();
    } catch (e, stackTrace) {
      // تسجيل الخطأ بشكل أكثر تفصيلاً
      debugPrint('خطأ في بناء المخطط $itemId: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      // عرض واجهة خطأ أكثر فائدة للمستخدم
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'حدث خطأ في عرض المخطط: ${e.toString().split('\n').first}',
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    // إعادة تحميل المخطط فقط بدلاً من إعادة تحميل كل المخططات
                    setState(() {
                      // تحديث المخطط المحدد فقط
                      _updateSingleChart(itemId);
                    });
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تحميل'),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () {
                    // تغيير نوع المخطط إلى النوع الافتراضي (شريطي)
                    _updateChartType(itemId, ChartType.bar);
                  },
                  icon: const Icon(Icons.bar_chart),
                  label: const Text('تغيير النوع'),
                ),
              ],
            ),
          ],
        ),
      );
    }
  }

  /// تحديث مخطط واحد فقط بدلاً من تحديث جميع المخططات
  void _updateSingleChart(String itemId) {
    final index = _dashboardItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      final item = _dashboardItems[index];

      try {
        // تنظيف المفاتيح غير المستخدمة أولاً
        _cleanupChartKeys();

        // التأكد من وجود مفتاح للمخطط
        getOrCreateChartKey(itemId);

        // تحديث محتوى العنصر بناءً على العنوان
        Widget content;

        // التحقق إذا كانت البطاقة هي بطاقة المهام الملغاة
        if (item.title == 'عدد المهام الملغاة') {
          content = _buildCancelledTasksContent();
        } else {
          // تحديث محتوى العنصر بناءً على نوع المخطط
          content = _buildChartContent(item);
        }

        // تحديث العنصر المحدد فقط
        _dashboardItems[index] = DashboardItem(
          id: item.id,
          title: item.title,
          content: content,
          position: item.position,
          size: item.size,
          chartType: item.chartType,
          filterOptions: item.filterOptions,
          gridSize: item.gridSize,
        );

        debugPrint('تم تحديث المخطط $itemId بنجاح');
      } catch (e) {
        debugPrint('فشل تحديث المخطط $itemId: $e');
      }
    }
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الدائري
  Map<String, double> _convertToPieChartData(Map<String, dynamic> data) {
    final Map<String, double> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        result[labels[i].toString()] = values[i].toDouble();
      }
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الخطي
  Map<String, List<FlSpot>> _convertToLineChartData(Map<String, dynamic> data) {
    final Map<String, List<FlSpot>> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      final List<FlSpot> spots = [];
      for (int i = 0; i < labels.length && i < values.length; i++) {
        spots.add(FlSpot(i.toDouble(), values[i].toDouble()));
      }

      result['data'] = spots;
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الراداري
  Map<String, List<double>> _convertToRadarChartData(
      Map<String, dynamic> data) {
    final Map<String, List<double>> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        if (values[i] is List) {
          // تحويل القائمة إلى قائمة من الأرقام العشرية
          final List<double> doubleList = [];
          for (var v in values[i] as List) {
            doubleList.add(v.toDouble());
          }
          result[labels[i].toString()] = doubleList;
        } else {
          // إذا كانت القيمة ليست قائمة، نضيف قائمة بقيمة واحدة
          result[labels[i].toString()] = [values[i].toDouble()];
        }
      }
    }

    return result;
  }

  /// التأكد من وجود الحد الأدنى من البيانات لمخطط الرادار (3 عناصر على الأقل)
  Map<String, List<double>> _ensureMinimumRadarData(
      Map<String, List<double>> data) {
    // إذا كانت البيانات فارغة، نعيد بيانات فارغة
    if (data.isEmpty) {
      return {};
    }

    // التأكد من أن كل سلسلة تحتوي على 3 قيم على الأقل
    for (final key in data.keys) {
      final List<double> values = data[key]!;
      if (values.length < 3) {
        // إضافة قيم إضافية إذا كان عدد القيم أقل من 3
        while (data[key]!.length < 3) {
          data[key]!.add(0.0); // قيمة صفرية
        }
      }
    }

    return data;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الفقاعي
  Map<String, List<BubbleData>> _convertToBubbleChartData(
      Map<String, dynamic> data) {
    final Map<String, List<BubbleData>> result = {};

    // إذا كانت البيانات تحتوي على قائمة من البيانات الفقاعية
    if (data.containsKey('bubbleData')) {
      final List<dynamic> bubbleDataList = data['bubbleData'];

      // تجميع البيانات حسب الفئة
      final Map<String, List<BubbleData>> categorizedData = {};

      for (var item in bubbleDataList) {
        final double x = item['x']?.toDouble() ?? 0.0;
        final double y = item['y']?.toDouble() ?? 0.0;
        final double size = item['size']?.toDouble() ?? 5.0;
        final String category = item['category'] ?? 'غير مصنف';
        final String name = item['name'] ?? '';

        if (!categorizedData.containsKey(category)) {
          categorizedData[category] = [];
        }

        categorizedData[category]!.add(
          BubbleData(
            x: x,
            y: y,
            size: size,
            label: name,
          ),
        );
      }

      return categorizedData;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      // إنشاء سلسلة بيانات واحدة
      final List<BubbleData> bubbles = [];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        bubbles.add(
          BubbleData(
            x: i.toDouble(),
            y: values[i].toDouble(),
            size: 5.0 + (values[i].toDouble() / 10),
            label: labels[i].toString(),
          ),
        );
      }

      result['data'] = bubbles;
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للخريطة الحرارية
  List<List<HeatmapCell>> _convertToHeatmapData(Map<String, dynamic> data) {
    final List<List<HeatmapCell>> result = [];

    // إذا كانت البيانات تحتوي على مصفوفة ثنائية الأبعاد
    if (data.containsKey('data')) {
      final List<dynamic> heatmapRows = data['data'];

      for (var row in heatmapRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'];

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            } else {
              // إذا كانت الخلية قيمة بسيطة
              cellRow.add(HeatmapCell(
                value: cell is num ? cell.toDouble() : 0.0,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على مصفوفة ثنائية الأبعاد بتنسيق آخر
    if (data.containsKey('heatmapData')) {
      final List<dynamic> heatmapRows = data['heatmapData'];

      for (var row in heatmapRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'];

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            } else {
              // إذا كانت الخلية قيمة بسيطة
              cellRow.add(HeatmapCell(
                value: cell is num ? cell.toDouble() : 0.0,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على بيانات منسقة
    if (data.containsKey('formattedData')) {
      final List<dynamic> formattedRows = data['formattedData'];

      for (var row in formattedRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'] ?? '${cell['value']}';

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    // نقوم بإنشاء مصفوفة 7×4 (أيام الأسبوع × الأسابيع)
    if (data.containsKey('labels') && data.containsKey('values')) {
      // إنشاء مصفوفة فارغة 4×7
      for (int i = 0; i < 4; i++) {
        final List<HeatmapCell> row = [];
        for (int j = 0; j < 7; j++) {
          row.add(HeatmapCell(value: 0.0));
        }
        result.add(row);
      }

      // توزيع القيم على المصفوفة
      final List<dynamic> values = data['values'];
      int index = 0;

      for (int i = 0; i < 4 && index < values.length; i++) {
        for (int j = 0; j < 7 && index < values.length; j++) {
          final value = values[index];
          result[i][j] = HeatmapCell(
            value: value is num ? value.toDouble() : 0.0,
          );
          index++;
        }
      }
    }

    // لا نضيف بيانات افتراضية، نعيد البيانات كما هي

    return result;
  }

  /// التأكد من صحة بيانات الخريطة الحرارية
  List<List<HeatmapCell>> _ensureValidHeatmapData(
      List<List<HeatmapCell>> data) {
    // إذا كانت البيانات فارغة، نضيف بيانات افتراضية
    if (data.isEmpty) {
      final List<List<HeatmapCell>> defaultData = [];

      // إنشاء مصفوفة 4×7 بقيم افتراضية
      for (int i = 0; i < 4; i++) {
        final List<HeatmapCell> row = [];
        for (int j = 0; j < 7; j++) {
          // إنشاء قيم عشوائية بين 1 و 10
          final double value = 1.0 + (i * 7 + j) % 10;
          row.add(HeatmapCell(value: value));
        }
        defaultData.add(row);
      }

      return defaultData;
    }

    // التأكد من أن كل صف يحتوي على 7 خلايا على الأقل
    for (int i = 0; i < data.length; i++) {
      while (data[i].length < 7) {
        data[i].add(HeatmapCell(value: 0.0));
      }
    }

    // التأكد من وجود 4 صفوف على الأقل
    while (data.length < 4) {
      final List<HeatmapCell> newRow = [];
      for (int j = 0; j < 7; j++) {
        newRow.add(HeatmapCell(value: 0.0));
      }
      data.add(newRow);
    }

    return data;
  }

  /// تحويل البيانات إلى تنسيق مناسب للخريطة الشجرية
  Map<String, double> _convertToTreemapData(Map<String, dynamic> data) {
    final Map<String, double> result = {};

    // إذا كانت البيانات تحتوي على قائمة من عناصر الخريطة الشجرية
    if (data.containsKey('items')) {
      final List<dynamic> treemapItems = data['items'];

      for (var item in treemapItems) {
        if (item is Map) {
          final String name = item['name'] ?? 'غير معروف';
          final double value =
              item['value'] is num ? item['value'].toDouble() : 0.0;

          result[name] = value;
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على قائمة من عناصر الخريطة الشجرية بتنسيق آخر
    if (data.containsKey('treemapData')) {
      final List<dynamic> treemapItems = data['treemapData'];

      for (var item in treemapItems) {
        if (item is Map) {
          final String label = item['label'] ?? item['name'] ?? 'غير معروف';
          final double value =
              item['value'] is num ? item['value'].toDouble() : 0.0;

          result[label] = value;
        }
      }

      return result;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        final String label = labels[i].toString();
        final double value = values[i] is num ? values[i].toDouble() : 0.0;

        result[label] = value;
      }
    }

    // لا نضيف بيانات افتراضية، نعيد البيانات كما هي

    return result;
  }

  /// تحديث فلتر المخطط
  Future<void> _updateChartFilter(
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions? advancedOptions) async {
    setState(() {
      _chartStartDates[chartKey] = startDate;
      _chartEndDates[chartKey] = endDate;
      _chartFilterTypes[chartKey] = filterType;

      if (advancedOptions != null) {
        _advancedFilterOptions[chartKey] = advancedOptions;
      }
    });

    // تحديث محتوى العناصر
    await _updateDashboardItemsContent();

    // حفظ معايير التصفية
    await _saveChartFilterOptions(chartKey);
  }

  /// تحديث نوع المخطط
  void _updateChartType(String itemId, ChartType chartType) {
    final index = _dashboardItems.indexWhere((item) => item.id == itemId);

    if (index != -1) {
      // إعادة تعيين مفتاح المخطط لتجنب مشاكل التكرار
      if (_chartKeys.containsKey(itemId)) {
        _chartKeys.remove(itemId);
        debugPrint('تم إزالة المفتاح القديم للمخطط: $itemId');
      }

      // التأكد من وجود خيارات التصفية المتقدمة
      final item = _dashboardItems[index];
      final advancedFilterOptions =
          item.filterOptions ?? AdvancedFilterOptions();

      setState(() {
        _dashboardItems[index] = DashboardItem(
          id: item.id,
          title: item.title,
          content: item.content,
          position: item.position,
          size: item.size,
          chartType: chartType,
          filterOptions: advancedFilterOptions,
          gridSize: item.gridSize,
        );

        // تحديث نوع المخطط في المتغيرات
        final chartKey = _getChartKeyFromTitle(item.title);
        _chartTypes[chartKey] = chartType;

        // لا نقوم بإنشاء مفتاح جديد عند تغيير نوع المخطط لتجنب مشاكل التكرار
        // نستخدم المفتاح الموجود أو ننشئ مفتاح ثابت إذا لم يكن موجودًا
        if (!_chartKeys.containsKey(itemId)) {
          _chartKeys[itemId] = GlobalKey(debugLabel: 'chart_${itemId}_fixed');
          debugPrint('تم إنشاء مفتاح جديد للمخطط: $itemId');
        } else {
          debugPrint('تم استخدام المفتاح الموجود للمخطط: $itemId');
        }
      });

      // تأخير قصير قبل تحديث المحتوى لضمان تطبيق التغييرات
      Future.delayed(const Duration(milliseconds: 100), () async {
        if (mounted) {
          // تحديث محتوى العناصر
          await _updateDashboardItemsContent();

          // حفظ التخطيط الجديد
          await _saveDashboardLayout();
        }
      });

      // عرض رسالة للمستخدم
      Get.snackbar(
        'تم تغيير نوع المخطط',
        'تم تغيير نوع المخطط بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// حفظ معايير التصفية
  Future<void> _saveChartFilterOptions(String chartKey) async {
    try {
      final dashboardRepository = Get.find<DashboardRepository>();

      // حفظ معايير التصفية العادية
      final filterData = {
        'startDate': _chartStartDates[chartKey]?.millisecondsSinceEpoch,
        'endDate': _chartEndDates[chartKey]?.millisecondsSinceEpoch,
        'filterType': _chartFilterTypes[chartKey]?.index,
        'chartType': _chartTypes[chartKey]?.index,
      };

      await dashboardRepository.saveChartData('${chartKey}_filter', filterData);

      // حفظ معايير التصفية المتقدمة
      if (_advancedFilterOptions.containsKey(chartKey)) {
        await dashboardRepository.saveChartData(
          '${chartKey}_advanced_filter',
          _advancedFilterOptions[chartKey]!.toJson(),
        );
      }

      debugPrint('تم حفظ معايير التصفية للمخطط: $chartKey');
    } catch (e) {
      debugPrint('خطأ في حفظ معايير التصفية: $e');
    }
  }

  /// حفظ تخطيط لوحة المعلومات
  Future<void> _saveDashboardLayout() async {
    try {
      // تحويل العناصر إلى JSON
      final layoutData = _dashboardItems.map((item) => item.toJson()).toList();

      // حفظ التخطيط باستخدام المتحكم
      await _dashboardLayoutController.saveDashboardLayout(layoutData);

      debugPrint('تم حفظ تخطيط لوحة المعلومات: ${layoutData.length} عنصر');
    } catch (e) {
      debugPrint('خطأ في حفظ تخطيط لوحة المعلومات: $e');
    }
  }

  /// بناء بيانات تدفق سير العمل
  Map<String, dynamic> _buildWorkflowData(List<dynamic> tasks) {
    // تحليل المهام حسب مراحل سير العمل
    final Map<String, int> workflowStages = {
      'جديدة': 0,
      'قيد التنفيذ': 0,
      'مراجعة': 0,
      'مكتملة': 0,
      'ملغاة': 0,
    };

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final status = task.status.toString().split('.').last;
          final statusName = _getStatusName(status);

          // تحديث العدد في المرحلة المناسبة
          if (workflowStages.containsKey(statusName)) {
            workflowStages[statusName] = workflowStages[statusName]! + 1;
          } else if (statusName == 'معلقة') {
            // إضافة المهام المعلقة إلى مرحلة المراجعة
            workflowStages['مراجعة'] = workflowStages['مراجعة']! + 1;
          }
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة لتدفق سير العمل: $e');
        }
      }
    }

    // ترتيب المراحل حسب تسلسل سير العمل
    final List<String> orderedStages = [
      'جديدة',
      'قيد التنفيذ',
      'مراجعة',
      'مكتملة',
      'ملغاة',
    ];

    final List<String> labels = [];
    final List<double> values = [];

    for (final stage in orderedStages) {
      if (workflowStages[stage]! > 0) {
        labels.add(stage);
        values.add(workflowStages[stage]!.toDouble());
      }
    }

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات الجدول الزمني للمشاريع
  Map<String, dynamic> _buildProjectTimelineData(List<dynamic> tasks) {
    // تجميع المهام حسب الأقسام والتواريخ
    final Map<String, List<Map<String, dynamic>>> departmentTasks = {};

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhere(
            (dept) => dept.id == departmentId,
            orElse: () =>
                Department(id: '', name: 'غير محدد', createdAt: DateTime.now()),
          );

          final departmentName = department.name;
          final startDate = task.createdAt;
          final endDate =
              task.dueDate ?? DateTime.now().add(const Duration(days: 7));

          if (!departmentTasks.containsKey(departmentName)) {
            departmentTasks[departmentName] = [];
          }

          departmentTasks[departmentName]!.add({
            'id': task.id,
            'title': task.title,
            'start': startDate,
            'end': endDate,
            'status': _getStatusName(task.status.toString().split('.').last),
          });
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة للجدول الزمني: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للجدول الزمني
    final List<Map<String, dynamic>> timelineItems = [];

    departmentTasks.forEach((department, tasksList) {
      for (final taskData in tasksList) {
        timelineItems.add({
          'department': department,
          'task': taskData['title'],
          'start': taskData['start'].millisecondsSinceEpoch,
          'end': taskData['end'].millisecondsSinceEpoch,
          'status': taskData['status'],
        });
      }
    });

    return {
      'items': timelineItems,
      'departments': departmentTasks.keys.toList(),
    };
  }

  /// بناء بيانات تدفق البيانات بين الأقسام
  Map<String, dynamic> _buildDataFlowData(List<dynamic> tasks) {
    // تحليل تدفق المهام بين الأقسام والحالات
    final Map<String, Map<String, int>> departmentToStatus = {};

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhere(
            (dept) => dept.id == departmentId,
            orElse: () =>
                Department(id: '', name: 'غير محدد', createdAt: DateTime.now()),
          );

          final departmentName = department.name;
          final status = task.status.toString().split('.').last;
          final statusName = _getStatusName(status);

          if (!departmentToStatus.containsKey(departmentName)) {
            departmentToStatus[departmentName] = {};
          }

          departmentToStatus[departmentName]![statusName] =
              (departmentToStatus[departmentName]![statusName] ?? 0) + 1;
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة لتدفق البيانات: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب لمخطط سانكي
    final List<Map<String, dynamic>> links = [];

    departmentToStatus.forEach((department, statusMap) {
      statusMap.forEach((status, count) {
        links.add({
          'source': department,
          'target': status,
          'value': count,
        });
      });
    });

    return {
      'links': links,
      'departments': departmentToStatus.keys.toList(),
      'statuses': ['جديدة', 'قيد التنفيذ', 'مكتملة', 'ملغاة', 'معلقة'],
    };
  }

  /// تصدير المخطط كصورة
  Future<void> _exportChart(String itemId, String title) async {
    try {
      // الحصول على المفتاح العام للمخطط
      final chartKey = _chartKeys[itemId];

      if (chartKey != null) {
        // تصدير المخطط كصورة
        final imagePath = await _chartExportService.exportToImage(
          itemId,
          title: title,
          globalKey: _chartKeys[itemId]!,
        );

        if (imagePath != null) {
          // عرض رسالة نجاح
          Get.snackbar(
            'تم التصدير بنجاح',
            'تم تصدير المخطط إلى: $imagePath',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      }
    } catch (e) {
      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير المخطط: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      debugPrint('خطأ في تصدير المخطط: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildDashboardContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  // متغير للتحكم في وضع التحرير
  bool _isEditMode = false;

  /// بناء قسم الترحيب والبطاقات الإحصائية
  Widget _buildWelcomeSection() {
    // الحصول على اسم المستخدم الحالي
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;
    final userName = currentUser?.name ?? 'المستخدم';

    // حساب الإحصائيات
    final totalTasks =
        _taskController.tasks.where((task) => !task.isDeleted).length;
    final completedTasks = _taskController.tasks
        .where((task) => !task.isDeleted && task.status == TaskStatus.completed)
        .length;
    final inProgressTasks = _taskController.tasks
        .where(
            (task) => !task.isDeleted && task.status == TaskStatus.inProgress)
        .length;
    final totalUsers = _userController.users.length;
    final totalDepartments = _departmentController.departments.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // قسم الترحيب
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(
                        red: Theme.of(context).primaryColor.r.toDouble(),
                        green: Theme.of(context).primaryColor.g.toDouble(),
                        blue: Theme.of(context).primaryColor.b.toDouble(),
                        alpha: 25.5, // 0.1 opacity = 25.5/255
                      ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  Icons.dashboard,
                  color: Theme.of(context).primaryColor,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، $userName',
                    style: AppStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'مرحباً بك في لوحة القيادة، هنا يمكنك متابعة أداء العمل',
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    currentUser?.role == UserRole.admin
                        ? 'الإحصائيات تعكس جميع مهام النظام'
                        : 'الإحصائيات تعكس المهام المخصصة لك فقط',
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.blue[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 24),

          // بطاقات الإحصائيات
          LayoutBuilder(
            builder: (context, constraints) {
              // تحديد عدد البطاقات في الصف بناءً على عرض الشاشة
              final screenWidth = constraints.maxWidth;
              int cardsPerRow = 5;

              if (screenWidth < 600) {
                cardsPerRow = 1;
              } else if (screenWidth < 900) {
                cardsPerRow = 2;
              } else if (screenWidth < 1200) {
                cardsPerRow = 3;
              } else if (screenWidth < 1500) {
                cardsPerRow = 4;
              }

              return Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  _buildStatCard(
                    'إجمالي المهام',
                    totalTasks.toString(),
                    Icons.task,
                    Colors.blue,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المهام المكتملة',
                    completedTasks.toString(),
                    Icons.check_circle,
                    Colors.green,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المهام قيد التنفيذ',
                    inProgressTasks.toString(),
                    Icons.pending_actions,
                    Colors.orange,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المستخدمين',
                    totalUsers.toString(),
                    Icons.people,
                    Colors.purple,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'الأقسام',
                    totalDepartments.toString(),
                    Icons.business,
                    Colors.teal,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  GestureDetector(
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.userDashboard);
                    },
                    child: _buildStatCard(
                      'لوحه تحكم المستخدم',
                      "         ---->",
                      Icons.analytics_outlined,
                      Colors.deepOrangeAccent,
                      constraints.maxWidth / cardsPerRow - 20,
                    ),
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 24),
          const Divider(),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              red: Colors.grey.r.toDouble(),
              green: Colors.grey.g.toDouble(),
              blue: Colors.grey.b.toDouble(),
              alpha: 25.5, // 0.1 opacity = 25.5/255
            ),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: color.withValues(
            red: color.r.toDouble(),
            green: color.g.toDouble(),
            blue: color.b.toDouble(),
            alpha: 76.5, // 0.3 opacity = 76.5/255
          ),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withValues(
                red: color.r.toDouble(),
                green: color.g.toDouble(),
                blue: color.b.toDouble(),
                alpha: 25.5, // 0.1 opacity = 25.5/255
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى لوحة المعلومات
  Widget _buildDashboardContent() {
    // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
    if (_isLoading || _isInitializing) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              _isInitializing
                  ? 'جاري تهيئة لوحة المعلومات...'
                  : 'جاري تحميل البيانات...',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    // عرض رسالة الخطأ إذا كان هناك خطأ
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // عرض رسالة إذا كانت قائمة العناصر فارغة
    if (_dashboardItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.dashboard_outlined,
              color: Colors.grey,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد عناصر في لوحة المعلومات',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _createDefaultDashboardLayout,
              child: const Text('إنشاء لوحة معلومات افتراضية'),
            ),
          ],
        ),
      );
    }

    // عرض لوحة المعلومات باستخدام المكون القابل للسحب والتحجيم
    return LayoutBuilder(
      builder: (context, constraints) {
        // التأكد من وجود أبعاد محددة
        if (constraints.maxWidth == 0 || constraints.maxHeight == 0) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // حساب الارتفاع الكلي المطلوب للمحتوى
        double maxHeight = 0;
        for (var item in _dashboardItems) {
          final itemBottom = item.position.dy + item.size.height;
          if (itemBottom > maxHeight) {
            maxHeight = itemBottom;
          }
        }

        // إضافة مساحة إضافية في الأسفل
        maxHeight += 100;

        // تحويل عناصر لوحة المعلومات إلى النموذج المطلوب للمكون القابل للسحب
        final draggableItems = _dashboardItems.map((item) {
          return draggable.DashboardItem(
            id: item.id,
            title: item.title,
            content: _buildChartContent(item),
            position: item.position,
            size: item.size,
          );
        }).toList();

        // التأكد من أن maxHeight ليست صفر أو لانهائية
        maxHeight = maxHeight <= 0 ? 800.0 : maxHeight;

        // التأكد من أن constraints.maxWidth ليست صفر أو لانهائية
        final safeWidth =
            constraints.maxWidth <= 0 ? 800.0 : constraints.maxWidth;

        // استخدام المكون القابل للسحب والتحجيم
        return SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              // إضافة قسم الترحيب في الأعلى
              _buildWelcomeSection(),

              // قسم المخططات
              Container(
                width: safeWidth,
                height: maxHeight,
                color: Colors
                    .transparent, // إضافة لون خلفية شفاف لضمان أن الحاوية تأخذ المساحة المطلوبة
                child: Stack(
                  clipBehavior:
                      Clip.none, // السماح للعناصر بالتجاوز خارج الحدود
                  children: [
                    // خلفية الشبكة في وضع التحرير
                    if (_isEditMode)
                      Positioned.fill(
                        child: CustomPaint(
                          painter: GridPainter(
                            gridSize: 20,
                            color: Colors.grey.withValues(
                                alpha: 51.0,
                                red: Colors.grey.r,
                                green: Colors.grey.g,
                                blue: Colors.grey.b),
                          ),
                        ),
                      ),

                    // عناصر لوحة المعلومات
                    ...draggableItems.map((item) {
                      return Positioned(
                        left: item.position.dx,
                        top: item.position.dy,
                        child: GestureDetector(
                          onTap: () {
                            final originalItem = _dashboardItems
                                .firstWhere((element) => element.id == item.id);
                            _showChartDetails(originalItem);
                          },
                          child: Stack(
                            children: [
                              ResizableChartContainer(
                                key: ValueKey(item.id),
                                title: item.title,
                                content: item.content,
                                initialHeight: item.size.height,
                                initialWidth: item.size.width,
                                isResizable: _isEditMode,
                                isDraggable: _isEditMode,
                                // إضافة معرف البطاقة ومعلومات البطاقات الأخرى لمنع التداخل
                                id: item.id,
                                otherItemsInfo:
                                    _dashboardItems.map((dashboardItem) {
                                  return {
                                    'id': dashboardItem.id,
                                    'position': {
                                      'dx': dashboardItem.position.dx,
                                      'dy': dashboardItem.position.dy,
                                    },
                                    'size': {
                                      'width': dashboardItem.size.width,
                                      'height': dashboardItem.size.height,
                                    },
                                  };
                                }).toList(),
                                onResize: (newSize) {
                                  // تحديث حجم العنصر
                                  final index = _dashboardItems.indexWhere(
                                      (element) => element.id == item.id);
                                  if (index != -1) {
                                    setState(() {
                                      final updatedItem = DashboardItem(
                                        id: _dashboardItems[index].id,
                                        title: _dashboardItems[index].title,
                                        content: _dashboardItems[index].content,
                                        position:
                                            _dashboardItems[index].position,
                                        size: newSize,
                                        chartType:
                                            _dashboardItems[index].chartType,
                                        filterOptions: _dashboardItems[index]
                                            .filterOptions,
                                        gridSize:
                                            _dashboardItems[index].gridSize,
                                      );
                                      _dashboardItems[index] = updatedItem;
                                    });

                                    // حفظ التخطيط الجديد
                                    _saveDashboardLayout();
                                  }
                                },
                                onMove: (newPosition) {
                                  // تحديث موقع العنصر
                                  final index = _dashboardItems.indexWhere(
                                      (element) => element.id == item.id);
                                  if (index != -1) {
                                    setState(() {
                                      final updatedItem = DashboardItem(
                                        id: _dashboardItems[index].id,
                                        title: _dashboardItems[index].title,
                                        content: _dashboardItems[index].content,
                                        position: newPosition,
                                        size: _dashboardItems[index].size,
                                        chartType:
                                            _dashboardItems[index].chartType,
                                        filterOptions: _dashboardItems[index]
                                            .filterOptions,
                                        gridSize:
                                            _dashboardItems[index].gridSize,
                                      );
                                      _dashboardItems[index] = updatedItem;
                                    });

                                    // حفظ التخطيط الجديد
                                    _saveDashboardLayout();
                                  }
                                },
                                onDoubleTap: () {
                                  // عرض تفاصيل المخطط عند النقر المزدوج
                                  final originalItem =
                                      _dashboardItems.firstWhere(
                                          (element) => element.id == item.id);
                                  _showChartDetails(originalItem);
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    }),

                    // زر تبديل وضع التحرير
                    Positioned(
                      right: 16,
                      bottom: 16,
                      child: FloatingActionButton(
                        heroTag: 'editModeToggle',
                        onPressed: () {
                          // اهتزاز عند النقر على زر التحرير
                          HapticFeedback.selectionClick();
                          _toggleEditMode();
                        },
                        tooltip: _isEditMode ? 'حفظ التخطيط' : 'تحرير التخطيط',
                        backgroundColor: _isEditMode
                            ? Theme.of(context).colorScheme.secondary
                            : Theme.of(context).primaryColor,
                        child: Icon(
                          _isEditMode ? Icons.save : Icons.edit,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// تبديل وضع التحرير
  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
    });

    // إذا تم الخروج من وضع التحرير، حفظ التخطيط
    if (!_isEditMode) {
      _saveDashboardLayout();

      // عرض رسالة تأكيد
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حفظ التخطيط بنجاح'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          width: 200,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } else {
      // عرض رسالة عند دخول وضع التحرير
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تفعيل وضع التحرير'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          width: 200,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

      // عرض تلميح للمستخدم حول كيفية استخدام وضع التحرير
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted && _isEditMode) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'اسحب البطاقات من الأعلى، واستخدم الزوايا للتحجيم'),
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              width: 300,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      });
    }

    // اهتزاز عند تغيير الوضع
    HapticFeedback.mediumImpact();
  }

  // تم إزالة دالة _handleLayoutChanged لأنها لم تعد مستخدمة

  // تم إزالة الدوال التي لم تعد مستخدمة بعد تنفيذ المكون الجديد
  // _buildDashboardItemWidget
  // _buildEditableDashboardItem
  // _buildStaticDashboardItem

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    // زر إضافة عنصر جديد
    return FloatingActionButton(
      heroTag: 'addItem',
      onPressed: _addNewDashboardItem,
      backgroundColor: Colors.green,
      tooltip: 'إضافة عنصر جديد',
      child: const Icon(Icons.add),
    );
  }

  /// إضافة عنصر جديد إلى لوحة المعلومات
  void _addNewDashboardItem() async {
    // تفعيل وضع التحرير إذا لم يكن مفعلاً
    if (!_isEditMode) {
      setState(() {
        _isEditMode = true;
      });

      // عرض رسالة عند دخول وضع التحرير
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تفعيل وضع التحرير'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          width: 200,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

      // اهتزاز عند تفعيل وضع التحرير
      HapticFeedback.mediumImpact();
    }

    // عرض مربع حوار Monday.com لإضافة عنصر جديد
    final result = await Get.dialog<DashboardWidget>(
      MondayStyleAddWidgetDialog(
        dashboardId: 'main_dashboard', // معرف لوحة المعلومات الرئيسية
      ),
    );

    if (result != null) {
      // الحصول على موقع افتراضي للعنصر الجديد
      final defaultPosition = Offset(
        8,
        _dashboardItems.isEmpty
            ? 20
            : _dashboardItems.last.position.dy +
                _dashboardItems.last.size.height +
                8,
      );

      // تحويل DashboardWidgetModel إلى DashboardItem
      final newItem = DashboardItem(
        id: result.id,
        title: result.title,
        content: Container(
          alignment: Alignment.center,
          child: const Text('جاري تحميل البيانات...'),
        ),
        position: defaultPosition,
        size: const Size(300, 350),
        chartType: _convertWidgetTypeToChartType(result.type),
        filterOptions: AdvancedFilterOptions(),
        gridSize: 20,
      );

      // إضافة العنصر إلى القائمة
      setState(() {
        _dashboardItems.add(newItem);
      });

      // تحديث محتوى العنصر الجديد
      await _updateDashboardItemsContent();

      // حفظ التخطيط الجديد
      _saveDashboardLayout();

      // اهتزاز عند إضافة عنصر جديد
      HapticFeedback.heavyImpact();

      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تمت إضافة "${result.title}" بنجاح'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            width: 250,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  // تم إزالة دالة _deleteDashboardItem لأنها لم تعد مستخدمة

  /// تحويل نوع العنصر من DashboardWidgetType إلى ChartType
  ChartType _convertWidgetTypeToChartType(DashboardWidgetType widgetType) {
    switch (widgetType) {
      case DashboardWidgetType.barChart:
        return ChartType.bar;
      case DashboardWidgetType.lineChart:
        return ChartType.line;
      case DashboardWidgetType.pieChart:
        return ChartType.pie;
      case DashboardWidgetType.table:
        return ChartType.bar; // استخدام مخطط شريطي كافتراضي للجداول
      case DashboardWidgetType.kpi:
        return ChartType.pie; // استخدام مخطط دائري كافتراضي لـ KPI
      default:
        return ChartType.bar; // افتراضي
    }
  }

  // تم حذف دالة _showChartTypeSelector لأنها لم تعد مستخدمة
  // يتم الآن استخدام مربع حوار Monday.com بدلاً منها

  // تم حذف دالة _showEditTitleDialog لأنها لم تعد مستخدمة
  // يتم الآن تعديل العنوان من خلال مربع حوار Monday.com

  /// عرض تفاصيل المخطط
  void _showChartDetails(DashboardItem item) {
    // عرض مربع حوار التفاصيل
    Get.dialog(
      Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(item.title,
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildChartContent(item),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إغلاق'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => _exportChart(item.id, item.title),
                      child: const Text('تصدير'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء مخطط قمعي (Funnel Chart)
  Widget _buildFunnelChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة لكل مرحلة
    final Map<String, Color> stageColors = {
      'جديدة': Colors.blue,
      'قيد التنفيذ': Colors.orange,
      'مراجعة': Colors.purple,
      'مكتملة': Colors.green,
      'ملغاة': Colors.red,
      'معلقة': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showValues': true,
      'showPercentages': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        stageColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.funnel,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.funnel,
              currentColors: stageColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // المخطط القمعي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات تدفق سير العمل من قاعدة البيانات
              final workflowData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);

              // تحويل البيانات إلى تنسيق مناسب للمخطط القمعي
              final Map<String, double> funnelData = {};

              if (workflowData.containsKey('labels') &&
                  workflowData.containsKey('values') &&
                  workflowData['labels'] is List &&
                  workflowData['values'] is List) {
                final labels = workflowData['labels'] as List;
                final values = workflowData['values'] as List;

                for (int i = 0; i < labels.length && i < values.length; i++) {
                  funnelData[labels[i].toString()] = values[i] is double
                      ? values[i]
                      : (values[i] as num).toDouble();
                }
              }

              debugPrint('بيانات المخطط القمعي: ${funnelData.length} عنصر');

              // التحقق من وجود بيانات
              if (funnelData.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.filter_alt,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد بيانات للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return EnhancedFunnelChart(
                data: funnelData,
                title: '',
                itemColors: stageColors,
                showValues: displayOptions['showValues'] ?? true,
                showPercentages: displayOptions['showPercentages'] ?? true,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط جانت (Gantt Chart)
  Widget _buildGanttChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة لكل حالة مهمة
    final Map<String, Color> taskStatusColors = {
      'جديدة': Colors.blue,
      'قيد التنفيذ': Colors.orange,
      'مراجعة': Colors.purple,
      'مكتملة': Colors.green,
      'ملغاة': Colors.red,
      'معلقة': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showCompletionPercentage': true,
      'showDates': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        taskStatusColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.gantt,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.gantt,
              currentColors: taskStatusColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // مخطط جانت
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات الجدول الزمني من قاعدة البيانات
              final timelineData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);

              // قائمة المهام لمخطط جانت
              final List<GanttTask> ganttTasks = [];

              // التحقق من وجود بيانات
              if (timelineData.isEmpty ||
                  !timelineData.containsKey('items') ||
                  timelineData['items'] is! List ||
                  (timelineData['items'] as List).isEmpty) {
                // إذا لم تكن هناك بيانات، نحاول الحصول على المهام مباشرة من المتحكم
                final tasks = _taskController.tasks.where((task) {
                  // تصفية المهام حسب التاريخ
                  if (startDate != null && task.createdAt.isBefore(startDate)) {
                    return false;
                  }
                  if (endDate != null && task.createdAt.isAfter(endDate)) {
                    return false;
                  }
                  // تصفية المهام حسب القسم
                  if (advancedFilterOptions.departmentIds != null &&
                      advancedFilterOptions.departmentIds!.isNotEmpty &&
                      !advancedFilterOptions.departmentIds!
                          .contains(task.departmentId)) {
                    return false;
                  }
                  return true;
                }).toList();

                debugPrint('عدد المهام لمخطط جانت: ${tasks.length}');

                if (tasks.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.event_busy,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد مهام للعرض',
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // تحويل المهام إلى نموذج GanttTask
                for (final task in tasks) {
                  // تحديد تاريخ البداية والنهاية
                  final taskStartDate = task.startDate ?? task.createdAt;
                  final taskEndDate = task.completedAt ??
                      task.dueDate ??
                      taskStartDate.add(const Duration(days: 3));

                  // تحديد لون المهمة بناءً على الحالة
                  final statusName = task.status.toString().split('.').last;
                  final statusKey = _getStatusName(statusName);
                  final taskColor = taskStatusColors[statusKey] ??
                      _getTaskStatusColor(task.status);

                  // إضافة مهمة جانت
                  ganttTasks.add(GanttTask(
                    id: task.id,
                    title: task.title,
                    startDate: taskStartDate,
                    endDate: taskEndDate,
                    completionPercentage: task.completionPercentage,
                    color: taskColor,
                  ));
                }
              } else {
                // استخدام البيانات من قاعدة البيانات
                final items = timelineData['items'] as List;

                for (final item in items) {
                  try {
                    // تحويل التواريخ من timestamp إلى DateTime
                    final startTimestamp = item['start'] as int;
                    final endTimestamp = item['end'] as int;
                    final taskStartDate =
                        DateTime.fromMillisecondsSinceEpoch(startTimestamp);
                    final taskEndDate =
                        DateTime.fromMillisecondsSinceEpoch(endTimestamp);

                    // تحديد لون المهمة بناءً على الحالة
                    final status = item['status'] as String? ?? 'قيد التنفيذ';
                    final taskColor = taskStatusColors[status] ?? Colors.blue;

                    // إضافة مهمة جانت
                    ganttTasks.add(GanttTask(
                      id: item['id'] as String? ?? 'task_${ganttTasks.length}',
                      title: item['task'] as String? ?? 'مهمة بدون عنوان',
                      startDate: taskStartDate,
                      endDate: taskEndDate,
                      completionPercentage:
                          item['completion'] as double? ?? 0.0,
                      color: taskColor,
                    ));
                  } catch (e) {
                    debugPrint('خطأ في معالجة مهمة جانت: $e');
                  }
                }
              }

              // التحقق من وجود مهام بعد المعالجة
              if (ganttTasks.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.event_busy,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مهام للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              // عرض مخطط جانت
              return GanttChart(
                tasks: ganttTasks,
                title: '',
                startDate: startDate,
                endDate: endDate,
                titleColumnWidth: 200,
                rowHeight: 40,
                showCompletionPercentage:
                    displayOptions['showCompletionPercentage'] ?? true,
                showDates: displayOptions['showDates'] ?? true,
                onTaskTap: (ganttTask) {
                  // عرض تفاصيل المهمة
                  _showTaskDetailsFromGanttTask(ganttTask);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  /// عرض تفاصيل المهمة من مهمة جانت
  void _showTaskDetailsFromGanttTask(GanttTask ganttTask) {
    // محاولة العثور على المهمة الأصلية في قائمة المهام
    final task = _taskController.tasks.firstWhereOrNull(
      (task) => task.id == ganttTask.id,
    );

    if (task != null) {
      // إذا وجدنا المهمة، نعرض تفاصيلها
      _showTaskDetailsDialog(task);
    } else {
      // إذا لم نجد المهمة، نعرض تفاصيل مهمة جانت فقط
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(ganttTask.title),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المهمة
                _buildInfoRow(Icons.calendar_today, 'تاريخ البدء',
                    DateFormat('yyyy-MM-dd').format(ganttTask.startDate)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.event, 'تاريخ الانتهاء',
                    DateFormat('yyyy-MM-dd').format(ganttTask.endDate)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.percent, 'نسبة الإنجاز',
                    '${ganttTask.completionPercentage.toStringAsFixed(1)}%'),

                // شريط التقدم
                const SizedBox(height: 16),
                const Text('التقدم:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: ganttTask.completionPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(ganttTask.completionPercentage / 100),
                  ),
                  minHeight: 10,
                  borderRadius: BorderRadius.circular(5),
                ),

                // معلومات إضافية
                if (ganttTask.additionalData != null &&
                    ganttTask.additionalData!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text('معلومات إضافية:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...ganttTask.additionalData!.entries.map((entry) =>
                      _buildInfoRow(Icons.info_outline, entry.key,
                          entry.value.toString())),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    }
  }

  /// بناء صف معلومات مع لون اختياري للقيمة
  Widget _buildInfoRow(IconData icon, String label, String value,
      {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text('$label:', style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? Colors.black87,
                fontWeight:
                    valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون التقدم بناءً على النسبة
  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red;
    } else if (progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// عرض تفاصيل المهمة في مربع حوار
  void _showTaskDetailsDialog(Task task) {
    // الحصول على اسم القسم
    final department = _departmentController.departments.firstWhereOrNull(
      (dept) => dept.id == task.departmentId,
    );
    final departmentName = department?.name ?? 'غير محدد';

    // الحصول على اسم المستخدم المسؤول
    final assignedUser = _userController.users.firstWhereOrNull(
      (user) => user.id == task.assigneeId,
    );
    final assignedUserName = assignedUser?.name ?? 'غير محدد';

    // الحصول على اسم الحالة
    final statusName = _getStatusName(task.status.toString().split('.').last);

    // الحصول على لون الحالة
    final statusColor = _getTaskStatusColor(task.status);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(task.title)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // الوصف
              if (task.description.isNotEmpty) ...[
                const Text('الوصف:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(task.description),
                ),
                const SizedBox(height: 16),
              ],

              // معلومات المهمة
              _buildInfoRow(Icons.category, 'القسم', departmentName),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.person, 'المسؤول', assignedUserName),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.flag, 'الحالة', statusName,
                  valueColor: statusColor),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.percent, 'نسبة الإكمال',
                  '${task.completionPercentage.toStringAsFixed(0)}%'),

              // شريط التقدم
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: task.completionPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getProgressColor(task.completionPercentage / 100),
                ),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),

              // التواريخ
              const SizedBox(height: 16),
              const Text('التواريخ:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.date_range, 'تاريخ الإنشاء',
                  DateFormat('yyyy-MM-dd').format(task.createdAt)),
              if (task.startDate != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.calendar_today, 'تاريخ البدء',
                    DateFormat('yyyy-MM-dd').format(task.startDate!)),
              ],
              if (task.dueDate != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.event, 'تاريخ الاستحقاق',
                    DateFormat('yyyy-MM-dd').format(task.dueDate!)),
              ],
              if (task.completedAt != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.check_circle, 'تاريخ الإكمال',
                    DateFormat('yyyy-MM-dd').format(task.completedAt!)),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون حالة المهمة
  Color _getTaskStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.blue;
      case TaskStatus.inProgress:
        return Colors.orange;
      case TaskStatus.waitingForInfo:
        return Colors.purple;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// عرض مربع حوار تخصيص المخطط
  void _showChartCustomizationDialog({
    required BuildContext context,
    required ChartType chartType,
    required Map<String, Color> currentColors,
    required Map<String, bool> currentDisplayOptions,
    required Function(Map<String, Color>) onColorsChanged,
    required Function(Map<String, bool>) onDisplayOptionsChanged,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: ChartCustomizationPanel(
            chartType: chartType,
            currentColors: currentColors,
            currentDisplayOptions: currentDisplayOptions,
            onColorsChanged: onColorsChanged,
            onDisplayOptionsChanged: onDisplayOptionsChanged,
          ),
        ),
      ),
    );
  }

  /// الحصول على ألوان المخطط المخزنة
  Map<String, Color> _getChartColors(String chartId) {
    final Map<String, Color> colors = {};

    // في التطبيق الحقيقي، يمكن استرجاع الألوان من التخزين المحلي أو قاعدة البيانات
    // هنا نستخدم مثالاً بسيطاً

    return colors;
  }

  /// حفظ ألوان المخطط
  void _saveChartColors(String chartId, Map<String, Color> colors) {
    // في التطبيق الحقيقي، يمكن حفظ الألوان في التخزين المحلي أو قاعدة البيانات
    debugPrint('تم حفظ ألوان المخطط $chartId: $colors');
  }

  /// الحصول على خيارات عرض المخطط المخزنة
  Map<String, bool> _getChartDisplayOptions(String chartId) {
    final Map<String, bool> options = {};

    // في التطبيق الحقيقي، يمكن استرجاع الخيارات من التخزين المحلي أو قاعدة البيانات
    // هنا نستخدم مثالاً بسيطاً

    return options;
  }

  /// حفظ خيارات عرض المخطط
  void _saveChartDisplayOptions(String chartId, Map<String, bool> options) {
    // في التطبيق الحقيقي، يمكن حفظ الخيارات في التخزين المحلي أو قاعدة البيانات
    debugPrint('تم حفظ خيارات عرض المخطط $chartId: $options');
  }

  /// بناء مخطط سانكي (Sankey Chart)
  Widget _buildSankeyChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة للأقسام
    final Map<String, Color> departmentColors = {
      'الإدارة': Colors.blue,
      'المبيعات': Colors.green,
      'التسويق': Colors.orange,
      'تطوير المنتجات': Colors.purple,
      'خدمة العملاء': Colors.teal,
      'الموارد البشرية': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showLinkValues': false,
      'showNodeNames': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        departmentColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.sankey,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.sankey,
              currentColors: departmentColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // مخطط سانكي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات تدفق البيانات من قاعدة البيانات
              final flowData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);
              debugPrint('بيانات مخطط سانكي: ${flowData.length} عنصر');

              // تحويل البيانات إلى روابط سانكي
              final links = <SankeyLink>[];

              // التحقق من وجود بيانات
              if (flowData.isEmpty || !flowData.containsKey('links')) {
                // إذا لم تكن هناك بيانات، نحاول إنشاء بيانات من المهام
                final tasks = _taskController.tasks.where((task) {
                  // تصفية المهام حسب التاريخ
                  if (startDate != null && task.createdAt.isBefore(startDate)) {
                    return false;
                  }
                  if (endDate != null && task.createdAt.isAfter(endDate)) {
                    return false;
                  }
                  return true;
                }).toList();

                if (tasks.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.account_tree,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات للعرض',
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // تحليل تدفق المهام بين الأقسام والحالات
                final Map<String, Map<String, int>> departmentToStatus = {};

                for (final task in tasks) {
                  try {
                    final departmentId = task.departmentId;
                    final department =
                        _departmentController.departments.firstWhere(
                      (dept) => dept.id == departmentId,
                      orElse: () => Department(
                          id: '', name: 'غير محدد', createdAt: DateTime.now()),
                    );

                    final departmentName = department.name;
                    final status = task.status.toString().split('.').last;
                    final statusName = _getStatusName(status);

                    if (!departmentToStatus.containsKey(departmentName)) {
                      departmentToStatus[departmentName] = {};
                    }

                    departmentToStatus[departmentName]![statusName] =
                        (departmentToStatus[departmentName]![statusName] ?? 0) +
                            1;
                  } catch (e) {
                    debugPrint('خطأ في معالجة المهمة لمخطط سانكي: $e');
                  }
                }

                // تحويل البيانات إلى روابط سانكي
                departmentToStatus.forEach((department, statusMap) {
                  statusMap.forEach((status, count) {
                    links.add(SankeyLink(
                      source: department,
                      target: status,
                      value: count.toDouble(),
                    ));
                  });
                });
              } else {
                // استخراج الروابط من البيانات
                if (flowData.containsKey('links')) {
                  final linksData = flowData['links'] as List<dynamic>;
                  for (final link in linksData) {
                    links.add(SankeyLink(
                      source: link['source'] as String,
                      target: link['target'] as String,
                      value: (link['value'] as num).toDouble(),
                    ));
                  }
                }
              }

              // التحقق من وجود روابط بعد المعالجة
              if (links.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.account_tree,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد بيانات للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              // تحديد ألوان العقد
              final Map<String, Color> nodeColors = {};

              // استخراج أسماء العقد من الروابط
              final Set<String> nodeNames = {};
              for (final link in links) {
                nodeNames.add(link.source);
                nodeNames.add(link.target);
              }

              // تعيين ألوان للعقد
              for (final nodeName in nodeNames) {
                nodeColors[nodeName] = departmentColors[nodeName] ??
                    Colors.primaries[nodeNames.toList().indexOf(nodeName) %
                        Colors.primaries.length];
              }

              return EnhancedSankeyChart(
                links: links,
                title: '',
                chartType: ChartType.sankey,
                advancedFilterOptions: advancedFilterOptions,
                nodeColors: nodeColors,
                showLinkValues: displayOptions['showLinkValues'] ?? false,
                showNodeNames: displayOptions['showNodeNames'] ?? true,
              );
            },
          ),
        ),
      ],
    );
  }
}
