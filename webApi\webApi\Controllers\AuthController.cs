using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using webApi.Models.Auth;
using webApi.Services;

namespace webApi.Controllers;

/// <summary>
/// وحدة تحكم المصادقة والتفويض
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, IJwtService jwtService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _jwtService = jwtService;
        _logger = logger;
    }

    /// <summary>
    /// تسجيل الدخول
    /// </summary>
    /// <param name="request">بيانات تسجيل الدخول</param>
    /// <returns>استجابة المصادقة مع الرموز</returns>
    /// <response code="200">تم تسجيل الدخول بنجاح</response>
    /// <response code="400">بيانات غير صحيحة</response>
    /// <response code="401">فشل في المصادقة</response>
    [HttpPost("login")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.LoginAsync(request);

        if (!result.Success)
        {
            return Unauthorized(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// التسجيل
    /// </summary>
    /// <param name="request">بيانات التسجيل</param>
    /// <returns>استجابة المصادقة مع الرموز</returns>
    /// <response code="201">تم التسجيل بنجاح</response>
    /// <response code="400">بيانات غير صحيحة</response>
    /// <response code="409">المستخدم موجود بالفعل</response>
    [HttpPost("register")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<AuthResponse>> Register([FromBody] RegisterRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.RegisterAsync(request);

        if (!result.Success)
        {
            if (result.Message.Contains("مستخدم") || result.Message.Contains("البريد"))
            {
                return Conflict(result);
            }
            return BadRequest(result);
        }

        return CreatedAtAction(nameof(GetProfile), result);
    }

    /// <summary>
    /// تحديث الرمز
    /// </summary>
    /// <param name="request">طلب تحديث الرمز</param>
    /// <returns>رمز وصول جديد</returns>
    /// <response code="200">تم تحديث الرمز بنجاح</response>
    /// <response code="400">رمز التحديث غير صحيح</response>
    /// <response code="401">رمز التحديث منتهي الصلاحية</response>
    [HttpPost("refresh-token")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthResponse>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.RefreshTokenAsync(request);

        if (!result.Success)
        {
            return Unauthorized(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// تسجيل الخروج
    /// </summary>
    /// <returns>نتيجة العملية</returns>
    /// <response code="200">تم تسجيل الخروج بنجاح</response>
    /// <response code="401">غير مصرح</response>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Logout()
    {
        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return Unauthorized();
        }

        // الحصول على رمز التحديث من الرأس أو الجسم
        var refreshToken = Request.Headers["RefreshToken"].FirstOrDefault() ?? "";

        var result = await _authService.LogoutAsync(userId, refreshToken);

        if (result)
        {
            return Ok(new { message = "تم تسجيل الخروج بنجاح" });
        }

        return BadRequest(new { message = "فشل في تسجيل الخروج" });
    }

    /// <summary>
    /// الحصول على الملف الشخصي للمستخدم الحالي
    /// </summary>
    /// <returns>معلومات المستخدم</returns>
    /// <response code="200">تم الحصول على الملف الشخصي بنجاح</response>
    /// <response code="401">غير مصرح</response>
    /// <response code="404">المستخدم غير موجود</response>
    [HttpGet("profile")]
    [Authorize]
    [ProducesResponseType(typeof(UserInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserInfo>> GetProfile()
    {
        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return Unauthorized();
        }

        var userInfo = await _authService.ValidateUserAsync(userId);
        if (userInfo == null)
        {
            return NotFound(new { message = "المستخدم غير موجود" });
        }

        return Ok(userInfo);
    }

    /// <summary>
    /// تغيير كلمة المرور
    /// </summary>
    /// <param name="request">بيانات تغيير كلمة المرور</param>
    /// <returns>نتيجة العملية</returns>
    /// <response code="200">تم تغيير كلمة المرور بنجاح</response>
    /// <response code="400">بيانات غير صحيحة</response>
    /// <response code="401">غير مصرح</response>
    [HttpPost("change-password")]
    [Authorize]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthResponse>> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return Unauthorized();
        }

        var result = await _authService.ChangePasswordAsync(userId, request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// نسيان كلمة المرور
    /// </summary>
    /// <param name="request">بيانات نسيان كلمة المرور</param>
    /// <returns>نتيجة العملية</returns>
    /// <response code="200">تم إرسال رسالة إعادة التعيين</response>
    /// <response code="400">بيانات غير صحيحة</response>
    [HttpPost("forgot-password")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<AuthResponse>> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.ForgotPasswordAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// التحقق من صحة الرمز
    /// </summary>
    /// <returns>معلومات المستخدم إذا كان الرمز صحيح</returns>
    /// <response code="200">الرمز صحيح</response>
    /// <response code="401">الرمز غير صحيح أو منتهي الصلاحية</response>
    [HttpGet("validate-token")]
    [Authorize]
    [ProducesResponseType(typeof(UserInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<UserInfo>> ValidateToken()
    {
        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return Unauthorized();
        }

        var userInfo = await _authService.ValidateUserAsync(userId);
        if (userInfo == null)
        {
            return Unauthorized();
        }

        return Ok(userInfo);
    }

    /// <summary>
    /// تحديث حالة الاتصال
    /// </summary>
    /// <param name="isOnline">حالة الاتصال</param>
    /// <returns>نتيجة العملية</returns>
    /// <response code="200">تم تحديث حالة الاتصال بنجاح</response>
    /// <response code="401">غير مصرح</response>
    [HttpPost("update-online-status")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> UpdateOnlineStatus([FromBody] bool isOnline)
    {
        var userIdClaim = User.FindFirst("UserId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return Unauthorized();
        }

        var result = await _authService.UpdateOnlineStatusAsync(userId, isOnline);

        if (result)
        {
            return Ok(new { message = "تم تحديث حالة الاتصال بنجاح" });
        }

        return BadRequest(new { message = "فشل في تحديث حالة الاتصال" });
    }

    /// <summary>
    /// اختبار الاتصال مع API
    /// </summary>
    /// <returns>رسالة تأكيد الاتصال</returns>
    /// <response code="200">الاتصال يعمل بشكل صحيح</response>
    [HttpGet("test")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult TestConnection()
    {
        return Ok(new
        {
            message = "API يعمل بشكل صحيح",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            status = "healthy"
        });
    }
}
