import 'user_model.dart';

/// نموذج طلب تسجيل الدخول
class LoginRequest {
  final String usernameOrEmail;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.usernameOrEmail,
    required this.password,
    this.rememberMe = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'usernameOrEmail': usernameOrEmail,
      'password': password,
      'rememberMe': rememberMe,
    };
  }
}

/// نموذج طلب التسجيل
class RegisterRequest {
  final String name;
  final String email;
  final String? username;
  final String password;
  final String confirmPassword;
  final String? firstName;
  final String? lastName;
  final int? departmentId;
  final UserRole role;

  const RegisterRequest({
    required this.name,
    required this.email,
    this.username,
    required this.password,
    required this.confirmPassword,
    this.firstName,
    this.lastName,
    this.departmentId,
    this.role = UserRole.user,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'name': name,
      'email': email,
      'username': username,
      'password': password,
      'confirmPassword': confirmPassword,
      'firstName': firstName,
      'lastName': lastName,
      'departmentId': departmentId,
      'role': role.value,
    };
  }
}

/// نموذج طلب تحديث الرمز
class RefreshTokenRequest {
  final String refreshToken;

  const RefreshTokenRequest({
    required this.refreshToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'refreshToken': refreshToken,
    };
  }
}

/// نموذج طلب تغيير كلمة المرور
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }
}

/// نموذج طلب نسيان كلمة المرور
class ForgotPasswordRequest {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

/// نموذج استجابة المصادقة
class AuthResponse {
  final bool success;
  final String message;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final String tokenType;
  final UserInfo? user;

  const AuthResponse({
    required this.success,
    required this.message,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.tokenType = 'Bearer',
    this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      tokenType: json['tokenType'] as String? ?? 'Bearer',
      user: json['user'] != null 
          ? UserInfo.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt?.toIso8601String(),
      'tokenType': tokenType,
      'user': user?.toJson(),
    };
  }

  /// التحقق من صحة الاستجابة ووجود الرموز
  bool get isValid => success && accessToken != null && user != null;

  /// التحقق من انتهاء صلاحية الرمز
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }
}

/// نموذج بيانات الجلسة المحفوظة محلياً
class SessionData {
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;
  final UserInfo user;
  final DateTime savedAt;

  const SessionData({
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
    required this.user,
    required this.savedAt,
  });

  factory SessionData.fromJson(Map<String, dynamic> json) {
    return SessionData(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      user: UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      savedAt: DateTime.parse(json['savedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
      'user': user.toJson(),
      'savedAt': savedAt.toIso8601String(),
    };
  }

  /// التحقق من انتهاء صلاحية الجلسة
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// التحقق من الحاجة لتحديث الرمز (قبل انتهاء الصلاحية بـ 5 دقائق)
  bool get needsRefresh {
    final refreshTime = expiresAt.subtract(const Duration(minutes: 5));
    return DateTime.now().isAfter(refreshTime);
  }
}
