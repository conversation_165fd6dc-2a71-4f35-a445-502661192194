import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import 'report_data_provider.dart';

import 'export/pdf_export_service.dart';
import 'export/excel_export_service.dart';
import 'export/csv_export_service.dart';
import 'export/json_export_service.dart';

/// خدمة التقارير المحسنة
///
/// توفر وظائف لإنشاء وتنفيذ وإدارة التقارير المحسنة
class ReportService extends GetxService {
  final ReportRepository _reportRepository = ReportRepository();
  final UserRepository _userRepository = UserRepository();
  final DepartmentRepository _departmentRepository = DepartmentRepository();
  final ReportDataProvider _dataProvider = ReportDataProvider();
  final ReportCacheService _cacheService = ReportCacheService();
  final Uuid _uuid = const Uuid();

  // خدمات التصدير
  final PdfExportService _pdfExportService = PdfExportService();
  final ExcelExportService _excelExportService = ExcelExportService();
  final CsvExportService _csvExportService = CsvExportService();
  final JsonExportService _jsonExportService = JsonExportService();

  /// إنشاء تقرير جديد
  Future<EnhancedReport> createReport({
    required String title,
    String? description,
    required ReportType type,
    required String createdById,
    required List<ReportFilter> filters,
    required List<ReportVisualization> visualizations,
    required ReportPeriod period,
    DateTime? customStartDate,
    DateTime? customEndDate,
    bool isShared = false,
    List<String>? sharedWithUserIds,
    bool isFavorite = false,
    int? displayOrder,
    Color? color,
    IconData? icon,
  }) async {
    final report = EnhancedReport(
      id: _uuid.v4(),
      title: title,
      description: description,
      type: type,
      createdById: createdById,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      filters: filters,
      visualizations: visualizations,
      period: period,
      customStartDate: customStartDate,
      customEndDate: customEndDate,
      isShared: isShared,
      sharedWithUserIds: sharedWithUserIds,
      isFavorite: isFavorite,
      displayOrder: displayOrder,
      color: color,
      icon: icon,
    );

    // حفظ التقرير في قاعدة البيانات
    await _saveReportToDatabase(report);

    return report;
  }

  /// حفظ التقرير في قاعدة البيانات
  Future<void> _saveReportToDatabase(EnhancedReport report) async {
    try {
      // تحويل التقرير إلى تنسيق قاعدة البيانات
      final Map<String, dynamic> reportMap = {
        'id': report.id,
        'title': report.title,
        'description': report.description,
        'type': report.type.index,
        'createdById': report.createdById,
        'createdAt': report.createdAt.millisecondsSinceEpoch,
        'updatedAt': report.updatedAt?.millisecondsSinceEpoch,
        'filters': jsonEncode(report.filters.map((f) => f.toMap()).toList()),
        'visualizations': jsonEncode(report.visualizations.map((v) => v.toMap()).toList()),
        'period': report.period.index,
        'customStartDate': report.customStartDate?.millisecondsSinceEpoch,
        'customEndDate': report.customEndDate?.millisecondsSinceEpoch,
        'filePath': report.filePath,
        'format': report.format?.index,
        'fileSize': report.fileSize,
        'lastExportedAt': report.lastExportedAt?.millisecondsSinceEpoch,
        'isShared': report.isShared ? 1 : 0,
        'sharedWithUserIds': report.sharedWithUserIds?.join(','),
        'isFavorite': report.isFavorite ? 1 : 0,
        'displayOrder': report.displayOrder,
        'colorValue': report.color?.value,
        'iconCodePoint': report.icon?.codePoint,
        'iconFontFamily': report.icon?.fontFamily,
      };

      // حفظ التقرير
      await _reportRepository.saveEnhancedReport(reportMap);
    } catch (e) {
      debugPrint('خطأ في حفظ التقرير: $e');
      rethrow;
    }
  }

  /// تحديث تقرير موجود
  Future<EnhancedReport> updateReport(EnhancedReport report) async {
    final updatedReport = report.copyWith(
      updatedAt: DateTime.now(),
    );

    await _saveReportToDatabase(updatedReport);
    return updatedReport;
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      return await _reportRepository.deleteEnhancedReport(reportId);
    } catch (e) {
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    }
  }

  /// الحصول على تقرير بواسطة المعرف
  Future<EnhancedReport?> getReportById(String reportId) async {
    try {
      final reportMap = await _reportRepository.getEnhancedReportById(reportId);
      if (reportMap == null) {
        return null;
      }

      return _mapToEnhancedReport(reportMap);
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير: $e');
      return null;
    }
  }

  /// الحصول على جميع التقارير
  Future<List<EnhancedReport>> getAllReports() async {
    try {
      final reportMaps = await _reportRepository.getAllEnhancedReports();
      return reportMaps.map((map) => _mapToEnhancedReport(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      return [];
    }
  }

  /// الحصول على التقارير الخاصة بمستخدم
  Future<List<EnhancedReport>> getReportsByUser(String userId) async {
    try {
      final reportMaps = await _reportRepository.getEnhancedReportsByUser(userId);
      return reportMaps.map((map) => _mapToEnhancedReport(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المستخدم: $e');
      return [];
    }
  }

  /// الحصول على التقارير المشتركة
  Future<List<EnhancedReport>> getSharedReports(String userId) async {
    try {
      final reportMaps = await _reportRepository.getSharedEnhancedReports(userId);
      return reportMaps.map((map) => _mapToEnhancedReport(map)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير المشتركة: $e');
      return [];
    }
  }

  /// تنفيذ تقرير
  Future<ReportResult> executeReport(String reportId) async {
    final startTime = DateTime.now().millisecondsSinceEpoch;

    try {
      // التحقق من وجود نتيجة مخزنة مؤقتًا
      final cachedResult = await _cacheService.getCachedResult(reportId);
      if (cachedResult != null) {
        return cachedResult;
      }

      // الحصول على التقرير
      final report = await getReportById(reportId);
      if (report == null) {
        return ReportResult(
          reportId: reportId,
          isSuccess: false,
          errorMessages: ['التقرير غير موجود'],
          executedAt: DateTime.now(),
        );
      }

      // تحديد فترة التقرير
      final DateTimeRange dateRange = _getReportDateRange(report);

      // تنفيذ التقرير حسب النوع
      final result = await _dataProvider.executeReport(
        report: report,
        dateRange: dateRange,
      );

      // حساب وقت التنفيذ
      final endTime = DateTime.now().millisecondsSinceEpoch;
      final executionTime = endTime - startTime;

      // إنشاء نتيجة التقرير
      final reportResult = ReportResult(
        reportId: reportId,
        isSuccess: true,
        data: result.data,
        summary: result.summary,
        totalRecords: result.data?.length,
        executionTime: executionTime,
        executedAt: DateTime.now(),
        visualizationData: result.visualizationData,
      );

      // تخزين النتيجة مؤقتًا
      await _cacheService.cacheResult(reportId, reportResult);

      return reportResult;
    } catch (e) {
      debugPrint('خطأ في تنفيذ التقرير: $e');
      return ReportResult(
        reportId: reportId,
        isSuccess: false,
        errorMessages: ['حدث خطأ أثناء تنفيذ التقرير: ${e.toString()}'],
        executionTime: DateTime.now().millisecondsSinceEpoch - startTime,
        executedAt: DateTime.now(),
      );
    }
  }

  /// تحديد نطاق تاريخ التقرير
  DateTimeRange _getReportDateRange(EnhancedReport report) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (report.period) {
      case ReportPeriod.today:
        return DateTimeRange(start: today, end: today.add(const Duration(days: 1)).subtract(const Duration(seconds: 1)));
      case ReportPeriod.yesterday:
        final yesterday = today.subtract(const Duration(days: 1));
        return DateTimeRange(start: yesterday, end: today.subtract(const Duration(seconds: 1)));
      case ReportPeriod.thisWeek:
        // بداية الأسبوع (الأحد)
        final startOfWeek = today.subtract(Duration(days: today.weekday % 7));
        return DateTimeRange(start: startOfWeek, end: now);
      case ReportPeriod.lastWeek:
        final startOfLastWeek = today.subtract(Duration(days: today.weekday % 7 + 7));
        final endOfLastWeek = today.subtract(Duration(days: today.weekday % 7 + 1));
        return DateTimeRange(start: startOfLastWeek, end: endOfLastWeek);
      case ReportPeriod.thisMonth:
        final startOfMonth = DateTime(now.year, now.month, 1);
        return DateTimeRange(start: startOfMonth, end: now);
      case ReportPeriod.lastMonth:
        final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
        final endOfLastMonth = DateTime(now.year, now.month, 0);
        return DateTimeRange(start: startOfLastMonth, end: endOfLastMonth);
      case ReportPeriod.thisQuarter:
        final currentQuarter = (now.month - 1) ~/ 3;
        final startOfQuarter = DateTime(now.year, currentQuarter * 3 + 1, 1);
        return DateTimeRange(start: startOfQuarter, end: now);
      case ReportPeriod.lastQuarter:
        final currentQuarter = (now.month - 1) ~/ 3;
        final previousQuarter = currentQuarter > 0 ? currentQuarter - 1 : 3;
        final startOfLastQuarter = DateTime(
          previousQuarter == 3 ? now.year - 1 : now.year,
          previousQuarter * 3 + 1,
          1,
        );
        final endOfLastQuarter = DateTime(
          currentQuarter == 0 ? now.year - 1 : now.year,
          currentQuarter * 3,
          0,
        );
        return DateTimeRange(start: startOfLastQuarter, end: endOfLastQuarter);
      case ReportPeriod.thisYear:
        final startOfYear = DateTime(now.year, 1, 1);
        return DateTimeRange(start: startOfYear, end: now);
      case ReportPeriod.lastYear:
        final startOfLastYear = DateTime(now.year - 1, 1, 1);
        final endOfLastYear = DateTime(now.year, 1, 0);
        return DateTimeRange(start: startOfLastYear, end: endOfLastYear);
      case ReportPeriod.custom:
        if (report.customStartDate != null && report.customEndDate != null) {
          return DateTimeRange(start: report.customStartDate!, end: report.customEndDate!);
        } else {
          // إذا لم يتم تحديد فترة مخصصة، استخدم الشهر الحالي
          final startOfMonth = DateTime(now.year, now.month, 1);
          return DateTimeRange(start: startOfMonth, end: now);
        }
    }
  }

  /// تحويل خريطة إلى تقرير محسن
  EnhancedReport _mapToEnhancedReport(Map<String, dynamic> map) {
    // تحويل الفلاتر
    List<ReportFilter> filters = [];
    if (map['filters'] != null) {
      final List<dynamic> filtersJson = jsonDecode(map['filters']);
      filters = filtersJson.map((f) => ReportFilter.fromMap(f)).toList();
    }

    // تحويل التصورات المرئية
    List<ReportVisualization> visualizations = [];
    if (map['visualizations'] != null) {
      final List<dynamic> visualizationsJson = jsonDecode(map['visualizations']);
      visualizations = visualizationsJson.map((v) => _mapToVisualization(v)).toList();
    }

    // تحويل اللون والأيقونة
    Color? color;
    if (map['colorValue'] != null) {
      color = Color(map['colorValue']);
    }

    IconData? icon;
    if (map['iconCodePoint'] != null && map['iconFontFamily'] != null) {
      icon = IconData(
        map['iconCodePoint'],
        fontFamily: map['iconFontFamily'],
      );
    }

    return EnhancedReport(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      type: ReportType.values[map['type']],
      createdById: map['createdById'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt']) : null,
      filters: filters,
      visualizations: visualizations,
      period: ReportPeriod.values[map['period']],
      customStartDate: map['customStartDate'] != null ? DateTime.fromMillisecondsSinceEpoch(map['customStartDate']) : null,
      customEndDate: map['customEndDate'] != null ? DateTime.fromMillisecondsSinceEpoch(map['customEndDate']) : null,
      filePath: map['filePath'],
      format: map['format'] != null ? ReportFormat.values[map['format']] : null,
      fileSize: map['fileSize'],
      lastExportedAt: map['lastExportedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['lastExportedAt']) : null,
      isShared: map['isShared'] == 1,
      sharedWithUserIds: map['sharedWithUserIds'] != null ? map['sharedWithUserIds'].split(',') : null,
      isFavorite: map['isFavorite'] == 1,
      displayOrder: map['displayOrder'],
      color: color,
      icon: icon,
    );
  }

  /// تحويل خريطة إلى تصور مرئي
  ReportVisualization _mapToVisualization(Map<String, dynamic> map) {
    // تحويل ألوان السلاسل
    List<Color>? seriesColors;
    if (map['seriesColors'] != null) {
      final List<dynamic> colorsJson = map['seriesColors'];
      seriesColors = colorsJson.map((c) => Color(c)).toList();
    }

    return ReportVisualization(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      type: VisualizationType.values[map['type']],
      orientation: map['orientation'] != null ? ChartOrientation.values[map['orientation']] : ChartOrientation.vertical,
      xAxisField: map['xAxisField'],
      xAxisLabel: map['xAxisLabel'],
      yAxisField: map['yAxisField'],
      yAxisLabel: map['yAxisLabel'],
      aggregationType: map['aggregationType'] != null ? AggregationType.values[map['aggregationType']] : null,
      dataFields: List<String>.from(map['dataFields']),
      dataLabels: map['dataLabels'] != null ? List<String>.from(map['dataLabels']) : null,
      seriesColors: seriesColors,
      showValues: map['showValues'] ?? true,
      showLabels: map['showLabels'] ?? true,
      showGrid: map['showGrid'] ?? true,
      showLegend: map['showLegend'] ?? true,
      legendPosition: map['legendPosition'] != null ? LegendPosition.values[map['legendPosition']] : null,
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      order: map['order'] ?? 0,
      settings: map['settings'],
    );
  }

  /// تصدير تقرير بتنسيق PDF
  Future<String?> exportReportToPdf(String reportId) async {
    debugPrint('بدء تصدير التقرير بتنسيق PDF: $reportId');

    try {
      // تنفيذ التقرير
      final reportResult = await executeReport(reportId);
      if (!reportResult.isSuccess) {
        debugPrint('فشل تنفيذ التقرير: ${reportResult.errorMessages}');
        return null;
      }

      // الحصول على معلومات التقرير
      final report = await getReportById(reportId);
      if (report == null) {
        debugPrint('التقرير غير موجود: $reportId');
        return null;
      }

      // استخدام خدمة تصدير PDF
      final filePath = await _pdfExportService.exportToPdf(
        reportId: report.id,
        title: report.title,
        description: report.description,
        reportType: report.type,
        data: reportResult.data ?? [],
        summary: reportResult.summary ?? {},
        visualizationData: reportResult.visualizationData,
      );

      // تحديث معلومات التقرير
      if (filePath != null) {
        final file = File(filePath);
        final fileSize = await file.length();

        final updatedReport = report.copyWith(
          filePath: filePath,
          format: ReportFormat.pdf,
          fileSize: fileSize,
          lastExportedAt: DateTime.now(),
        );

        await updateReport(updatedReport);
      }

      debugPrint('تم تصدير التقرير بنجاح إلى: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق PDF: $e');
      return null;
    }
  }

  /// تصدير تقرير بتنسيق Excel
  Future<String?> exportReportToExcel(String reportId) async {
    debugPrint('بدء تصدير التقرير بتنسيق Excel: $reportId');

    try {
      // تنفيذ التقرير
      final reportResult = await executeReport(reportId);
      if (!reportResult.isSuccess) {
        debugPrint('فشل تنفيذ التقرير: ${reportResult.errorMessages}');
        return null;
      }

      // الحصول على معلومات التقرير
      final report = await getReportById(reportId);
      if (report == null) {
        debugPrint('التقرير غير موجود: $reportId');
        return null;
      }

      // استخدام خدمة تصدير Excel
      final filePath = await _excelExportService.exportToExcel(
        reportId: report.id,
        title: report.title,
        description: report.description,
        reportType: report.type,
        data: reportResult.data ?? [],
        summary: reportResult.summary ?? {},
        visualizationData: reportResult.visualizationData,
      );

      // تحديث معلومات التقرير
      if (filePath != null) {
        final file = File(filePath);
        final fileSize = await file.length();

        final updatedReport = report.copyWith(
          filePath: filePath,
          format: ReportFormat.excel,
          fileSize: fileSize,
          lastExportedAt: DateTime.now(),
        );

        await updateReport(updatedReport);
      }

      debugPrint('تم تصدير التقرير بنجاح إلى: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق Excel: $e');
      return null;
    }
  }

  /// تصدير تقرير بتنسيق CSV
  Future<String?> exportReportToCsv(String reportId) async {
    debugPrint('بدء تصدير التقرير بتنسيق CSV: $reportId');

    try {
      // تنفيذ التقرير
      final reportResult = await executeReport(reportId);
      if (!reportResult.isSuccess) {
        debugPrint('فشل تنفيذ التقرير: ${reportResult.errorMessages}');
        return null;
      }

      // الحصول على معلومات التقرير
      final report = await getReportById(reportId);
      if (report == null) {
        debugPrint('التقرير غير موجود: $reportId');
        return null;
      }

      // استخدام خدمة تصدير CSV
      final filePath = await _csvExportService.exportToCsv(
        reportId: report.id,
        title: report.title,
        data: reportResult.data ?? [],
        visualizationData: reportResult.visualizationData,
      );

      // تحديث معلومات التقرير
      if (filePath != null) {
        final file = File(filePath);
        final fileSize = await file.length();

        final updatedReport = report.copyWith(
          filePath: filePath,
          format: ReportFormat.csv,
          fileSize: fileSize,
          lastExportedAt: DateTime.now(),
        );

        await updateReport(updatedReport);
      }

      debugPrint('تم تصدير التقرير بنجاح إلى: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق CSV: $e');
      return null;
    }
  }

  /// تصدير تقرير بتنسيق JSON
  Future<String?> exportReportToJson(String reportId) async {
    debugPrint('بدء تصدير التقرير بتنسيق JSON: $reportId');

    try {
      // تنفيذ التقرير
      final reportResult = await executeReport(reportId);
      if (!reportResult.isSuccess) {
        debugPrint('فشل تنفيذ التقرير: ${reportResult.errorMessages}');
        return null;
      }

      // الحصول على معلومات التقرير
      final report = await getReportById(reportId);
      if (report == null) {
        debugPrint('التقرير غير موجود: $reportId');
        return null;
      }

      // استخدام خدمة تصدير JSON
      final filePath = await _jsonExportService.exportToJson(
        reportId: report.id,
        title: report.title,
        description: report.description,
        reportType: report.type,
        data: reportResult.data ?? [],
        summary: reportResult.summary ?? {},
        visualizationData: reportResult.visualizationData,
      );

      // تحديث معلومات التقرير
      if (filePath != null) {
        final file = File(filePath);
        final fileSize = await file.length();

        final updatedReport = report.copyWith(
          filePath: filePath,
          format: ReportFormat.json,
          fileSize: fileSize,
          lastExportedAt: DateTime.now(),
        );

        await updateReport(updatedReport);
      }

      debugPrint('تم تصدير التقرير بنجاح إلى: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق JSON: $e');
      return null;
    }
  }
}
