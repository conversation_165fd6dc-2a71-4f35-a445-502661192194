import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/system_models.dart';
import '../services/api/system_settings_api_service.dart';

/// متحكم إعدادات النظام
class SystemSettingsController extends GetxController {
  final SystemSettingsApiService _apiService = SystemSettingsApiService();

  // قوائم الإعدادات
  final RxList<SystemSetting> _allSettings = <SystemSetting>[].obs;
  final RxList<SystemSetting> _filteredSettings = <SystemSetting>[].obs;

  // الإعداد الحالي
  final Rx<SystemSetting?> _currentSetting = Rx<SystemSetting?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _categoryFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<SystemSetting> get allSettings => _allSettings;
  List<SystemSetting> get filteredSettings => _filteredSettings;
  SystemSetting? get currentSetting => _currentSetting.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get categoryFilter => _categoryFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllSettings();
  }

  /// تحميل جميع إعدادات النظام
  Future<void> loadAllSettings() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final settings = await _apiService.getAllSettings();
      _allSettings.assignAll(settings);
      _applyFilters();
      debugPrint('تم تحميل ${settings.length} إعداد نظام');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعدادات النظام: $e';
      debugPrint('خطأ في تحميل إعدادات النظام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على إعداد نظام بالمعرف
  Future<void> getSettingById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final setting = await _apiService.getSettingById(id);
      _currentSetting.value = setting;
      debugPrint('تم تحميل إعداد النظام: ${setting.key}');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعداد النظام: $e';
      debugPrint('خطأ في تحميل إعداد النظام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على إعداد بالمفتاح
  Future<SystemSetting?> getSettingByKey(String key) async {
    try {
      final setting = await _apiService.getSettingByKey(key);
      debugPrint('تم تحميل إعداد النظام بالمفتاح: $key');
      return setting;
    } catch (e) {
      debugPrint('خطأ في تحميل إعداد النظام بالمفتاح: $e');
      return null;
    }
  }

  /// إنشاء إعداد نظام جديد
  Future<bool> createSetting(SystemSetting setting) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newSetting = await _apiService.createSetting(setting);
      _allSettings.add(newSetting);
      _applyFilters();
      debugPrint('تم إنشاء إعداد نظام جديد: ${newSetting.key}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء إعداد النظام: $e';
      debugPrint('خطأ في إنشاء إعداد النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إعداد نظام
  Future<bool> updateSetting(int id, SystemSetting setting) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateSetting(id, setting);
      final index = _allSettings.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allSettings[index] = setting;
        _applyFilters();
      }
      debugPrint('تم تحديث إعداد النظام: ${setting.key}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث إعداد النظام: $e';
      debugPrint('خطأ في تحديث إعداد النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث قيمة إعداد بالمفتاح
  Future<bool> updateSettingValue(String key, String value) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateSettingValue(key, value);
      final index = _allSettings.indexWhere((s) => s.key == key);
      if (index != -1) {
        _allSettings[index] = _allSettings[index].copyWith(value: value);
        _applyFilters();
      }
      debugPrint('تم تحديث قيمة إعداد النظام: $key = $value');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث قيمة إعداد النظام: $e';
      debugPrint('خطأ في تحديث قيمة إعداد النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف إعداد نظام
  Future<bool> deleteSetting(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteSetting(id);
      _allSettings.removeWhere((s) => s.id == id);
      _applyFilters();
      debugPrint('تم حذف إعداد النظام');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف إعداد النظام: $e';
      debugPrint('خطأ في حذف إعداد النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الإعدادات حسب الفئة
  Future<void> getSettingsByCategory(String category) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final settings = await _apiService.getSettingsByCategory(category);
      _allSettings.assignAll(settings);
      _applyFilters();
      debugPrint('تم تحميل ${settings.length} إعداد للفئة $category');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعدادات الفئة: $e';
      debugPrint('خطأ في تحميل إعدادات الفئة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعادة تعيين الإعدادات للقيم الافتراضية
  Future<bool> resetToDefaults() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.resetToDefaults();
      await loadAllSettings();
      debugPrint('تم إعادة تعيين الإعدادات للقيم الافتراضية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إعادة تعيين الإعدادات: $e';
      debugPrint('خطأ في إعادة تعيين الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير الإعدادات
  Future<String?> exportSettings() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportSettings();
      debugPrint('تم تصدير الإعدادات: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير الإعدادات: $e';
      debugPrint('خطأ في تصدير الإعدادات: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// استيراد الإعدادات
  Future<bool> importSettings(String filePath) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.importSettings(filePath);
      await loadAllSettings();
      debugPrint('تم استيراد الإعدادات من: $filePath');
      return true;
    } catch (e) {
      _error.value = 'خطأ في استيراد الإعدادات: $e';
      debugPrint('خطأ في استيراد الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allSettings.where((setting) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!setting.key.toLowerCase().contains(query) &&
            !setting.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح الفئة
      if (_categoryFilter.value != null && setting.category != _categoryFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !setting.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredSettings.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح الفئة
  void setCategoryFilter(String? category) {
    _categoryFilter.value = category;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _categoryFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllSettings();
  }
}
