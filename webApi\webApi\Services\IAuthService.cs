using webApi.Models.Auth;

namespace webApi.Services;

/// <summary>
/// واجهة خدمة المصادقة
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// تسجيل الدخول
    /// </summary>
    /// <param name="request">بيانات تسجيل الدخول</param>
    /// <returns>استجابة المصادقة</returns>
    Task<AuthResponse> LoginAsync(LoginRequest request);

    /// <summary>
    /// التسجيل
    /// </summary>
    /// <param name="request">بيانات التسجيل</param>
    /// <returns>استجابة المصادقة</returns>
    Task<AuthResponse> RegisterAsync(RegisterRequest request);

    /// <summary>
    /// تحديث الرمز
    /// </summary>
    /// <param name="request">طلب تحديث الرمز</param>
    /// <returns>استجابة المصادقة</returns>
    Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request);

    /// <summary>
    /// تسجيل الخروج
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="refreshToken">رمز التحديث</param>
    /// <returns>نتيجة العملية</returns>
    Task<bool> LogoutAsync(int userId, string refreshToken);

    /// <summary>
    /// تغيير كلمة المرور
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="request">بيانات تغيير كلمة المرور</param>
    /// <returns>نتيجة العملية</returns>
    Task<AuthResponse> ChangePasswordAsync(int userId, ChangePasswordRequest request);

    /// <summary>
    /// نسيان كلمة المرور
    /// </summary>
    /// <param name="request">بيانات نسيان كلمة المرور</param>
    /// <returns>نتيجة العملية</returns>
    Task<AuthResponse> ForgotPasswordAsync(ForgotPasswordRequest request);

    /// <summary>
    /// التحقق من صحة المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>بيانات المستخدم</returns>
    Task<UserInfo?> ValidateUserAsync(int userId);

    /// <summary>
    /// تحديث آخر تسجيل دخول
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>نتيجة العملية</returns>
    Task<bool> UpdateLastLoginAsync(int userId);

    /// <summary>
    /// تحديث حالة الاتصال
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="isOnline">حالة الاتصال</param>
    /// <returns>نتيجة العملية</returns>
    Task<bool> UpdateOnlineStatusAsync(int userId, bool isOnline);
}
