import 'package:flutter/foundation.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';
import 'api_service.dart';

/// خدمة API للمستخدمين
class UserApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiService.get('/users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      rethrow;
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    try {
      final response = await _apiService.get('/users/$id');
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم: $e');
      return null;
    }
  }

  /// إنشاء مستخدم جديد
  Future<User?> createUser(User user) async {
    try {
      final response = await _apiService.post(
        '/users',
        body: user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث مستخدم
  Future<User?> updateUser(User user) async {
    try {
      final response = await _apiService.put(
        '/users/${user.id}',
        body: user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم: $e');
      rethrow;
    }
  }

  /// حذف مستخدم (حذف ناعم)
  Future<bool> deleteUser(int id) async {
    try {
      final response = await _apiService.delete('/users/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final response = await _apiService.get('/users/active');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين النشطين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين المتصلين
  Future<List<User>> getOnlineUsers() async {
    try {
      final response = await _apiService.get('/users/online');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين المتصلين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين بدور معين
  Future<List<User>> getUsersByRole(UserRole role) async {
    try {
      final response = await _apiService.get('/users/role/${role.value}');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين بالدور: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين في قسم معين
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      final response = await _apiService.get('/users/department/$departmentId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي القسم: $e');
      rethrow;
    }
  }

  /// البحث عن المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final response = await _apiService.get('/users/search?q=$query');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدمين: $e');
      rethrow;
    }
  }

  /// تحديث حالة النشاط للمستخدم
  Future<bool> updateUserActiveStatus(int userId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/users/$userId/active',
        body: {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة النشاط: $e');
      return false;
    }
  }

  /// تحديث حالة الاتصال للمستخدم
  Future<bool> updateUserOnlineStatus(int userId, bool isOnline) async {
    try {
      final response = await _apiService.put(
        '/users/$userId/online',
        body: {'isOnline': isOnline},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الاتصال: $e');
      return false;
    }
  }

  /// تحديث دور المستخدم
  Future<bool> updateUserRole(int userId, UserRole role) async {
    try {
      final response = await _apiService.put(
        '/users/$userId/role',
        body: {'role': role.value},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }

  /// تحديث قسم المستخدم
  Future<bool> updateUserDepartment(int userId, int? departmentId) async {
    try {
      final response = await _apiService.put(
        '/users/$userId/department',
        body: {'departmentId': departmentId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث قسم المستخدم: $e');
      return false;
    }
  }

  /// رفع صورة الملف الشخصي
  Future<String?> uploadProfileImage(int userId, String imagePath) async {
    try {
      // TODO: تنفيذ رفع الصورة
      // هذا يتطلب multipart/form-data request
      debugPrint('TODO: تنفيذ رفع صورة الملف الشخصي');
      return null;
    } catch (e) {
      debugPrint('خطأ في رفع صورة الملف الشخصي: $e');
      return null;
    }
  }

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final response = await _apiService.get('/departments');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final response = await _apiService.get('/departments/$id');
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على القسم: $e');
      return null;
    }
  }

  /// إنشاء قسم جديد
  Future<Department?> createDepartment(Department department) async {
    try {
      final response = await _apiService.post(
        '/departments',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<Department?> updateDepartment(Department department) async {
    try {
      final response = await _apiService.put(
        '/departments/${department.id}',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث القسم: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    try {
      final response = await _apiService.delete('/departments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف القسم: $e');
      return false;
    }
  }

  /// الحصول على الأقسام النشطة
  Future<List<Department>> getActiveDepartments() async {
    try {
      final response = await _apiService.get('/departments/active');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام النشطة: $e');
      rethrow;
    }
  }
}
