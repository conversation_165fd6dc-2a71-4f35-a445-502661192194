-- Rollback Script: Revert Archive Models Updates
-- التراجع عن تحديثات نماذج الأرشيف

-- تحذير: هذا السكريبت سيحذف البيانات المضافة في الأعمدة الجديدة
-- تأكد من عمل نسخة احتياطية قبل التنفيذ

-- 1. حذف جدول archive_document_tags
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[archive_document_tags]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[archive_document_tags];
    PRINT 'Table archive_document_tags dropped';
END

-- 2. حذف Foreign Key للعمود created_by في archive_documents
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_archive_documents_created_by_users')
BEGIN
    ALTER TABLE [dbo].[archive_documents]
    DROP CONSTRAINT [FK_archive_documents_created_by_users];
    PRINT 'Foreign key FK_archive_documents_created_by_users dropped';
END

-- 3. حذف الأعمدة المضافة من archive_documents
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_at')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    DROP COLUMN [created_at];
    PRINT 'Column created_at dropped from archive_documents';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_by')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    DROP COLUMN [created_by];
    PRINT 'Column created_by dropped from archive_documents';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'content')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    DROP COLUMN [content];
    PRINT 'Column content dropped from archive_documents';
END

-- 4. حذف الأعمدة المضافة من archive_tags
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_tags] 
    DROP COLUMN [is_active];
    PRINT 'Column is_active dropped from archive_tags';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'description')
BEGIN
    ALTER TABLE [dbo].[archive_tags] 
    DROP COLUMN [description];
    PRINT 'Column description dropped from archive_tags';
END

-- 5. حذف الأعمدة المضافة من archive_categories
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    DROP COLUMN [is_active];
    PRINT 'Column is_active dropped from archive_categories';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'icon')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    DROP COLUMN [icon];
    PRINT 'Column icon dropped from archive_categories';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'color')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    DROP COLUMN [color];
    PRINT 'Column color dropped from archive_categories';
END

PRINT 'Rollback completed successfully: Archive models reverted to original state';
