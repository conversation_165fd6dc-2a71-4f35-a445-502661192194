import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/text_document_controller.dart';
import '../services/auth_service.dart';
import '../services/api/text_document_api_service.dart';
import '../services/export_services/enhanced_pdf_export_service.dart';
// تم إزالة استيرادات قاعدة البيانات

/// رابط التطبيق الرئيسي
///
/// يضمن تسجيل جميع الخدمات والمتحكمات الأساسية عند بدء التطبيق
class AppBindings extends Bindings {
  @override
  void dependencies() {
    // تسجيل خدمة المصادقة
    if (!Get.isRegistered<AuthService>()) {
      Get.put(AuthService(), permanent: true);
    }

    // تسجيل متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }

    // تم إزالة تسجيل قاعدة البيانات والمستودعات

    // تسجيل خدمة API للمستندات النصية
    if (!Get.isRegistered<TextDocumentApiService>()) {
      Get.put(TextDocumentApiService(), permanent: true);
    }

    // تسجيل خدمة تصدير PDF المحسنة
    if (!Get.isRegistered<EnhancedPdfExportService>()) {
      Get.put(EnhancedPdfExportService(), permanent: true);
    }

    // تسجيل متحكم المستندات النصية
    if (!Get.isRegistered<TextDocumentController>()) {
      Get.put(TextDocumentController(), permanent: true);
    }
  }
}
