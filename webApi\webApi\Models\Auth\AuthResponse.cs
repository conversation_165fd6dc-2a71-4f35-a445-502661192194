namespace webApi.Models.Auth;

/// <summary>
/// نموذج استجابة المصادقة
/// </summary>
public class AuthResponse
{
    /// <summary>
    /// حالة نجاح العملية
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// رسالة الاستجابة
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// رمز الوصول JWT
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// رمز التحديث
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// تاريخ انتهاء صلاحية الرمز
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// نوع الرمز (Bearer)
    /// </summary>
    public string TokenType { get; set; } = "Bearer";

    /// <summary>
    /// بيانات المستخدم
    /// </summary>
    public UserInfo? User { get; set; }
}

/// <summary>
/// معلومات المستخدم المختصرة
/// </summary>
public class UserInfo
{
    /// <summary>
    /// معرف المستخدم
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// الاسم الكامل
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// اسم المستخدم
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// دور المستخدم
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// اسم الدور
    /// </summary>
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// معرف القسم
    /// </summary>
    public int? DepartmentId { get; set; }

    /// <summary>
    /// اسم القسم
    /// </summary>
    public string? DepartmentName { get; set; }

    /// <summary>
    /// صورة الملف الشخصي
    /// </summary>
    public string? ProfileImage { get; set; }

    /// <summary>
    /// حالة النشاط
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// حالة الاتصال
    /// </summary>
    public bool IsOnline { get; set; }
}
