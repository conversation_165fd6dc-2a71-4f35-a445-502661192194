@echo off
echo ========================================
echo    اختبار الاتصال مع ASP.NET Core API
echo ========================================
echo.

echo اختبار الاتصال مع خادم API...
echo.

echo 1. اختبار المنفذ HTTP (5175)...
curl -s -o nul -w "HTTP Status: %%{http_code}\n" http://localhost:5175/api/auth/test 2>nul
if %errorlevel% neq 0 (
    echo ❌ فشل الاتصال مع المنفذ HTTP
) else (
    echo ✅ نجح الاتصال مع المنفذ HTTP
)

echo.
echo 2. اختبار المنفذ HTTPS (7111)...
curl -s -o nul -w "HTTPS Status: %%{http_code}\n" -k https://localhost:7111/api/auth/test 2>nul
if %errorlevel% neq 0 (
    echo ❌ فشل الاتصال مع المنفذ HTTPS
) else (
    echo ✅ نجح الاتصال مع المنفذ HTTPS
)

echo.
echo 3. اختبار Swagger UI...
curl -s -o nul -w "Swagger Status: %%{http_code}\n" http://localhost:5175/swagger 2>nul
if %errorlevel% neq 0 (
    echo ❌ فشل الوصول إلى Swagger UI
) else (
    echo ✅ نجح الوصول إلى Swagger UI
)

echo.
echo ========================================
echo انتهى الاختبار
echo ========================================
pause
