import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../services/api/task_priority_api_service.dart';

/// متحكم أولويات المهام
class TaskPriorityController extends GetxController {
  final TaskPriorityApiService _apiService = TaskPriorityApiService();

  // قوائم أولويات المهام
  final RxList<TaskPriority> _allPriorities = <TaskPriority>[].obs;
  final RxList<TaskPriority> _filteredPriorities = <TaskPriority>[].obs;

  // أولوية المهمة الحالية
  final Rx<TaskPriority?> _currentPriority = Rx<TaskPriority?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskPriority> get allPriorities => _allPriorities;
  List<TaskPriority> get filteredPriorities => _filteredPriorities;
  TaskPriority? get currentPriority => _currentPriority.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllPriorities();
  }

  /// تحميل جميع أولويات المهام
  Future<void> loadAllPriorities() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final priorities = await _apiService.getAllPriorities();
      _allPriorities.assignAll(priorities);
      _applyFilters();
      debugPrint('تم تحميل ${priorities.length} أولوية مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أولويات المهام: $e';
      debugPrint('خطأ في تحميل أولويات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على أولوية مهمة بالمعرف
  Future<void> getPriorityById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final priority = await _apiService.getPriorityById(id);
      _currentPriority.value = priority;
      debugPrint('تم تحميل أولوية المهمة: ${priority.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل أولوية المهمة: $e';
      debugPrint('خطأ في تحميل أولوية المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<bool> createPriority(TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newPriority = await _apiService.createPriority(priority);
      _allPriorities.add(newPriority);
      _applyFilters();
      debugPrint('تم إنشاء أولوية مهمة جديدة: ${newPriority.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء أولوية المهمة: $e';
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updatePriority(int id, TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updatePriority(id, priority);
      final index = _allPriorities.indexWhere((p) => p.id == id);
      if (index != -1) {
        _allPriorities[index] = priority;
        _applyFilters();
      }
      debugPrint('تم تحديث أولوية المهمة: ${priority.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث أولوية المهمة: $e';
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deletePriority(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deletePriority(id);
      _allPriorities.removeWhere((p) => p.id == id);
      _applyFilters();
      debugPrint('تم حذف أولوية المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف أولوية المهمة: $e';
      debugPrint('خطأ في حذف أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأولويات النشطة فقط
  List<TaskPriority> get activePriorities {
    return _allPriorities.where((priority) => priority.isActive).toList();
  }

  /// الحصول على أولوية افتراضية
  TaskPriority? get defaultPriority {
    return _allPriorities.firstWhereOrNull((priority) => priority.isDefault);
  }

  /// ترتيب الأولويات حسب المستوى
  List<TaskPriority> get prioritiesByLevel {
    var sorted = List<TaskPriority>.from(_allPriorities);
    sorted.sort((a, b) => a.level.compareTo(b.level));
    return sorted;
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPriorities.where((priority) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!priority.name.toLowerCase().contains(query) &&
            !priority.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !priority.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredPriorities.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllPriorities();
  }
}
