<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار CORS للـ API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار CORS للـ API</h1>
        
        <div class="test-section info">
            <h3>📋 معلومات الاختبار</h3>
            <p>هذه الصفحة تختبر إعدادات CORS للـ ASP.NET Core API</p>
            <p><strong>عنوان API:</strong> <span id="apiUrl">http://localhost:5175</span></p>
            <p><strong>عنوان API (HTTPS):</strong> <span id="apiUrlHttps">https://localhost:7111</span></p>
        </div>

        <div class="test-section">
            <h3>🔍 اختبار الاتصال الأساسي</h3>
            <button onclick="testBasicConnection()">اختبار GET الأساسي</button>
            <button onclick="testOptionsRequest()">اختبار OPTIONS (Preflight)</button>
            <button onclick="testWithCredentials()">اختبار مع Credentials</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 اختبار المصادقة</h3>
            <button onclick="testAuthEndpoint()">اختبار نقطة المصادقة</button>
            <button onclick="testProtectedEndpoint()">اختبار نقطة محمية</button>
            <div id="authResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 اختبار البيانات</h3>
            <button onclick="testPostRequest()">اختبار POST</button>
            <button onclick="testPutRequest()">اختبار PUT</button>
            <button onclick="testDeleteRequest()">اختبار DELETE</button>
            <div id="dataResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_HTTP = 'http://localhost:5175';
        const API_BASE_HTTPS = 'https://localhost:7111';
        
        function logResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const status = isSuccess ? '✅' : '❌';
            element.textContent += `[${timestamp}] ${status} ${message}\n`;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function testBasicConnection() {
            const resultId = 'basicResult';
            document.getElementById(resultId).textContent = '';
            
            try {
                logResult(resultId, 'بدء اختبار الاتصال الأساسي...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                logResult(resultId, `استجابة HTTP: ${response.status} ${response.statusText}`);
                logResult(resultId, `رؤوس CORS: ${response.headers.get('Access-Control-Allow-Origin') || 'غير موجود'}`);
                
                if (response.ok) {
                    const data = await response.text();
                    logResult(resultId, `البيانات: ${data}`);
                } else {
                    logResult(resultId, 'فشل في الحصول على استجابة صحيحة', false);
                }
            } catch (error) {
                logResult(resultId, `خطأ: ${error.message}`, false);
            }
        }

        async function testOptionsRequest() {
            const resultId = 'basicResult';
            
            try {
                logResult(resultId, 'بدء اختبار OPTIONS (Preflight)...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/users`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:8080',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                logResult(resultId, `استجابة OPTIONS: ${response.status}`);
                logResult(resultId, `Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin') || 'غير موجود'}`);
                logResult(resultId, `Allow-Methods: ${response.headers.get('Access-Control-Allow-Methods') || 'غير موجود'}`);
                logResult(resultId, `Allow-Headers: ${response.headers.get('Access-Control-Allow-Headers') || 'غير موجود'}`);
                
            } catch (error) {
                logResult(resultId, `خطأ في OPTIONS: ${error.message}`, false);
            }
        }

        async function testWithCredentials() {
            const resultId = 'basicResult';
            
            try {
                logResult(resultId, 'بدء اختبار مع Credentials...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/health`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                logResult(resultId, `استجابة مع Credentials: ${response.status}`);
                logResult(resultId, `Allow-Credentials: ${response.headers.get('Access-Control-Allow-Credentials') || 'غير موجود'}`);
                
            } catch (error) {
                logResult(resultId, `خطأ مع Credentials: ${error.message}`, false);
            }
        }

        async function testAuthEndpoint() {
            const resultId = 'authResult';
            document.getElementById(resultId).textContent = '';
            
            try {
                logResult(resultId, 'بدء اختبار نقطة المصادقة...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123'
                    })
                });
                
                logResult(resultId, `استجابة المصادقة: ${response.status}`);
                
                if (response.status === 401 || response.status === 400) {
                    logResult(resultId, 'نقطة المصادقة تعمل (رفض بيانات اختبار)');
                } else if (response.ok) {
                    logResult(resultId, 'نقطة المصادقة تعمل (قبول بيانات اختبار)');
                }
                
            } catch (error) {
                logResult(resultId, `خطأ في المصادقة: ${error.message}`, false);
            }
        }

        async function testProtectedEndpoint() {
            const resultId = 'authResult';
            
            try {
                logResult(resultId, 'بدء اختبار نقطة محمية...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/users`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer invalid-token'
                    }
                });
                
                logResult(resultId, `استجابة النقطة المحمية: ${response.status}`);
                
                if (response.status === 401) {
                    logResult(resultId, 'النقطة المحمية تعمل (رفض token غير صحيح)');
                } else {
                    logResult(resultId, 'النقطة المحمية قد لا تعمل بشكل صحيح', false);
                }
                
            } catch (error) {
                logResult(resultId, `خطأ في النقطة المحمية: ${error.message}`, false);
            }
        }

        async function testPostRequest() {
            const resultId = 'dataResult';
            document.getElementById(resultId).textContent = '';
            
            try {
                logResult(resultId, 'بدء اختبار POST...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: 'Test User',
                        email: '<EMAIL>'
                    })
                });
                
                logResult(resultId, `استجابة POST: ${response.status}`);
                
            } catch (error) {
                logResult(resultId, `خطأ في POST: ${error.message}`, false);
            }
        }

        async function testPutRequest() {
            const resultId = 'dataResult';
            
            try {
                logResult(resultId, 'بدء اختبار PUT...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/users/1`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: 'Updated User',
                        email: '<EMAIL>'
                    })
                });
                
                logResult(resultId, `استجابة PUT: ${response.status}`);
                
            } catch (error) {
                logResult(resultId, `خطأ في PUT: ${error.message}`, false);
            }
        }

        async function testDeleteRequest() {
            const resultId = 'dataResult';
            
            try {
                logResult(resultId, 'بدء اختبار DELETE...');
                
                const response = await fetch(`${API_BASE_HTTP}/api/users/1`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                logResult(resultId, `استجابة DELETE: ${response.status}`);
                
            } catch (error) {
                logResult(resultId, `خطأ في DELETE: ${error.message}`, false);
            }
        }

        // تشغيل اختبار أساسي عند تحميل الصفحة
        window.onload = function() {
            console.log('🧪 صفحة اختبار CORS جاهزة');
        };
    </script>
</body>
</html>
