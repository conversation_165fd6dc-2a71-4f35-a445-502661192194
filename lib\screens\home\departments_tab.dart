import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../models/department_model.dart';
import '../../models/user_model.dart';
import '../../services/department_service.dart';
import '../../utils/responsive_helper.dart';
import '../admin/department_detail_screen.dart';

/// تبويب الأقسام
/// يعرض قائمة بالأقسام المتاحة في النظام
class DepartmentsTab extends StatefulWidget {
  const DepartmentsTab({super.key});

  @override
  State<DepartmentsTab> createState() => _DepartmentsTabState();
}

class _DepartmentsTabState extends State<DepartmentsTab> {
  final _departmentService = DepartmentService();
  final _departmentRepository = DepartmentRepository();
  final _authController = Get.find<AuthController>();

  bool _isLoading = true;
  List<Department> _departments = [];
  String? _errorMessage;

  // متغير لتخزين مراقب التحديثات
  Worker? _updateSubscription;

  @override
  void initState() {
    super.initState();
    _loadDepartments();

    // الاشتراك في إشعارات تحديث بيانات الأقسام
    _updateSubscription = _departmentService.subscribeToDepartmentUpdates(() {
      // تحديث البيانات عند استلام إشعار
      _loadDepartments();
    });
  }

  @override
  void dispose() {
    // إلغاء الاشتراك في إشعارات التحديث
    _updateSubscription?.dispose();
    super.dispose();
  }

  /// تحميل الأقسام من قاعدة البيانات
  Future<void> _loadDepartments() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل الأقسام حسب صلاحيات المستخدم
      if (_authController.isSuperAdmin) {
        // مدير النظام العام يرى جميع الأقسام
        _departments = await _departmentRepository.getAllDepartments();
      } else if (_authController.isAdmin &&
          _authController.currentUser.value?.departmentId != null) {
        // مدير الإدارة يرى إدارته فقط
        final department = await _departmentRepository.getDepartmentById(
            _authController.currentUser.value!.departmentId!);
        _departments = department != null ? [department] : [];
      } else if (_authController.isDepartmentManager &&
          _authController.currentUser.value?.departmentId != null) {
        // مدير القسم يرى قسمه فقط
        final department = await _departmentRepository.getDepartmentById(
            _authController.currentUser.value!.departmentId!);
        _departments = department != null ? [department] : [];
      } else {
        // المستخدم العادي يرى الأقسام النشطة فقط
        _departments = await _departmentRepository.getActiveDepartments();
      }
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء تحميل الأقسام: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض تفاصيل القسم
  void _showDepartmentDetails(Department department) {
    // التحقق من صلاحيات المستخدم
    final canViewDetails = _authController.isAdmin ||
        (_authController.isDepartmentManager &&
            _authController.currentUser.value?.departmentId == department.id);

    if (canViewDetails) {
      // الانتقال إلى شاشة تفاصيل القسم وتحديث البيانات عند العودة
      Get.to(() => DepartmentDetailScreen(departmentId: department.id))
          ?.then((_) {
        // تحديث قائمة الأقسام عند العودة من شاشة التفاصيل
        _loadDepartments();
      });
    } else {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لعرض تفاصيل هذا القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إنشاء قسم جديد
  void _createNewDepartment() {
    // التحقق من صلاحيات المستخدم
    if (!_authController.isAdmin) {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لإنشاء قسم جديد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // عرض نموذج إنشاء قسم جديد
    _showCreateDepartmentDialog();
  }

  /// عرض نموذج إنشاء قسم جديد
  void _showCreateDepartmentDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء قسم جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'اسم القسم',
                  prefixIcon: const Icon(Icons.business),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'وصف القسم',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال اسم القسم',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }

              // إنشاء قسم جديد
              final newDepartment = Department(
                id: '',
                name: nameController.text.trim(),
                description: descriptionController.text.trim(),
                isActive: true,
                createdAt: DateTime.now(),
              );

              try {
                await _departmentRepository.createDepartment(newDepartment);
                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم إنشاء القسم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green.shade100,
                  colorText: Colors.green.shade800,
                );
                _loadDepartments();
              } catch (e) {
                Get.snackbar(
                  'خطأ',
                  'حدث خطأ أثناء إنشاء القسم: $e',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من حجم الشاشة
    final isLargeScreen = ResponsiveHelper.isTablet(context) ||
        ResponsiveHelper.isDesktop(context);

    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _departments.isEmpty
                  ? _buildEmptyWidget()
                  : _buildDepartmentsList(isLargeScreen),
      floatingActionButton: _authController.isAdmin
          ? FloatingActionButton(
              heroTag: 'departments_fab',
              onPressed: _createNewDepartment,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  /// بناء واجهة عرض الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'حدث خطأ غير معروف',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadDepartments,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة عرض القائمة الفارغة
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.business,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أقسام',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _authController.isAdmin
                ? 'قم بإنشاء قسم جديد باستخدام زر الإضافة'
                : 'لا توجد أقسام متاحة حالياً',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (_authController.isAdmin) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _createNewDepartment,
              icon: const Icon(Icons.add),
              label: const Text('إنشاء قسم جديد'),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة الأقسام
  Widget _buildDepartmentsList(bool isLargeScreen) {
    if (isLargeScreen) {
      // عرض الأقسام في شكل بطاقات للشاشات الكبيرة
      return Scaffold(
        appBar: AppBar(
          title: Text("الادارات  "),
        ),
        body: GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _departments.length,
          itemBuilder: (context, index) {
            final department = _departments[index];
            return _buildDepartmentCard(department);
          },
        ),
      );
    } else {
      // عرض الأقسام في شكل قائمة للشاشات الصغيرة
      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _departments.length,
        itemBuilder: (context, index) {
          final department = _departments[index];
          return _buildDepartmentListItem(department);
        },
      );
    }
  }

  /// بناء بطاقة قسم
  Widget _buildDepartmentCard(Department department) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () => _showDepartmentDetails(department),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.business, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      department.name,
                      style: AppStyles.titleMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (!department.isActive)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'غير نشط',
                        style: TextStyle(
                          color: Colors.red.shade800,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              if (department.description != null &&
                  department.description!.isNotEmpty)
                Expanded(
                  child: Text(
                    department.description!,
                    style: AppStyles.bodySmall,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 3,
                  ),
                ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  FutureBuilder<User?>(
                    future:
                        _departmentService.getDepartmentManager(department.id),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Text('جاري التحميل...');
                      }

                      if (snapshot.hasError || !snapshot.hasData) {
                        return const Text('لا يوجد مدير');
                      }

                      return Row(
                        children: [
                          const Icon(Icons.person,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            snapshot.data!.name,
                            style: AppStyles.labelSmall,
                          ),
                        ],
                      );
                    },
                  ),
                  FutureBuilder<List<User>>(
                    future:
                        _departmentService.getDepartmentMembers(department.id),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const SizedBox.shrink();
                      }

                      final count =
                          snapshot.hasData ? snapshot.data!.length : 0;

                      return Row(
                        children: [
                          const Icon(Icons.people,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            '$count عضو',
                            style: AppStyles.labelSmall,
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر قائمة قسم
  Widget _buildDepartmentListItem(Department department) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              department.isActive ? AppColors.primary : Colors.grey,
          child: const Icon(Icons.business, color: Colors.white),
        ),
        title: Text(department.name),
        subtitle:
            department.description != null && department.description!.isNotEmpty
                ? Text(
                    department.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showDepartmentDetails(department),
      ),
    );
  }
}
