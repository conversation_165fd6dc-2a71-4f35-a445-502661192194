@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 تشغيل جميع خوادم المشروع
echo ========================================
echo.

echo 📋 قائمة الخوادم:
echo   1. ASP.NET Core API (Port 5175/7111)
echo   2. Flutter Web Server (Port 8080)  
echo   3. WebSocket Server (Port 8080)
echo.

echo ⚠️  تأكد من إغلاق أي خوادم تعمل على نفس المنافذ
echo.
pause

echo.
echo 🔧 بدء تشغيل الخوادم...
echo.

REM تشغيل ASP.NET Core API
echo 🌐 تشغيل ASP.NET Core API...
start "ASP.NET Core API" cmd /k "cd /d webApi\webApi && echo 🚀 تشغيل ASP.NET Core API على المنفذ 5175/7111 && dotnet run"

REM انتظار قصير
timeout /t 3 /nobreak >nul

REM تشغيل Flutter Web Server
echo 🎯 تشغيل Flutter Web Server...
start "Flutter Web Server" cmd /k "cd /d web_server && echo 🚀 تشغيل Flutter Web Server على المنفذ 8080 && dart run bin/server.dart"

REM انتظار قصير
timeout /t 2 /nobreak >nul

REM تشغيل WebSocket Server
echo 💬 تشغيل WebSocket Server...
start "WebSocket Server" cmd /k "echo 🚀 تشغيل WebSocket Server على المنفذ 8080 && node server.js"

echo.
echo ✅ تم تشغيل جميع الخوادم!
echo.
echo 📋 عناوين الخوادم:
echo   🌐 ASP.NET Core API: http://localhost:5175
echo   🔒 ASP.NET Core API (HTTPS): https://localhost:7111
echo   🎯 Flutter Web: http://localhost:8080
echo   💬 WebSocket: ws://localhost:8080
echo.
echo 🧪 لاختبار CORS، افتح:
echo   📁 webApi\test_cors.html
echo.
echo 📚 للمزيد من المعلومات، راجع:
echo   📄 webApi\CORS_SETUP_README.md
echo.
echo ⚠️  لإيقاف الخوادم، أغلق نوافذ الأوامر المفتوحة
echo.
pause
