@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧪 اختبار سريع لإعدادات CORS
echo ========================================
echo.

echo 🔍 فحص الخوادم...
echo.

REM فحص ASP.NET Core API
echo 📡 فحص ASP.NET Core API (Port 5175)...
curl -s -o nul -w "%%{http_code}" http://localhost:5175/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ASP.NET Core API يعمل
) else (
    echo ❌ ASP.NET Core API لا يعمل
    echo    💡 تشغيل: cd webApi\webApi && dotnet run
)
echo.

REM فحص Flutter Web Server
echo 📡 فحص Flutter Web Server (Port 8080)...
curl -s -o nul -w "%%{http_code}" http://localhost:8080 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter Web Server يعمل
) else (
    echo ❌ Flutter Web Server لا يعمل
    echo    💡 تشغيل: cd web_server && dart run bin/server.dart
)
echo.

REM فحص WebSocket Server
echo 📡 فحص WebSocket Server (Port 8080)...
netstat -an | find "8080" | find "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ WebSocket Server يعمل
) else (
    echo ❌ WebSocket Server لا يعمل
    echo    💡 تشغيل: node server.js
)
echo.

echo ========================================
echo 🧪 اختبار CORS Headers
echo ========================================
echo.

REM اختبار CORS Headers
echo 🔍 اختبار CORS Headers من ASP.NET Core API...
echo.

REM اختبار OPTIONS request
echo 📋 اختبار OPTIONS (Preflight) Request:
curl -s -X OPTIONS ^
  -H "Origin: http://localhost:8080" ^
  -H "Access-Control-Request-Method: POST" ^
  -H "Access-Control-Request-Headers: Content-Type, Authorization" ^
  -I http://localhost:5175/api/health 2>nul | findstr /i "access-control"

if %errorlevel% equ 0 (
    echo ✅ CORS Headers موجودة
) else (
    echo ❌ CORS Headers غير موجودة أو غير صحيحة
)
echo.

REM اختبار GET request
echo 📋 اختبار GET Request مع Origin:
curl -s -H "Origin: http://localhost:8080" -I http://localhost:5175/api/health 2>nul | findstr /i "access-control"

if %errorlevel% equ 0 (
    echo ✅ CORS يعمل مع GET requests
) else (
    echo ❌ CORS لا يعمل مع GET requests
)
echo.

echo ========================================
echo 📋 ملخص النتائج
echo ========================================
echo.

echo 🔧 إذا كانت هناك مشاكل:
echo   1. تأكد من تشغيل جميع الخوادم
echo   2. تحقق من إعدادات CORS في Program.cs
echo   3. راجع ملف CORS_SETUP_README.md
echo   4. استخدم test_cors.html للاختبار التفصيلي
echo.

echo 🚀 لتشغيل جميع الخوادم:
echo   start_all_servers.bat
echo.

echo 🧪 لاختبار تفصيلي:
echo   افتح webApi\test_cors.html في المتصفح
echo.

pause
