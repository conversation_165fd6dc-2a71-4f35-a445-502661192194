# 🌐 إعداد CORS للـ Flutter Web API

## 📋 نظرة عامة

تم تطبيق إعدادات CORS (Cross-Origin Resource Sharing) بشكل متكامل في مشروع ASP.NET Core API لدعم تطبيق Flutter Web بشكل كامل.

## 🔧 الإعدادات المطبقة

### 1. سياسات CORS المتعددة

#### أ) سياسة التطوير (`FlutterWebPolicy`)
```csharp
// سياسة مخصصة للـ Flutter Web في بيئة التطوير
policy
    .WithOrigins(
        "http://localhost:8080",
        "https://localhost:8080", 
        "http://127.0.0.1:8080",
        "https://127.0.0.1:8080"
    )
    .AllowAnyMethod()
    .AllowAnyHeader()
    .AllowCredentials()
    .SetPreflightMaxAge(TimeSpan.FromHours(24));
```

#### ب) سياسة الإنتاج (`ProductionPolicy`)
```csharp
// سياسة مقيدة للإنتاج باستخدام إعدادات التكوين
policy
    .WithOrigins(allowedOrigins)
    .WithMethods(allowedMethods)
    .WithHeaders(allowedHeaders)
    .SetPreflightMaxAge(TimeSpan.FromSeconds(preflightMaxAge))
    .AllowCredentials();
```

#### ج) سياسة مفتوحة (`AllowFlutterApp`)
```csharp
// سياسة مفتوحة للاختبار (للتطوير فقط)
policy
    .AllowAnyOrigin()
    .AllowAnyMethod()
    .AllowAnyHeader();
```

### 2. إعدادات التكوين

#### ملف `appsettings.json`
```json
{
  "Cors": {
    "AllowedOrigins": [
      "http://localhost:8080",
      "https://localhost:8080",
      "http://localhost:3000",
      "https://localhost:3000",
      "http://127.0.0.1:8080",
      "https://127.0.0.1:8080"
    ],
    "AllowedMethods": [
      "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
    ],
    "AllowedHeaders": [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
      "Origin",
      "Access-Control-Request-Method",
      "Access-Control-Request-Headers"
    ],
    "AllowCredentials": true,
    "PreflightMaxAge": 86400
  }
}
```

#### ملف `appsettings.Development.json`
```json
{
  "Cors": {
    "AllowedOrigins": [
      "http://localhost:8080",
      "https://localhost:8080",
      "http://localhost:3000",
      "https://localhost:3000",
      "http://127.0.0.1:8080",
      "https://127.0.0.1:8080",
      "http://localhost:5175",
      "https://localhost:7111"
    ],
    "AllowedMethods": [
      "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"
    ],
    "AllowedHeaders": [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
      "Origin",
      "Access-Control-Request-Method",
      "Access-Control-Request-Headers",
      "X-Auth-Token",
      "Cache-Control",
      "Pragma"
    ],
    "AllowCredentials": true,
    "PreflightMaxAge": 86400
  }
}
```

## 🚀 كيفية الاستخدام

### 1. تشغيل الخوادم

```bash
# تشغيل ASP.NET Core API
cd webApi/webApi
dotnet run

# تشغيل Flutter Web Server
cd web_server
dart run bin/server.dart

# تشغيل WebSocket Server
node server.js
```

### 2. عناوين الخوادم

- **ASP.NET Core API**: `http://localhost:5175` أو `https://localhost:7111`
- **Flutter Web**: `http://localhost:8080`
- **WebSocket**: `ws://localhost:8080`

### 3. اختبار CORS

افتح ملف `test_cors.html` في المتصفح لاختبار إعدادات CORS:
```
file:///path/to/webApi/test_cors.html
```

## 🔍 التحقق من الإعدادات

### 1. فحص رؤوس CORS

```javascript
// في Flutter Web أو JavaScript
fetch('http://localhost:5175/api/health', {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
    }
})
.then(response => {
    console.log('CORS Headers:', {
        'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
        'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
        'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
    });
});
```

### 2. اختبار Preflight Request

```javascript
// اختبار OPTIONS request
fetch('http://localhost:5175/api/users', {
    method: 'OPTIONS',
    headers: {
        'Origin': 'http://localhost:8080',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
});
```

## 🛡️ الأمان

### 1. بيئة التطوير
- استخدام سياسة `FlutterWebPolicy` المخصصة
- السماح بـ Credentials
- مدة Preflight طويلة (24 ساعة)

### 2. بيئة الإنتاج
- استخدام سياسة `ProductionPolicy` المقيدة
- تحديد Origins محددة فقط
- تحديد Methods و Headers محددة
- مراقبة دقيقة للطلبات

## 🔧 استكشاف الأخطاء

### 1. خطأ CORS الشائع
```
Access to fetch at 'http://localhost:5175/api/users' from origin 'http://localhost:8080' has been blocked by CORS policy
```

**الحل:**
- تأكد من تشغيل ASP.NET Core API
- تحقق من إعدادات Origins في التكوين
- تأكد من ترتيب Middleware في Program.cs

### 2. مشاكل Preflight
```
Response to preflight request doesn't pass access control check
```

**الحل:**
- تحقق من دعم OPTIONS method
- تأكد من إعدادات Headers المسموحة
- فحص إعدادات Credentials

### 3. مشاكل Authentication
```
Request header field authorization is not allowed by Access-Control-Allow-Headers
```

**الحل:**
- إضافة "Authorization" إلى AllowedHeaders
- تأكد من إعداد AllowCredentials = true

## 📝 ملاحظات مهمة

1. **ترتيب Middleware**: يجب أن يكون `UseCors()` قبل `UseAuthentication()` و `UseAuthorization()`

2. **AllowCredentials**: عند استخدام `AllowCredentials(true)` لا يمكن استخدام `AllowAnyOrigin()`

3. **Preflight Caching**: استخدام `SetPreflightMaxAge()` لتحسين الأداء

4. **Security Headers**: تم إضافة رؤوس أمان إضافية في خادم Dart

## 🎯 النتيجة

✅ **CORS مطبق بالكامل ومتكامل**
- دعم كامل لـ Flutter Web
- سياسات متعددة للبيئات المختلفة
- إعدادات قابلة للتخصيص
- اختبارات شاملة
- أمان محسن

🚀 **جاهز للاستخدام مع Flutter Web!**
