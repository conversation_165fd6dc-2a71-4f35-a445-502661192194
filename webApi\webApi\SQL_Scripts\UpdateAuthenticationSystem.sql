-- سكريبت تحديث قاعدة البيانات لدعم نظام المصادقة والتفويض
-- Authentication & Authorization System Update Script

USE databasetasks;
GO

-- إضافة جدول رموز التحديث (Refresh Tokens)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='refresh_tokens' AND xtype='U')
BEGIN
    CREATE TABLE refresh_tokens (
        id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        token NVARCHAR(500) NOT NULL,
        expires_at BIGINT NOT NULL,
        created_at BIGINT NOT NULL,
        is_revoked BIT DEFAULT 0,
        revoked_at BIGINT NULL,
        replaced_by_token NVARCHAR(500) NULL,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )
    
    
    -- إنشاء فهرس على user_id و token
    CREATE INDEX IX_refresh_tokens_user_id ON refresh_tokens(user_id);
    CREATE INDEX IX_refresh_tokens_token ON refresh_tokens(token);
    CREATE INDEX IX_refresh_tokens_expires_at ON refresh_tokens(expires_at);
    
    PRINT 'تم إنشاء جدول refresh_tokens بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول refresh_tokens موجود بالفعل';
END
GO

-- إضافة جدول سجل تسجيل الدخول (Login History)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='login_history' AND xtype='U')
BEGIN
    CREATE TABLE login_history (
        id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        login_time BIGINT NOT NULL,
        logout_time BIGINT NULL,
        ip_address NVARCHAR(45) NULL,
        user_agent NVARCHAR(500) NULL,
        is_successful BIT NOT NULL,
        failure_reason NVARCHAR(255) NULL,
        session_duration INT NULL, -- بالثواني
        FOREIGN KEY (user_id) REFERENCES users(id),
    )
    
    -- إنشاء فهارس
    CREATE INDEX IX_login_history_user_id ON login_history(user_id);
    CREATE INDEX IX_login_history_login_time ON login_history(login_time);
    CREATE INDEX IX_login_history_is_successful ON login_history(is_successful);
    
    PRINT 'تم إنشاء جدول login_history بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول login_history موجود بالفعل';
END
GO

-- إضافة جدول محاولات تسجيل الدخول الفاشلة (Failed Login Attempts)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='failed_login_attempts' AND xtype='U')
BEGIN
    CREATE TABLE failed_login_attempts (
        id INT IDENTITY(1,1) PRIMARY KEY,
        email_or_username NVARCHAR(255) NOT NULL,
        ip_address NVARCHAR(45) NULL,
        attempt_time BIGINT NOT NULL,
        failure_reason NVARCHAR(255) NULL,
        user_agent NVARCHAR(500) NULL,
    )
    
    -- إنشاء فهارس
    CREATE INDEX IX_failed_login_attempts_email_username ON failed_login_attempts(email_or_username);
    CREATE INDEX IX_failed_login_attempts_ip_address ON failed_login_attempts(ip_address);
    CREATE INDEX IX_failed_login_attempts_attempt_time ON failed_login_attempts(attempt_time);
    
    PRINT 'تم إنشاء جدول failed_login_attempts بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول failed_login_attempts موجود بالفعل';
END
GO

-- إضافة جدول رموز إعادة تعيين كلمة المرور (Password Reset Tokens)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='password_reset_tokens' AND xtype='U')
BEGIN
    CREATE TABLE password_reset_tokens (
        id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        token NVARCHAR(500) NOT NULL,
        expires_at BIGINT NOT NULL,
        created_at BIGINT NOT NULL,
        is_used BIT DEFAULT 0,
        used_at BIGINT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id),
    )
    
    -- إنشاء فهارس
    CREATE INDEX IX_password_reset_tokens_user_id ON password_reset_tokens(user_id);
    CREATE INDEX IX_password_reset_tokens_token ON password_reset_tokens(token);
    CREATE INDEX IX_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);
    
    PRINT 'تم إنشاء جدول password_reset_tokens بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول password_reset_tokens موجود بالفعل';
END
GO

-- تحديث جدول المستخدمين لإضافة حقول المصادقة الإضافية
-- إضافة حقل تاريخ آخر تغيير لكلمة المرور
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'password_changed_at')
BEGIN
    ALTER TABLE users ADD password_changed_at BIGINT NULL;
    PRINT 'تم إضافة حقل password_changed_at إلى جدول users';
END
ELSE
BEGIN
    PRINT 'حقل password_changed_at موجود بالفعل في جدول users';
END
GO

-- إضافة حقل عدد محاولات تسجيل الدخول الفاشلة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'failed_login_attempts')
BEGIN
    ALTER TABLE users ADD failed_login_attempts INT DEFAULT 0;
    PRINT 'تم إضافة حقل failed_login_attempts إلى جدول users';
END
ELSE
BEGIN
    PRINT 'حقل failed_login_attempts موجود بالفعل في جدول users';
END
GO

-- إضافة حقل تاريخ قفل الحساب
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'locked_until')
BEGIN
    ALTER TABLE users ADD locked_until BIGINT NULL;
    PRINT 'تم إضافة حقل locked_until إلى جدول users';
END
ELSE
BEGIN
    PRINT 'حقل locked_until موجود بالفعل في جدول users';
END
GO

-- إضافة حقل تفعيل المصادقة الثنائية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'two_factor_enabled')
BEGIN
    ALTER TABLE users ADD two_factor_enabled BIT DEFAULT 0;
    PRINT 'تم إضافة حقل two_factor_enabled إلى جدول users';
END
ELSE
BEGIN
    PRINT 'حقل two_factor_enabled موجود بالفعل في جدول users';
END
GO

-- إضافة حقل مفتاح المصادقة الثنائية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'two_factor_secret')
BEGIN
    ALTER TABLE users ADD two_factor_secret NVARCHAR(255) NULL;
    PRINT 'تم إضافة حقل two_factor_secret إلى جدول users';
END
ELSE
BEGIN
    PRINT 'حقل two_factor_secret موجود بالفعل في جدول users';
END
GO
-- تحديث كلمات المرور الموجودة لتكون مشفرة بـ BCrypt
-- هذا السكريبت يجب تشغيله مرة واحدة فقط
DECLARE @UsersToUpdate TABLE (Id INT, CurrentPassword NVARCHAR(255));

-- البحث عن المستخدمين الذين لديهم كلمات مرور غير مشفرة
INSERT INTO @UsersToUpdate (Id, CurrentPassword)
SELECT id, password 
FROM users 
WHERE password NOT LIKE '$2%' -- كلمات المرور المشفرة بـ BCrypt تبدأ بـ $2
AND password IS NOT NULL 
AND password != '';

-- طباعة عدد المستخدمين الذين يحتاجون تحديث
DECLARE @UserCount INT = (SELECT COUNT(*) FROM @UsersToUpdate);
PRINT 'عدد المستخدمين الذين يحتاجون تحديث كلمة المرور: ' + CAST(@UserCount AS NVARCHAR(10));

-- ملاحظة: في البيئة الحقيقية، يجب تشفير كلمات المرور باستخدام BCrypt من خلال التطبيق
-- هنا سنقوم بتعيين كلمة مرور افتراضية مشفرة للمستخدمين الموجودين
-- كلمة المرور الافتراضية: "123456" مشفرة بـ BCrypt
UPDATE users 
SET password = '$2a$11$8gF7YQvnkKW8yZKZQXQZ4eJ7YQvnkKW8yZKZQXQZ4eJ7YQvnkKW8yZ',
    password_changed_at = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
WHERE id IN (SELECT Id FROM @UsersToUpdate);

PRINT 'تم تحديث كلمات المرور للمستخدمين الموجودين';
GO

-- إنشاء إجراءات مخزنة للمصادقة
-- إجراء تسجيل محاولة تسجيل دخول
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_LogLoginAttempt')
    DROP PROCEDURE sp_LogLoginAttempt;
GO

CREATE PROCEDURE sp_LogLoginAttempt
    @UserId INT,
    @IsSuccessful BIT,
    @IpAddress NVARCHAR(45) = NULL,
    @UserAgent NVARCHAR(500) = NULL,
    @FailureReason NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO login_history (user_id, login_time, is_successful, ip_address, user_agent, failure_reason)
    VALUES (@UserId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()), @IsSuccessful, @IpAddress, @UserAgent, @FailureReason);
    
    -- تحديث عداد المحاولات الفاشلة
    IF @IsSuccessful = 0
    BEGIN
        UPDATE users 
        SET failed_login_attempts = ISNULL(failed_login_attempts, 0) + 1
        WHERE id = @UserId;
        
        -- قفل الحساب إذا تجاوز عدد المحاولات الحد المسموح (5 محاولات)
        IF (SELECT failed_login_attempts FROM users WHERE id = @UserId) >= 5
        BEGIN
            UPDATE users 
            SET locked_until = DATEDIFF(SECOND, '1970-01-01', DATEADD(MINUTE, 30, GETUTCDATE())) -- قفل لمدة 30 دقيقة
            WHERE id = @UserId;
        END
    END
    ELSE
    BEGIN
        -- إعادة تعيين عداد المحاولات الفاشلة عند نجاح تسجيل الدخول
        UPDATE users 
        SET failed_login_attempts = 0, locked_until = NULL
        WHERE id = @UserId;
    END
END
GO

PRINT 'تم إنشاء إجراء sp_LogLoginAttempt بنجاح';
GO

-- إجراء تنظيف الرموز المنتهية الصلاحية
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CleanupExpiredTokens')
    DROP PROCEDURE sp_CleanupExpiredTokens;
GO

CREATE PROCEDURE sp_CleanupExpiredTokens
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE());
    
    -- حذف رموز التحديث المنتهية الصلاحية
    DELETE FROM refresh_tokens WHERE expires_at < @CurrentTime;
    
    -- حذف رموز إعادة تعيين كلمة المرور المنتهية الصلاحية
    DELETE FROM password_reset_tokens WHERE expires_at < @CurrentTime;
    
    -- حذف سجلات محاولات تسجيل الدخول الفاشلة الأقدم من 30 يوم
    DELETE FROM failed_login_attempts WHERE attempt_time < (@CurrentTime - (30 * 24 * 60 * 60));
    
    -- حذف سجلات تسجيل الدخول الأقدم من 90 يوم
    DELETE FROM login_history WHERE login_time < (@CurrentTime - (90 * 24 * 60 * 60));
END
GO

PRINT 'تم إنشاء إجراء sp_CleanupExpiredTokens بنجاح';
GO

PRINT 'تم تحديث قاعدة البيانات لدعم نظام المصادقة والتفويض بنجاح!';
PRINT 'ملاحظة: كلمة المرور الافتراضية للمستخدمين الموجودين هي: 123456';
PRINT 'يُنصح بتغيير كلمات المرور عند أول تسجيل دخول';
GO
