import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../models/report_model.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../database/task_repository.dart';

/// شاشة بناء التقارير المخصصة
///
/// تتيح للمستخدم إنشاء تقارير مخصصة وتحديد معايير التقرير
class CustomReportBuilderScreen extends StatefulWidget {
  final String title;
  final String? description;
  final ReportType type;
  final ReportCriteria initialCriteria;
  final String? reportId; // إضافة معرف التقرير للتعديل

  const CustomReportBuilderScreen({
    super.key,
    required this.title,
    this.description,
    required this.type,
    required this.initialCriteria,
    this.reportId, // معرف التقرير للتعديل (null في حالة إنشاء تقرير جديد)
  });

  @override
  State<CustomReportBuilderScreen> createState() => _CustomReportBuilderScreenState();
}

class _CustomReportBuilderScreenState extends State<CustomReportBuilderScreen> {
  final ReportRepository _reportRepository = ReportRepository();
  final UserRepository _userRepository = UserRepository();
  final DepartmentRepository _departmentRepository = DepartmentRepository();
  final TaskRepository _taskRepository = TaskRepository();
  final AuthController _authController = Get.find<AuthController>();
  final Uuid _uuid = const Uuid();

  late ReportCriteria _criteria;

  List<User> _allUsers = [];
  List<Department> _allDepartments = [];
  List<Task> _allTasks = [];

  bool _isLoading = true;
  String? _errorMessage;

  // تواريخ البداية والنهاية
  DateTime? _startDate;
  DateTime? _endDate;

  // المستخدمون المحددون
  List<String> _selectedUserIds = [];

  // الأقسام المحددة
  List<String> _selectedDepartmentIds = [];

  // المهام المحددة
  List<String> _selectedTaskIds = [];

  // حالات المهام المحددة
  List<int> _selectedTaskStatuses = [];

  // أولويات المهام المحددة
  List<int> _selectedTaskPriorities = [];

  @override
  void initState() {
    super.initState();
    _criteria = widget.initialCriteria;
    _initializeCriteria();
    _loadData();
  }

  /// تهيئة معايير التقرير
  void _initializeCriteria() {
    _startDate = _criteria.startDate;
    _endDate = _criteria.endDate;
    _selectedUserIds = _criteria.userIds ?? [];
    _selectedDepartmentIds = _criteria.departmentIds ?? [];
    _selectedTaskIds = _criteria.taskIds ?? [];
    _selectedTaskStatuses = _criteria.taskStatuses ?? [];
    _selectedTaskPriorities = _criteria.taskPriorities ?? [];
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل المستخدمين
      _allUsers = await _userRepository.getAllUsers();

      // تحميل الأقسام
      _allDepartments = await _departmentRepository.getAllDepartments();

      // تحميل المهام
      _allTasks = await _taskRepository.getAllTasks();
      _allTasks = _allTasks.where((task) => !task.isDeleted).toList();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// حفظ التقرير
  Future<void> _saveReport() async {
    // تخزين مراجع السياق قبل العمليات غير المتزامنة
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigatorContext = Navigator.of(context);

    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(widget.reportId != null ? 'جاري تحديث التقرير...' : 'جاري حفظ التقرير...'),
          ],
        ),
      ),
    );

    try {
      // إنشاء معايير التقرير
      final criteria = ReportCriteria(
        startDate: _startDate,
        endDate: _endDate,
        userIds: _selectedUserIds.isEmpty ? null : _selectedUserIds,
        departmentIds: _selectedDepartmentIds.isEmpty ? null : _selectedDepartmentIds,
        taskIds: _selectedTaskIds.isEmpty ? null : _selectedTaskIds,
        taskStatuses: _selectedTaskStatuses.isEmpty ? null : _selectedTaskStatuses,
        taskPriorities: _selectedTaskPriorities.isEmpty ? null : _selectedTaskPriorities,
      );

      if (widget.reportId != null) {
        // تحديث تقرير موجود
        // الحصول على التقرير الحالي
        final existingReport = await _reportRepository.getReportById(widget.reportId!);

        if (existingReport != null) {
          // تحديث التقرير
          final updatedReport = Report(
            id: widget.reportId!,
            title: widget.title,
            description: widget.description,
            type: widget.type,
            createdById: existingReport.createdById,
            createdAt: existingReport.createdAt,
            criteria: jsonEncode(criteria.toJson()),
            isShared: existingReport.isShared,
            sharedWithUserIds: existingReport.sharedWithUserIds,
            filePath: existingReport.filePath,
            format: existingReport.format,
            fileSize: existingReport.fileSize,
            lastExportedAt: existingReport.lastExportedAt,
          );

          // حفظ التقرير المحدث
          await _reportRepository.updateReport(updatedReport);
        } else {
          throw Exception('لم يتم العثور على التقرير للتعديل');
        }
      } else {
        // إنشاء تقرير جديد
        final report = Report(
          id: _uuid.v4(),
          title: widget.title,
          description: widget.description,
          type: widget.type,
          createdById: _authController.currentUser.value!.id,
          createdAt: DateTime.now(),
          criteria: jsonEncode(criteria.toJson()),
          isShared: false,
        );

        // حفظ التقرير الجديد
        await _reportRepository.createReport(report);
      }

      // التحقق من أن الحالة لا تزال مرتبطة قبل استخدام السياق
      if (!mounted) return;

      // إغلاق مؤشر التقدم
      navigatorContext.pop();

      // إغلاق الشاشة
      navigatorContext.pop(true);

      // عرض رسالة النجاح
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(widget.reportId != null ? 'تم تحديث التقرير بنجاح' : 'تم حفظ التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // التحقق من أن الحالة لا تزال مرتبطة قبل استخدام السياق
      if (!mounted) return;

      // إغلاق مؤشر التقدم
      navigatorContext.pop();

      // عرض رسالة الخطأ
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء ${widget.reportId != null ? 'تحديث' : 'حفظ'} التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض منتقي التاريخ
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? (_startDate ?? DateTime.now()) : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  /// عرض مربع حوار اختيار المستخدمين
  Future<void> _showUserSelectionDialog() async {
    final List<String> tempSelectedUserIds = List.from(_selectedUserIds);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المستخدمين'),
        content: SizedBox(
          width: double.maxFinite,
          child: StatefulBuilder(
            builder: (context, setState) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: _allUsers.length,
                itemBuilder: (context, index) {
                  final user = _allUsers[index];
                  return CheckboxListTile(
                    title: Text(user.name),
                    subtitle: Text(user.email),
                    value: tempSelectedUserIds.contains(user.id),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          tempSelectedUserIds.add(user.id);
                        } else {
                          tempSelectedUserIds.remove(user.id);
                        }
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedUserIds = tempSelectedUserIds;
              });
              Navigator.of(context).pop();
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار اختيار الأقسام
  Future<void> _showDepartmentSelectionDialog() async {
    final List<String> tempSelectedDepartmentIds = List.from(_selectedDepartmentIds);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الأقسام'),
        content: SizedBox(
          width: double.maxFinite,
          child: StatefulBuilder(
            builder: (context, setState) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: _allDepartments.length,
                itemBuilder: (context, index) {
                  final department = _allDepartments[index];
                  return CheckboxListTile(
                    title: Text(department.name),
                    subtitle: Text(department.description ?? ''),
                    value: tempSelectedDepartmentIds.contains(department.id),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          tempSelectedDepartmentIds.add(department.id);
                        } else {
                          tempSelectedDepartmentIds.remove(department.id);
                        }
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedDepartmentIds = tempSelectedDepartmentIds;
              });
              Navigator.of(context).pop();
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار اختيار المهام
  Future<void> _showTaskSelectionDialog() async {
    final List<String> tempSelectedTaskIds = List.from(_selectedTaskIds);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المهام'),
        content: SizedBox(
          width: double.maxFinite,
          child: StatefulBuilder(
            builder: (context, setState) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: _allTasks.length,
                itemBuilder: (context, index) {
                  final task = _allTasks[index];
                  return CheckboxListTile(
                    title: Text(task.title),
                    subtitle: Text('الحالة: ${task.status.toString().split('.').last}'),
                    value: tempSelectedTaskIds.contains(task.id),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          tempSelectedTaskIds.add(task.id);
                        } else {
                          tempSelectedTaskIds.remove(task.id);
                        }
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedTaskIds = tempSelectedTaskIds;
              });
              Navigator.of(context).pop();
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار اختيار حالات المهام
  Future<void> _showTaskStatusSelectionDialog() async {
    final List<int> tempSelectedTaskStatuses = List.from(_selectedTaskStatuses);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار حالات المهام'),
        content: SizedBox(
          width: double.maxFinite,
          child: StatefulBuilder(
            builder: (context, setState) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: TaskStatus.values.length,
                itemBuilder: (context, index) {
                  final status = TaskStatus.values[index];
                  return CheckboxListTile(
                    title: Text(status.toString().split('.').last),
                    value: tempSelectedTaskStatuses.contains(status.index),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          tempSelectedTaskStatuses.add(status.index);
                        } else {
                          tempSelectedTaskStatuses.remove(status.index);
                        }
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedTaskStatuses = tempSelectedTaskStatuses;
              });
              Navigator.of(context).pop();
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار اختيار أولويات المهام
  Future<void> _showTaskPrioritySelectionDialog() async {
    final List<int> tempSelectedTaskPriorities = List.from(_selectedTaskPriorities);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار أولويات المهام'),
        content: SizedBox(
          width: double.maxFinite,
          child: StatefulBuilder(
            builder: (context, setState) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: TaskPriority.values.length,
                itemBuilder: (context, index) {
                  final priority = TaskPriority.values[index];
                  return CheckboxListTile(
                    title: Text(priority.toString().split('.').last),
                    value: tempSelectedTaskPriorities.contains(priority.index),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          tempSelectedTaskPriorities.add(priority.index);
                        } else {
                          tempSelectedTaskPriorities.remove(priority.index);
                        }
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedTaskPriorities = tempSelectedTaskPriorities;
              });
              Navigator.of(context).pop();
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'تقرير حالة المهام';
      case ReportType.userPerformance:
        return 'تقرير أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'تقرير أداء القسم';
      case ReportType.timeTracking:
        return 'تقرير تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقرير تقدم المهام';
      case ReportType.taskDetails:
        return 'تقرير تفاصيل المهمة';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'تقرير غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء تقرير'),
        actions: [
          ElevatedButton.icon(
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text('حفظ', style: TextStyle(color: Colors.white)),
            onPressed: _saveReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success, // استخدام لون النجاح الأخضر للزر
              foregroundColor: Colors.white,
              elevation: 3, // إضافة ظل للزر ليكون أكثر بروزًا
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 8), // إضافة مسافة بين الزر وحافة الشاشة
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportInfoCard(),
          const SizedBox(height: 24),
          _buildDateRangeSection(),
          const SizedBox(height: 24),
          _buildFilterSection(),
        ],
      ),
    );
  }

  Widget _buildReportInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التقرير',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('عنوان التقرير'),
              subtitle: Text(widget.title),
              leading: const Icon(Icons.title),
            ),
            if (widget.description != null)
              ListTile(
                title: const Text('وصف التقرير'),
                subtitle: Text(widget.description!),
                leading: const Icon(Icons.description),
              ),
            ListTile(
              title: const Text('نوع التقرير'),
              subtitle: Text(_getReportTypeName(widget.type)),
              leading: const Icon(Icons.category),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نطاق التاريخ',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ البداية'),
                    subtitle: Text(_startDate != null
                        ? DateFormat('yyyy-MM-dd').format(_startDate!)
                        : 'غير محدد'),
                    leading: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح تاريخ البداية',
                  onPressed: _startDate == null
                      ? null
                      : () {
                          setState(() {
                            _startDate = null;
                          });
                        },
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ النهاية'),
                    subtitle: Text(_endDate != null
                        ? DateFormat('yyyy-MM-dd').format(_endDate!)
                        : 'غير محدد'),
                    leading: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, false),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح تاريخ النهاية',
                  onPressed: _endDate == null
                      ? null
                      : () {
                          setState(() {
                            _endDate = null;
                          });
                        },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معايير التصفية',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            _buildFilterItem(
              title: 'المستخدمون',
              subtitle: _selectedUserIds.isEmpty
                  ? 'جميع المستخدمين'
                  : '${_selectedUserIds.length} مستخدم محدد',
              icon: Icons.people,
              onTap: _showUserSelectionDialog,
              onClear: _selectedUserIds.isEmpty
                  ? null
                  : () {
                      setState(() {
                        _selectedUserIds = [];
                      });
                    },
            ),
            _buildFilterItem(
              title: 'الأقسام',
              subtitle: _selectedDepartmentIds.isEmpty
                  ? 'جميع الأقسام'
                  : '${_selectedDepartmentIds.length} قسم محدد',
              icon: Icons.business,
              onTap: _showDepartmentSelectionDialog,
              onClear: _selectedDepartmentIds.isEmpty
                  ? null
                  : () {
                      setState(() {
                        _selectedDepartmentIds = [];
                      });
                    },
            ),
            _buildFilterItem(
              title: 'المهام',
              subtitle: _selectedTaskIds.isEmpty
                  ? 'جميع المهام'
                  : '${_selectedTaskIds.length} مهمة محددة',
              icon: Icons.assignment,
              onTap: _showTaskSelectionDialog,
              onClear: _selectedTaskIds.isEmpty
                  ? null
                  : () {
                      setState(() {
                        _selectedTaskIds = [];
                      });
                    },
            ),
            _buildFilterItem(
              title: 'حالات المهام',
              subtitle: _selectedTaskStatuses.isEmpty
                  ? 'جميع الحالات'
                  : '${_selectedTaskStatuses.length} حالة محددة',
              icon: Icons.list_alt,
              onTap: _showTaskStatusSelectionDialog,
              onClear: _selectedTaskStatuses.isEmpty
                  ? null
                  : () {
                      setState(() {
                        _selectedTaskStatuses = [];
                      });
                    },
            ),
            _buildFilterItem(
              title: 'أولويات المهام',
              subtitle: _selectedTaskPriorities.isEmpty
                  ? 'جميع الأولويات'
                  : '${_selectedTaskPriorities.length} أولوية محددة',
              icon: Icons.low_priority,
              onTap: _showTaskPrioritySelectionDialog,
              onClear: _selectedTaskPriorities.isEmpty
                  ? null
                  : () {
                      setState(() {
                        _selectedTaskPriorities = [];
                      });
                    },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    VoidCallback? onClear,
  }) {
    return Row(
      children: [
        Expanded(
          child: ListTile(
            title: Text(title),
            subtitle: Text(subtitle),
            leading: Icon(icon),
            onTap: onTap,
          ),
        ),
        if (onClear != null)
          IconButton(
            icon: const Icon(Icons.clear),
            tooltip: 'مسح',
            onPressed: onClear,
          ),
      ],
    );
  }
}
