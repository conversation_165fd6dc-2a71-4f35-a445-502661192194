import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/dashboard/monday_style_dashboard_grid.dart';
import '../../widgets/dashboard/monday_style_add_widget_dialog.dart';

import '../../controllers/auth_controller.dart';


/// شاشة لوحة المعلومات بتصميم Monday.com
///
/// تعرض لوحة معلومات قابلة للتخصيص بتصميم مشابه لـ Monday.com
class MondayDashboardScreen extends StatefulWidget {
  const MondayDashboardScreen({super.key});

  @override
  State<MondayDashboardScreen> createState() => _MondayDashboardScreenState();
}

class _MondayDashboardScreenState extends State<MondayDashboardScreen> {
  late final DashboardController _dashboardController;
  late final AuthController _authController;

  bool _isLoading = true;
  bool _isEditing = false;
  String? _errorMessage;

  // معرف لوحة المعلومات الحالية
  String? _currentDashboardId;

  // قائمة لوحات المعلومات
  List<Dashboard> _dashboards = [];

  @override
  void initState() {
    super.initState();

    // التأكد من وجود المتحكمات المطلوبة
    _initControllers();
  }

  /// تهيئة المتحكمات المطلوبة
  void _initControllers() {
    try {
      // التحقق من وجود المتحكمات أو تسجيلها
      if (!Get.isRegistered<AuthController>()) {
        // إنشاء متحكم المصادقة وتهيئته
        final authController = AuthController();
        // تسجيل المتحكم بشكل دائم
        Get.put(authController, permanent: true);
        _authController = authController;
      } else {
        // الحصول على متحكم المصادقة الموجود
        _authController = Get.find<AuthController>();
        // التأكد من تهيئة المتحكم
        if (_authController.currentUser.value == null) {
          _authController.onInit();
        }
      }

      // التحقق من وجود متحكم لوحة المعلومات أو تسجيله
      if (!Get.isRegistered<DashboardController>()) {
        // إنشاء متحكم لوحة المعلومات وتهيئته
        final dashboardController = DashboardController();
        // تسجيل المتحكم بشكل دائم
        Get.put(dashboardController, permanent: true);
        _dashboardController = dashboardController;
      } else {
        // الحصول على متحكم لوحة المعلومات الموجود
        _dashboardController = Get.find<DashboardController>();
      }

      // التأكد من أن المستخدم الحالي موجود قبل تحميل البيانات
      if (_authController.currentUser.value != null) {
        // تحميل البيانات
        _loadDashboard();
      } else {
        setState(() {
          _errorMessage = 'لم يتم تسجيل الدخول. يرجى تسجيل الدخول أولاً.';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تهيئة المتحكمات: $e';
        _isLoading = false;
      });

      Get.snackbar(
        'خطأ',
        'خطأ في تهيئة المتحكمات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  /// تحميل لوحة المعلومات
  Future<void> _loadDashboard() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل لوحات المعلومات للمستخدم الحالي
      final userId = _authController.currentUser.value!.id;
      _dashboards = await _dashboardController.getDashboardsByUserId(userId);

      // إذا لم تكن هناك لوحات معلومات، إنشاء واحدة افتراضية
      if (_dashboards.isEmpty) {
        final newDashboard = await _createDefaultDashboard(userId);
        _dashboards = [newDashboard];
      }

      // تحديد لوحة المعلومات الافتراضية
      final defaultDashboard = _dashboards.firstWhere(
        (dashboard) => dashboard.isDefault,
        orElse: () => _dashboards.first,
      );

      setState(() {
        _currentDashboardId = defaultDashboard.id;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل لوحة المعلومات: $e';
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل لوحة المعلومات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<Dashboard> _createDefaultDashboard(String userId) async {
    final dashboard = Dashboard(
      id: const Uuid().v4(),
      title: 'لوحة المعلومات الرئيسية',
      ownerId: userId,
      widgets: [],
      isDefault: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _dashboardController.saveDashboard(dashboard);
    return dashboard;
  }



  /// إضافة عنصر جديد
  Future<void> _addWidget() async {
    if (_currentDashboardId == null) return;

    final result = await Get.dialog<bool>(
      MondayStyleAddWidgetDialog(
        dashboardId: _currentDashboardId!,
      ),
    );

    if (result == true) {
      // تحديث لوحة المعلومات بعد إضافة العنصر
      _loadDashboard();
    }
  }

  /// تغيير لوحة المعلومات الحالية
  void _changeDashboard(String dashboardId) {
    setState(() {
      _currentDashboardId = dashboardId;
    });
  }

  /// إنشاء لوحة معلومات جديدة
  Future<void> _createNewDashboard() async {
    final TextEditingController nameController = TextEditingController();

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('إنشاء لوحة معلومات جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم لوحة المعلومات',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      final userId = _authController.currentUser.value!.id;
      final dashboard = Dashboard(
        id: const Uuid().v4(),
        title: nameController.text,
        ownerId: userId,
        widgets: [],
        isDefault: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _dashboardController.saveDashboard(dashboard);
      _loadDashboard();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _buildDashboardSelector(),
        actions: [
          // زر وضع التعديل
          IconButton(
            icon: Icon(_isEditing ? Icons.done : Icons.edit),
            onPressed: () {
              setState(() {
                _isEditing = !_isEditing;
              });
            },
            tooltip: _isEditing ? 'إنهاء التعديل' : 'تعديل',
          ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboard,
            tooltip: 'تحديث',
          ),
          // زر إضافة لوحة معلومات جديدة
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: _createNewDashboard,
            tooltip: 'إضافة لوحة معلومات',
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.snackbar(
                'إعدادات',
                'هذه الميزة قيد التطوير',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.blue.withAlpha(180),
                colorText: Colors.white,
                margin: const EdgeInsets.all(8),
              );
            },
            tooltip: 'إعدادات',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _isEditing
          ? FloatingActionButton(
              onPressed: _addWidget,
              tooltip: 'إضافة عنصر',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  /// بناء منتقي لوحة المعلومات
  Widget _buildDashboardSelector() {
    if (_dashboards.isEmpty) {
      return const Text('لوحة معلومات Monday.com');
    }

    return DropdownButton<String>(
      value: _currentDashboardId,
      onChanged: (value) {
        if (value != null) {
          _changeDashboard(value);
        }
      },
      items: _dashboards.map((dashboard) {
        return DropdownMenuItem<String>(
          value: dashboard.id,
          child: Text(dashboard.title),
        );
      }).toList(),
      underline: Container(), // إزالة الخط السفلي
      dropdownColor: Theme.of(context).appBarTheme.backgroundColor,
      style: Theme.of(context).appBarTheme.titleTextStyle,
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDashboard,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_currentDashboardId == null) {
      return EmptyStateWidget(
        icon: Icons.dashboard_customize,
        title: 'لا توجد لوحة معلومات',
        message: 'لم يتم العثور على لوحة معلومات. يمكنك إنشاء لوحة معلومات جديدة.',
        buttonText: 'إنشاء لوحة معلومات',
        onButtonPressed: _createNewDashboard,
      );
    }

    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: MondayStyleDashboardGrid(
          dashboardId: _currentDashboardId!,
          isEditing: _isEditing,
        ),
      ),
    );
  }
}
