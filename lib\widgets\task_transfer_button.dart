import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_colors.dart';
import '../controllers/auth_controller.dart';
import '../controllers/user_controller.dart';
import '../models/task_model.dart';
import '../models/department_model.dart';
import 'user_selection_dialog.dart';

/// زر تحويل المهمة
/// يسمح بتحويل المهمة إلى مستخدم آخر مع إضافة تعليق ومرفقات
class TaskTransferButton extends StatefulWidget {
  final Task task;
  final Function(String userId, String comment, List<String> attachments) onTransfer;
  final bool isEnabled;

  const TaskTransferButton({
    super.key,
    required this.task,
    required this.onTransfer,
    this.isEnabled = true,
  });

  @override
  State<TaskTransferButton> createState() => _TaskTransferButtonState();
}

class _TaskTransferButtonState extends State<TaskTransferButton> {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: widget.isEnabled ? () => _showTransferDialog(context) : null,
      icon: const Icon(Icons.send),
      label: const Text('تحويل المهمة'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// عرض حوار تحويل المهمة
  /// يستخدم حوار اختيار المستخدم المحسن
  void _showTransferDialog(BuildContext context) async {
    final userController = Get.find<UserController>();
    final authController = Get.find<AuthController>();

    // تحميل المستخدمين إذا لم يتم تحميلهم بالفعل
    if (userController.users.isEmpty) {
      await userController.loadAllUsers();
    }

    // TODO: تحميل الإدارات عبر API
    // final departments = await apiService.getAllDepartments();
    final departments = <Department>[];

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => _TransferDialog(
        task: widget.task,
        users: userController.users,
        departments: departments,
        onTransfer: widget.onTransfer,
      ),
    );
  }
}

/// حوار تحويل المهمة الداخلي
class _TransferDialog extends StatefulWidget {
  final Task task;
  final List<User> users;
  final List<Department> departments;
  final Function(String userId, String comment, List<String> attachments) onTransfer;

  const _TransferDialog({
    required this.task,
    required this.users,
    required this.departments,
    required this.onTransfer,
  });

  @override
  State<_TransferDialog> createState() => _TransferDialogState();
}

class _TransferDialogState extends State<_TransferDialog> {
  final TextEditingController _commentController = TextEditingController();
  String? _selectedUserId;
  List<String> _attachments = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.6,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                const Icon(Icons.send, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'تحويل المهمة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات المهمة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المهمة: ${widget.task.title}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (widget.task.description?.isNotEmpty == true)
                    Text('الوصف: ${widget.task.description}'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // اختيار المستخدم
            Row(
              children: [
                Expanded(
                  child: Text(
                    _selectedUserId != null
                        ? 'المستخدم المحدد: ${_getUserName(_selectedUserId!)}'
                        : 'لم يتم اختيار مستخدم',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ElevatedButton(
                  onPressed: _showUserSelectionDialog,
                  child: const Text('اختيار مستخدم'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // تعليق التحويل
            TextField(
              controller: _commentController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'تعليق التحويل',
                hintText: 'اكتب تعليقاً حول سبب التحويل...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // المرفقات
            Row(
              children: [
                const Text('المرفقات:', style: TextStyle(fontWeight: FontWeight.bold)),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addAttachment,
                  icon: const Icon(Icons.attach_file),
                  label: const Text('إضافة مرفق'),
                ),
              ],
            ),
            if (_attachments.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                height: 100,
                child: ListView.builder(
                  itemCount: _attachments.length,
                  itemBuilder: (context, index) {
                    return ListTile(
                      leading: const Icon(Icons.attachment),
                      title: Text(_attachments[index]),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setState(() {
                            _attachments.removeAt(index);
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
            const SizedBox(height: 24),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _selectedUserId != null && !_isLoading
                      ? _performTransfer
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('تحويل'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showUserSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => UserSelectionDialog(
        users: widget.users,
        departments: widget.departments,
        multiSelect: false,
        title: 'اختيار المستخدم لتحويل المهمة',
        onUsersSelected: (userIds) {
          if (userIds.isNotEmpty) {
            setState(() {
              _selectedUserId = userIds.first;
            });
          }
        },
      ),
    );
  }

  Future<void> _addAttachment() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles();
      
      if (result != null) {
        setState(() {
          _attachments.add(result.files.single.name);
        });
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الملف: $e');
    }
  }

  Future<void> _performTransfer() async {
    if (_selectedUserId == null) return;

    setState(() => _isLoading = true);

    try {
      await widget.onTransfer(
        _selectedUserId!,
        _commentController.text,
        _attachments,
      );

      if (mounted) {
        Navigator.of(context).pop();
        Get.snackbar(
          'نجح',
          'تم تحويل المهمة بنجاح',
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحويل المهمة: $e');
      Get.snackbar(
        'خطأ',
        'فشل في تحويل المهمة',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getUserName(String userId) {
    final user = widget.users.firstWhere(
      (u) => u.id.toString() == userId,
      orElse: () => User(
        id: 0,
        name: 'مستخدم غير معروف',
        email: '',
        role: UserRole.user,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      ),
    );
    return user.name;
  }
}
