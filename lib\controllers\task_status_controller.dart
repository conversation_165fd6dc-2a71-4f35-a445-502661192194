import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../services/api/task_status_api_service.dart';

/// متحكم حالات المهام
class TaskStatusController extends GetxController {
  final TaskStatusApiService _apiService = TaskStatusApiService();

  // قوائم حالات المهام
  final RxList<TaskStatus> _allStatuses = <TaskStatus>[].obs;
  final RxList<TaskStatus> _filteredStatuses = <TaskStatus>[].obs;

  // حالة المهمة الحالية
  final Rx<TaskStatus?> _currentStatus = Rx<TaskStatus?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskStatus> get allStatuses => _allStatuses;
  List<TaskStatus> get filteredStatuses => _filteredStatuses;
  TaskStatus? get currentStatus => _currentStatus.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllStatuses();
  }

  /// تحميل جميع حالات المهام
  Future<void> loadAllStatuses() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final statuses = await _apiService.getAllStatuses();
      _allStatuses.assignAll(statuses);
      _applyFilters();
      debugPrint('تم تحميل ${statuses.length} حالة مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل حالات المهام: $e';
      debugPrint('خطأ في تحميل حالات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على حالة مهمة بالمعرف
  Future<void> getStatusById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final status = await _apiService.getStatusById(id);
      _currentStatus.value = status;
      debugPrint('تم تحميل حالة المهمة: ${status.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل حالة المهمة: $e';
      debugPrint('خطأ في تحميل حالة المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء حالة مهمة جديدة
  Future<bool> createStatus(TaskStatus status) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newStatus = await _apiService.createStatus(status);
      _allStatuses.add(newStatus);
      _applyFilters();
      debugPrint('تم إنشاء حالة مهمة جديدة: ${newStatus.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء حالة المهمة: $e';
      debugPrint('خطأ في إنشاء حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حالة مهمة
  Future<bool> updateStatus(int id, TaskStatus status) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateStatus(id, status);
      final index = _allStatuses.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allStatuses[index] = status;
        _applyFilters();
      }
      debugPrint('تم تحديث حالة المهمة: ${status.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة المهمة: $e';
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حالة مهمة
  Future<bool> deleteStatus(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteStatus(id);
      _allStatuses.removeWhere((s) => s.id == id);
      _applyFilters();
      debugPrint('تم حذف حالة المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف حالة المهمة: $e';
      debugPrint('خطأ في حذف حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الحالات النشطة فقط
  List<TaskStatus> get activeStatuses {
    return _allStatuses.where((status) => status.isActive).toList();
  }

  /// الحصول على حالة افتراضية
  TaskStatus? get defaultStatus {
    return _allStatuses.firstWhereOrNull((status) => status.isDefault);
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allStatuses.where((status) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!status.name.toLowerCase().contains(query) &&
            !status.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !status.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredStatuses.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllStatuses();
  }
}
