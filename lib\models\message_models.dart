import 'user_model.dart';

/// أنواع محتوى الرسائل
enum MessageContentType {
  text(1, 'نص'),
  image(2, 'صورة'),
  file(3, 'ملف'),
  voice(4, 'صوت'),
  video(5, 'فيديو'),
  location(6, 'موقع');

  const MessageContentType(this.value, this.displayName);
  
  final int value;
  final String displayName;

  static MessageContentType fromValue(int value) {
    return MessageContentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MessageContentType.text,
    );
  }
}

/// أولوية الرسائل
enum MessagePriority {
  normal(0, 'عادية'),
  important(1, 'مهمة'),
  urgent(2, 'عاجلة');

  const MessagePriority(this.value, this.displayName);
  
  final int value;
  final String displayName;

  static MessagePriority fromValue(int value) {
    return MessagePriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => MessagePriority.normal,
    );
  }
}

/// نموذج مجموعة الدردشة
class ChatGroup {
  final int id;
  final String name;
  final String? description;
  final String? avatar;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isPrivate;
  final int? maxMembers;

  // Navigation properties
  final User? createdByUser;
  final List<GroupMember>? members;
  final Message? lastMessage;

  const ChatGroup({
    required this.id,
    required this.name,
    this.description,
    this.avatar,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isPrivate = false,
    this.maxMembers,
    this.createdByUser,
    this.members,
    this.lastMessage,
  });

  factory ChatGroup.fromJson(Map<String, dynamic> json) {
    return ChatGroup(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      avatar: json['avatar'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isPrivate: json['isPrivate'] as bool? ?? false,
      maxMembers: json['maxMembers'] as int?,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      members: json['groupMembers'] != null 
          ? (json['groupMembers'] as List)
              .map((m) => GroupMember.fromJson(m as Map<String, dynamic>))
              .toList()
          : null,
      lastMessage: json['lastMessage'] != null 
          ? Message.fromJson(json['lastMessage'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isPrivate': isPrivate,
      'maxMembers': maxMembers,
    };
  }
}

/// نموذج عضو المجموعة
class GroupMember {
  final int id;
  final int groupId;
  final int userId;
  final int joinedAt;
  final bool isAdmin;
  final bool isModerator;
  final bool isActive;

  // Navigation properties
  final User? user;
  final ChatGroup? group;

  const GroupMember({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.joinedAt,
    this.isAdmin = false,
    this.isModerator = false,
    this.isActive = true,
    this.user,
    this.group,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      id: json['id'] as int,
      groupId: json['groupId'] as int,
      userId: json['userId'] as int,
      joinedAt: json['joinedAt'] as int,
      isAdmin: json['isAdmin'] as bool? ?? false,
      isModerator: json['isModerator'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      group: json['group'] != null 
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'joinedAt': joinedAt,
      'isAdmin': isAdmin,
      'isModerator': isModerator,
      'isActive': isActive,
    };
  }
}

/// نموذج الرسالة
class Message {
  final int id;
  final int groupId;
  final int senderId;
  final String content;
  final MessageContentType contentType;
  final int? replyToMessageId;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isRead;
  final bool isPinned;
  final int? pinnedAt;
  final int? pinnedBy;
  final MessagePriority priority;
  final bool isMarkedForFollowUp;
  final int? followUpAt;
  final int? markedForFollowUpBy;
  final bool isEdited;
  final int? receiverId;
  final int? sentAt;

  // Navigation properties
  final ChatGroup? group;
  final User? sender;
  final User? receiver;
  final Message? replyToMessage;
  final List<MessageAttachment>? attachments;
  final List<MessageReaction>? reactions;

  const Message({
    required this.id,
    required this.groupId,
    required this.senderId,
    required this.content,
    this.contentType = MessageContentType.text,
    this.replyToMessageId,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isRead = false,
    this.isPinned = false,
    this.pinnedAt,
    this.pinnedBy,
    this.priority = MessagePriority.normal,
    this.isMarkedForFollowUp = false,
    this.followUpAt,
    this.markedForFollowUpBy,
    this.isEdited = false,
    this.receiverId,
    this.sentAt,
    this.group,
    this.sender,
    this.receiver,
    this.replyToMessage,
    this.attachments,
    this.reactions,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as int,
      groupId: json['groupId'] as int,
      senderId: json['senderId'] as int,
      content: json['content'] as String,
      contentType: MessageContentType.fromValue(json['contentType'] as int),
      replyToMessageId: json['replyToMessageId'] as int?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isRead: json['isRead'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      pinnedAt: json['pinnedAt'] as int?,
      pinnedBy: json['pinnedBy'] as int?,
      priority: MessagePriority.fromValue(json['priority'] as int? ?? 0),
      isMarkedForFollowUp: json['isMarkedForFollowUp'] as bool? ?? false,
      followUpAt: json['followUpAt'] as int?,
      markedForFollowUpBy: json['markedForFollowUpBy'] as int?,
      isEdited: json['isEdited'] as bool? ?? false,
      receiverId: json['receiverId'] as int?,
      sentAt: json['sentAt'] as int?,
      group: json['group'] != null 
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
      sender: json['sender'] != null 
          ? User.fromJson(json['sender'] as Map<String, dynamic>)
          : null,
      receiver: json['receiver'] != null 
          ? User.fromJson(json['receiver'] as Map<String, dynamic>)
          : null,
      replyToMessage: json['replyToMessage'] != null 
          ? Message.fromJson(json['replyToMessage'] as Map<String, dynamic>)
          : null,
      attachments: json['messageAttachments'] != null 
          ? (json['messageAttachments'] as List)
              .map((a) => MessageAttachment.fromJson(a as Map<String, dynamic>))
              .toList()
          : null,
      reactions: json['messageReactions'] != null 
          ? (json['messageReactions'] as List)
              .map((r) => MessageReaction.fromJson(r as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'senderId': senderId,
      'content': content,
      'contentType': contentType.value,
      'replyToMessageId': replyToMessageId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isRead': isRead,
      'isPinned': isPinned,
      'pinnedAt': pinnedAt,
      'pinnedBy': pinnedBy,
      'priority': priority.value,
      'isMarkedForFollowUp': isMarkedForFollowUp,
      'followUpAt': followUpAt,
      'markedForFollowUpBy': markedForFollowUpBy,
      'isEdited': isEdited,
      'receiverId': receiverId,
      'sentAt': sentAt,
    };
  }

  /// التحقق من كون الرسالة من المستخدم الحالي
  bool isFromUser(int userId) => senderId == userId;

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);
}

/// نموذج مرفق الرسالة
class MessageAttachment {
  final int id;
  final int messageId;
  final String fileName;
  final String filePath;
  final String fileType;
  final int fileSize;
  final int uploadedAt;

  const MessageAttachment({
    required this.id,
    required this.messageId,
    required this.fileName,
    required this.filePath,
    required this.fileType,
    required this.fileSize,
    required this.uploadedAt,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] as int,
      messageId: json['messageId'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileType: json['fileType'] as String,
      fileSize: json['fileSize'] as int,
      uploadedAt: json['uploadedAt'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'fileName': fileName,
      'filePath': filePath,
      'fileType': fileType,
      'fileSize': fileSize,
      'uploadedAt': uploadedAt,
    };
  }
}

/// نموذج تفاعل الرسالة
class MessageReaction {
  final int id;
  final int messageId;
  final int userId;
  final String emoji;
  final int createdAt;

  // Navigation properties
  final User? user;

  const MessageReaction({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.emoji,
    required this.createdAt,
    this.user,
  });

  factory MessageReaction.fromJson(Map<String, dynamic> json) {
    return MessageReaction(
      id: json['id'] as int,
      messageId: json['messageId'] as int,
      userId: json['userId'] as int,
      emoji: json['emoji'] as String,
      createdAt: json['createdAt'] as int,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'emoji': emoji,
      'createdAt': createdAt,
    };
  }
}
