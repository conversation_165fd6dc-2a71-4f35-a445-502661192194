import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/auth_models.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

class AuthController extends GetxController {
  final Rx<UserInfo?> currentUser = Rx<UserInfo?>(null);
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  late final AuthService _authService;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      // التأكد من تهيئة StorageService
      if (!Get.isRegistered<StorageService>()) {
        final storageService = StorageService();
        await storageService.initialize();
        Get.put(storageService, permanent: true);
      }

      // تهيئة AuthService
      _authService = AuthService();
      await _authService.initialize();

      // تحميل المستخدم الحالي إذا كان متوفراً
      currentUser.value = _authService.currentUser;

      debugPrint('تم تهيئة AuthController بنجاح');
      update();
    } catch (e) {
      debugPrint('خطأ في تهيئة AuthController: $e');
      error.value = 'خطأ في تهيئة النظام: ${e.toString()}';
    }
  }

  bool get isLoggedIn => _authService.isLoggedIn;
  bool get isSuperAdmin => currentUser.value?.role == UserRole.superAdmin;
  bool get isAdmin => currentUser.value?.role == UserRole.admin;
  bool get isManager => currentUser.value?.role == UserRole.manager;
  bool get isSupervisor => currentUser.value?.role == UserRole.supervisor;

  /// التحقق من كون المستخدم مدير (أي نوع من المديرين)
  bool get isAnyAdmin => isSuperAdmin || isAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارية عامة (مدير النظام العام فقط)
  bool get hasSystemAdminRights => isSuperAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارة الإدارة (مدير النظام العام أو مدير إدارة)
  bool get hasDepartmentAdminRights => isSuperAdmin || isAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارية (مدير أو أعلى)
  bool get hasManagerRights => currentUser.value?.role.isManagerOrAbove ?? false;

  /// التحقق من كون المستخدم يمكنه رؤية جميع الصفحات (مدير أو أعلى)
  bool get canSeeAllPages => hasManagerRights;

  // Load current user from storage
  Future<void> loadCurrentUser() async {
    isLoading.value = true;

    try {
      currentUser.value = _authService.currentUser;
      error.value = '';
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Login user
  Future<bool> login(String email, String password) async {
    isLoading.value = true;
    error.value = '';
    update();

    try {
      // تسجيل الدخول واستلام بيانات المستخدم
      final authResponse = await _authService.login(email, password);

      // التحقق من نجاح تسجيل الدخول
      if (authResponse.success && authResponse.user != null) {
        currentUser.value = authResponse.user;

        // طباعة معلومات تشخيصية
        debugPrint(
            'تم تسجيل الدخول للمستخدم: ${currentUser.value!.name} (${currentUser.value!.id})');
        debugPrint('الدور: ${currentUser.value!.role}');

        update();
        return true;
      } else {
        error.value = authResponse.message.isNotEmpty
            ? authResponse.message
            : 'بيانات الدخول غير صحيحة';
        update();
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      debugPrint('خطأ في تسجيل الدخول: $e');
      update();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Register new user
  Future<bool> register(RegisterRequest request) async {
    isLoading.value = true;
    error.value = '';

    try {
      final authResponse = await _authService.register(request);

      if (authResponse.success && authResponse.user != null) {
        currentUser.value = authResponse.user;
        update();
        return true;
      } else {
        error.value = authResponse.message.isNotEmpty
            ? authResponse.message
            : 'فشل في التسجيل';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Logout user
  Future<bool> logout() async {
    isLoading.value = true;

    try {
      final result = await _authService.logout();
      if (result) {
        currentUser.value = null;
        update();
        return true;
      } else {
        error.value = 'فشل في تسجيل الخروج';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Clear error
  void clearError() {
    error.value = '';
    update();
  }

  // Set error message
  void setError(String errorMessage) {
    error.value = errorMessage;
    update();
  }

  // Update user profile
  Future<bool> updateUserProfile(Map<String, dynamic> userData) async {
    isLoading.value = true;
    error.value = '';

    try {
      final user = await _authService.updateUserProfile(userData);
      if (user != null) {
        currentUser.value = user;
        update();
        return true;
      } else {
        error.value = 'فشل تحديث الملف الشخصي';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _authService.changePassword(currentPassword, newPassword);
      if (!result) {
        error.value = _authService.error.isNotEmpty
            ? _authService.error
            : 'فشل في تغيير كلمة المرور';
      }
      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Validate token
  Future<bool> validateToken() async {
    try {
      final user = await _authService.validateToken();
      if (user != null) {
        currentUser.value = user;
        update();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الرمز: $e');
      return false;
    }
  }
}
