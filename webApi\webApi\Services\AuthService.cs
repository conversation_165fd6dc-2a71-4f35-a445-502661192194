using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Auth;
using BCrypt.Net;

namespace webApi.Services;

/// <summary>
/// خدمة المصادقة الرئيسية
/// </summary>
public class AuthService : IAuthService
{
    private readonly TasksDbContext _context;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(TasksDbContext context, IJwtService jwtService, ILogger<AuthService> logger)
    {
        _context = context;
        _jwtService = jwtService;
        _logger = logger;
    }

    /// <summary>
    /// تسجيل الدخول
    /// </summary>
    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            // البحث عن المستخدم بالبريد الإلكتروني أو اسم المستخدم
            var user = await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .FirstOrDefaultAsync(u => 
                    (u.Email == request.UsernameOrEmail || u.Username == request.UsernameOrEmail) &&
                    !u.IsDeleted);

            if (user == null)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                };
            }

            // التحقق من حالة المستخدم
            if (!user.IsActive)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "حساب المستخدم غير مفعل"
                };
            }

            // التحقق من كلمة المرور
            if (!BCrypt.Net.BCrypt.Verify(request.Password, user.Password))
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                };
            }

            // إنشاء الرموز
            var accessToken = _jwtService.GenerateAccessToken(user);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // تحديث آخر تسجيل دخول وحالة الاتصال
            user.LastLogin = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            user.IsOnline = true;
            user.LastSeen = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            // إنشاء معلومات المستخدم
            var userInfo = new UserInfo
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Username = user.Username,
                Role = (UserRole)user.Role,
                RoleName = user.RoleNavigation?.Name ?? ((UserRole)user.Role).ToString(),
                DepartmentId = user.DepartmentId,
                DepartmentName = user.Department?.Name,
                ProfileImage = user.ProfileImage,
                IsActive = user.IsActive,
                IsOnline = user.IsOnline
            };

            _logger.LogInformation("تم تسجيل دخول المستخدم {UserId} بنجاح", user.Id);

            return new AuthResponse
            {
                Success = true,
                Message = "تم تسجيل الدخول بنجاح",
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresAt = _jwtService.GetTokenExpiration(accessToken),
                User = userInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تسجيل الدخول");
            return new AuthResponse
            {
                Success = false,
                Message = "حدث خطأ أثناء تسجيل الدخول"
            };
        }
    }

    /// <summary>
    /// التسجيل
    /// </summary>
    public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
    {
        try
        {
            // التحقق من وجود المستخدم
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == request.Email && !u.IsDeleted);

            if (existingUser != null)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "البريد الإلكتروني مستخدم بالفعل"
                };
            }

            // التحقق من اسم المستخدم إذا تم توفيره
            if (!string.IsNullOrEmpty(request.Username))
            {
                var existingUsername = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username && !u.IsDeleted);

                if (existingUsername != null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "اسم المستخدم مستخدم بالفعل"
                    };
                }
            }

            // التحقق من وجود القسم إذا تم توفيره
            if (request.DepartmentId.HasValue)
            {
                var departmentExists = await _context.Departments
                    .AnyAsync(d => d.Id == request.DepartmentId.Value);

                if (!departmentExists)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "القسم المحدد غير موجود"
                    };
                }
            }

            // تشفير كلمة المرور
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(request.Password);

            // إنشاء المستخدم الجديد
            var newUser = new User
            {
                Name = request.Name,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                Username = request.Username,
                Password = hashedPassword,
                DepartmentId = request.DepartmentId,
                Role = (int)request.Role,
                IsActive = true,
                IsDeleted = false,
                IsOnline = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            _context.Users.Add(newUser);
            await _context.SaveChangesAsync();

            // تحميل البيانات المرتبطة
            await _context.Entry(newUser)
                .Reference(u => u.Department)
                .LoadAsync();

            await _context.Entry(newUser)
                .Reference(u => u.RoleNavigation)
                .LoadAsync();

            _logger.LogInformation("تم إنشاء مستخدم جديد {UserId}", newUser.Id);

            // تسجيل الدخول التلقائي
            var loginRequest = new LoginRequest
            {
                UsernameOrEmail = request.Email,
                Password = request.Password
            };

            return await LoginAsync(loginRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في التسجيل");
            return new AuthResponse
            {
                Success = false,
                Message = "حدث خطأ أثناء التسجيل"
            };
        }
    }

    /// <summary>
    /// تحديث الرمز
    /// </summary>
    public Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
    {
        try
        {
            // في التطبيق الحقيقي، يجب حفظ رموز التحديث في قاعدة البيانات
            // هنا سنقوم بتنفيذ مبسط

            return System.Threading.Tasks.Task.FromResult(new AuthResponse
            {
                Success = false,
                Message = "رمز التحديث غير صحيح"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديث الرمز");
            return System.Threading.Tasks.Task.FromResult(new AuthResponse
            {
                Success = false,
                Message = "حدث خطأ أثناء تحديث الرمز"
            });
        }
    }

    /// <summary>
    /// تسجيل الخروج
    /// </summary>
    public async Task<bool> LogoutAsync(int userId, string refreshToken)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.IsOnline = false;
                user.LastSeen = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تسجيل خروج المستخدم {UserId}", userId);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تسجيل الخروج للمستخدم {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// تغيير كلمة المرور
    /// </summary>
    public async Task<AuthResponse> ChangePasswordAsync(int userId, ChangePasswordRequest request)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "المستخدم غير موجود"
                };
            }

            // التحقق من كلمة المرور الحالية
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.Password))
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "كلمة المرور الحالية غير صحيحة"
                };
            }

            // تشفير كلمة المرور الجديدة
            user.Password = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم تغيير كلمة مرور المستخدم {UserId}", userId);

            return new AuthResponse
            {
                Success = true,
                Message = "تم تغيير كلمة المرور بنجاح"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تغيير كلمة المرور للمستخدم {UserId}", userId);
            return new AuthResponse
            {
                Success = false,
                Message = "حدث خطأ أثناء تغيير كلمة المرور"
            };
        }
    }

    /// <summary>
    /// نسيان كلمة المرور
    /// </summary>
    public async Task<AuthResponse> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == request.Email && !u.IsDeleted);

            if (user == null)
            {
                // لأسباب أمنية، نعيد نفس الرسالة حتى لو لم يكن المستخدم موجود
                return new AuthResponse
                {
                    Success = true,
                    Message = "إذا كان البريد الإلكتروني موجود، ستتلقى رسالة لإعادة تعيين كلمة المرور"
                };
            }

            // هنا يجب إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
            // سنقوم بتنفيذ مبسط

            _logger.LogInformation("طلب إعادة تعيين كلمة المرور للمستخدم {UserId}", user.Id);

            return new AuthResponse
            {
                Success = true,
                Message = "إذا كان البريد الإلكتروني موجود، ستتلقى رسالة لإعادة تعيين كلمة المرور"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في نسيان كلمة المرور");
            return new AuthResponse
            {
                Success = false,
                Message = "حدث خطأ أثناء معالجة الطلب"
            };
        }
    }

    /// <summary>
    /// التحقق من صحة المستخدم
    /// </summary>
    public async Task<UserInfo?> ValidateUserAsync(int userId)
    {
        try
        {
            var user = await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted && u.IsActive);

            if (user == null)
                return null;

            return new UserInfo
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Username = user.Username,
                Role = (UserRole)user.Role,
                RoleName = user.RoleNavigation?.Name ?? ((UserRole)user.Role).ToString(),
                DepartmentId = user.DepartmentId,
                DepartmentName = user.Department?.Name,
                ProfileImage = user.ProfileImage,
                IsActive = user.IsActive,
                IsOnline = user.IsOnline
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في التحقق من المستخدم {UserId}", userId);
            return null;
        }
    }

    /// <summary>
    /// تحديث آخر تسجيل دخول
    /// </summary>
    public async Task<bool> UpdateLastLoginAsync(int userId)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.LastLogin = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديث آخر تسجيل دخول للمستخدم {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// تحديث حالة الاتصال
    /// </summary>
    public async Task<bool> UpdateOnlineStatusAsync(int userId, bool isOnline)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.IsOnline = isOnline;
                if (!isOnline)
                {
                    user.LastSeen = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحديث حالة الاتصال للمستخدم {UserId}", userId);
            return false;
        }
    }
}
