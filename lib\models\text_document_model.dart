import 'dart:convert';

/// أنواع المستندات النصية
enum TextDocumentType {
  note('note', 'ملاحظة'),
  report('report', 'تقرير'),
  memo('memo', 'مذكرة'),
  letter('letter', 'رسالة'),
  contract('contract', 'عقد'),
  other('other', 'أخرى');

  const TextDocumentType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static TextDocumentType fromValue(String value) {
    return TextDocumentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TextDocumentType.note,
    );
  }
}

/// نموذج المستند النصي
class TextDocument {
  final String id;
  final String title;
  final String content;
  final TextDocumentType type;
  final String? taskId;
  final int? userId;
  final bool isShared;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  const TextDocument({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    this.taskId,
    this.userId,
    this.isShared = false,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  /// إنشاء مستند من JSON
  factory TextDocument.fromJson(Map<String, dynamic> json) {
    return TextDocument(
      id: json['id']?.toString() ?? '',
      title: json['title'] as String? ?? '',
      content: json['content'] as String? ?? '',
      type: TextDocumentType.fromValue(json['type'] as String? ?? 'note'),
      taskId: json['taskId']?.toString(),
      userId: json['userId'] as int?,
      isShared: json['isShared'] as bool? ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );
  }

  /// تحويل المستند إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.value,
      'taskId': taskId,
      'userId': userId,
      'isShared': isShared,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// إنشاء نسخة محدثة من المستند
  TextDocument copyWith({
    String? id,
    String? title,
    String? content,
    TextDocumentType? type,
    String? taskId,
    int? userId,
    bool? isShared,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return TextDocument(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      isShared: isShared ?? this.isShared,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  /// الحصول على النص الخام من المحتوى
  String getDocumentText() {
    try {
      // محاولة تحليل المحتوى كـ JSON
      final decoded = jsonDecode(content);
      if (decoded is Map<String, dynamic> && decoded.containsKey('text')) {
        return decoded['text'] as String;
      }
      // إذا لم يكن JSON، إرجاع المحتوى كما هو
      return content;
    } catch (e) {
      // إذا فشل التحليل، إرجاع المحتوى كما هو
      return content;
    }
  }

  /// تحويل النص إلى تنسيق JSON للحفظ
  static String textToJson(String text) {
    return jsonEncode({
      'text': text,
      'format': 'plain',
      'version': '1.0',
    });
  }

  @override
  String toString() {
    return 'TextDocument(id: $id, title: $title, type: ${type.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextDocument && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مستند جديد
class CreateTextDocumentRequest {
  final String title;
  final String content;
  final TextDocumentType type;
  final String? taskId;
  final bool isShared;

  const CreateTextDocumentRequest({
    required this.title,
    required this.content,
    required this.type,
    this.taskId,
    this.isShared = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'type': type.value,
      'taskId': taskId,
      'isShared': isShared,
    };
  }
}

/// نموذج طلب تحديث مستند
class UpdateTextDocumentRequest {
  final String? title;
  final String? content;
  final TextDocumentType? type;
  final bool? isShared;

  const UpdateTextDocumentRequest({
    this.title,
    this.content,
    this.type,
    this.isShared,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (content != null) json['content'] = content;
    if (type != null) json['type'] = type!.value;
    if (isShared != null) json['isShared'] = isShared;
    return json;
  }
}
