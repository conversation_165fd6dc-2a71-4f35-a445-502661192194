# نظام المصادقة والتفويض - Authentication & Authorization System

## نظرة عامة

تم تنفيذ نظام مصادقة وتفويض شامل ومتكامل يدعم:

- **JWT Authentication** - مصادقة باستخدام رموز JWT
- **Role-based Authorization** - تفويض مبني على الأدوار
- **Password Hashing** - تشفير كلمات المرور باستخدام BCrypt
- **Refresh Tokens** - رموز التحديث للجلسات الطويلة
- **Login History** - سجل تسجيل الدخول
- **Account Lockout** - قفل الحساب بعد محاولات فاشلة

## الميزات المنفذة

### 1. المصادقة (Authentication)

#### نقاط النهاية (Endpoints):
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - التسجيل
- `POST /api/auth/logout` - تسجيل الخروج
- `POST /api/auth/refresh-token` - تحديث الرمز
- `GET /api/auth/profile` - الملف الشخصي
- `POST /api/auth/change-password` - تغيير كلمة المرور
- `POST /api/auth/forgot-password` - نسيان كلمة المرور
- `GET /api/auth/validate-token` - التحقق من صحة الرمز
- `POST /api/auth/update-online-status` - تحديث حالة الاتصال

#### أمثلة الاستخدام:

**تسجيل الدخول:**
```json
POST /api/auth/login
{
  "usernameOrEmail": "<EMAIL>",
  "password": "123456",
  "rememberMe": true
}
```

**التسجيل:**
```json
POST /api/auth/register
{
  "name": "أحمد محمد",
  "firstName": "أحمد",
  "lastName": "محمد",
  "email": "<EMAIL>",
  "username": "ahmed123",
  "password": "123456",
  "confirmPassword": "123456",
  "departmentId": 1,
  "role": 1
}
```

### 2. التفويض (Authorization)

#### الأدوار المتاحة:
1. **User (1)** - مستخدم عادي
2. **Supervisor (2)** - مشرف
3. **Manager (3)** - مدير
4. **Admin (4)** - مدير عام
5. **SuperAdmin (5)** - مدير النظام

#### خصائص التفويض:
- `[RoleAuthorize(UserRole.Admin, UserRole.SuperAdmin)]` - أدوار محددة
- `[AdminOnly]` - مديرين عامين فقط
- `[ManagerOrAbove]` - مديرين وما فوق
- `[SupervisorOrAbove]` - مشرفين وما فوق
- `[SuperAdminOnly]` - مدير النظام فقط

### 3. الحماية والأمان

#### تشفير كلمات المرور:
- استخدام BCrypt لتشفير كلمات المرور
- Salt عشوائي لكل كلمة مرور
- مقاومة هجمات Rainbow Table

#### JWT Security:
- رموز موقعة رقمياً
- انتهاء صلاحية قابل للتكوين
- Claims مخصصة للمستخدم والدور

#### Account Security:
- قفل الحساب بعد 5 محاولات فاشلة
- قفل لمدة 30 دقيقة
- تسجيل جميع محاولات تسجيل الدخول

## التكوين

### إعدادات JWT في appsettings.json:
```json
{
  "Jwt": {
    "SecretKey": "TasksManagementSystemSecretKey2024!@#$%^&*()_+",
    "Issuer": "TasksManagementAPI",
    "Audience": "TasksManagementClient",
    "AccessTokenExpirationMinutes": 60,
    "RefreshTokenExpirationDays": 7
  }
}
```

### تكوين الخدمات في Program.cs:
```csharp
// إضافة خدمات المصادقة
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IAuthService, AuthService>();

// تكوين JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options => {
        // تكوين JWT
    });

// إضافة Authorization
builder.Services.AddAuthorization();
```

## قاعدة البيانات

### الجداول الجديدة:
1. **refresh_tokens** - رموز التحديث
2. **login_history** - سجل تسجيل الدخول
3. **failed_login_attempts** - محاولات تسجيل الدخول الفاشلة
4. **password_reset_tokens** - رموز إعادة تعيين كلمة المرور

### الحقول الجديدة في جدول users:
- `password_changed_at` - تاريخ آخر تغيير لكلمة المرور
- `failed_login_attempts` - عدد المحاولات الفاشلة
- `locked_until` - تاريخ انتهاء قفل الحساب
- `two_factor_enabled` - تفعيل المصادقة الثنائية
- `two_factor_secret` - مفتاح المصادقة الثنائية

### تشغيل سكريبت التحديث:
```sql
-- تشغيل سكريبت تحديث قاعدة البيانات
-- ملف: SQL_Scripts/UpdateAuthenticationSystem.sql
```

## الاستخدام في Swagger

1. افتح Swagger UI: `https://localhost:7111/swagger`
2. سجل دخول باستخدام `/api/auth/login`
3. انسخ الـ Access Token من الاستجابة
4. اضغط على زر "Authorize" في Swagger
5. أدخل: `Bearer {your-token-here}`
6. الآن يمكنك الوصول للـ APIs المحمية

## أمثلة الاستجابات

### استجابة تسجيل الدخول الناجح:
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "base64-encoded-refresh-token",
  "expiresAt": "2024-01-01T12:00:00Z",
  "tokenType": "Bearer",
  "user": {
    "id": 1,
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "username": "ahmed123",
    "role": 4,
    "roleName": "Admin",
    "departmentId": 1,
    "departmentName": "تقنية المعلومات",
    "isActive": true,
    "isOnline": true
  }
}
```

### استجابة خطأ المصادقة:
```json
{
  "success": false,
  "message": "اسم المستخدم أو كلمة المرور غير صحيحة"
}
```

## الأمان والاعتبارات

### كلمات المرور:
- الحد الأدنى: 6 أحرف
- يُنصح بـ 8+ أحرف مع أرقام ورموز
- تشفير BCrypt مع cost factor = 11

### JWT Tokens:
- انتهاء صلاحية قصير للـ Access Token (60 دقيقة)
- انتهاء صلاحية طويل للـ Refresh Token (7 أيام)
- تخزين آمن للـ Secret Key

### حماية الحساب:
- قفل تلقائي بعد 5 محاولات فاشلة
- تسجيل جميع الأنشطة
- تنظيف دوري للبيانات القديمة

## الصيانة

### تنظيف البيانات:
```sql
-- تشغيل إجراء التنظيف دورياً
EXEC sp_CleanupExpiredTokens;
```

### مراقبة الأمان:
- مراجعة سجلات تسجيل الدخول
- مراقبة المحاولات الفاشلة
- تحديث كلمات المرور دورياً

## الدعم والتطوير المستقبلي

### ميزات مخططة:
- [ ] المصادقة الثنائية (2FA)
- [ ] تسجيل الدخول بـ OAuth (Google, Microsoft)
- [ ] إعادة تعيين كلمة المرور عبر البريد الإلكتروني
- [ ] جلسات متعددة الأجهزة
- [ ] تحليلات الأمان المتقدمة

### ملاحظات مهمة:
- كلمة المرور الافتراضية للمستخدمين الموجودين: `123456`
- يجب تغيير كلمة المرور عند أول تسجيل دخول
- تأكد من تحديث Secret Key في البيئة الإنتاجية
- استخدم HTTPS في البيئة الإنتاجية
