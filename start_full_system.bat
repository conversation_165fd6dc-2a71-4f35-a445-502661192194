@echo off
echo ========================================
echo    تشغيل النظام الكامل
echo    ASP.NET Core API + Flutter App
echo ========================================
echo.

echo بدء تشغيل خادم ASP.NET Core API...
start "ASP.NET Core API" cmd /k "cd /d \"%~dp0webApi\webApi\" && dotnet run --urls \"http://localhost:5175;https://localhost:7111\""

echo انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo بدء تشغيل تطبيق Flutter...
start "Flutter App" cmd /k "flutter run -d chrome --web-port 8080"

echo.
echo ========================================
echo تم تشغيل النظام بنجاح!
echo.
echo خادم ASP.NET Core API:
echo   - HTTP: http://localhost:5175
echo   - HTTPS: https://localhost:7111
echo   - Swagger: http://localhost:5175/swagger
echo.
echo تطبيق Flutter:
echo   - Web: http://localhost:8080
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
echo ========================================
pause > nul
