import 'user_model.dart';

/// نموذج لوحة التحكم
class Dashboard {
  final int id;
  final String name;
  final String? description;
  final String? layout; // JSON string for layout configuration
  final bool isDefault;
  final bool isPublic;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final User? createdByUser;
  final List<DashboardWidget>? widgets;
  final List<User>? sharedWithUsers;

  const Dashboard({
    required this.id,
    required this.name,
    this.description,
    this.layout,
    this.isDefault = false,
    this.isPublic = false,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.createdByUser,
    this.widgets,
    this.sharedWithUsers,
  });

  factory Dashboard.fromJson(Map<String, dynamic> json) {
    return Dashboard(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      layout: json['layout'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      isPublic: json['isPublic'] as bool? ?? false,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      widgets: json['dashboardWidgets'] != null 
          ? (json['dashboardWidgets'] as List)
              .map((w) => DashboardWidget.fromJson(w as Map<String, dynamic>))
              .toList()
          : null,
      sharedWithUsers: json['users'] != null 
          ? (json['users'] as List)
              .map((u) => User.fromJson(u as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'layout': layout,
      'isDefault': isDefault,
      'isPublic': isPublic,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }

  Dashboard copyWith({
    int? id,
    String? name,
    String? description,
    String? layout,
    bool? isDefault,
    bool? isPublic,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    User? createdByUser,
    List<DashboardWidget>? widgets,
    List<User>? sharedWithUsers,
  }) {
    return Dashboard(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      layout: layout ?? this.layout,
      isDefault: isDefault ?? this.isDefault,
      isPublic: isPublic ?? this.isPublic,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      createdByUser: createdByUser ?? this.createdByUser,
      widgets: widgets ?? this.widgets,
      sharedWithUsers: sharedWithUsers ?? this.sharedWithUsers,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على عدد الويدجت
  int get widgetCount => widgets?.length ?? 0;

  @override
  String toString() {
    return 'Dashboard(id: $id, name: $name, widgetCount: $widgetCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dashboard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج ويدجت لوحة التحكم
class DashboardWidget {
  final int id;
  final int dashboardId;
  final String type; // chart, table, metric, etc.
  final String title;
  final String? config; // JSON configuration
  final int positionX;
  final int positionY;
  final int width;
  final int height;
  final bool isVisible;
  final int createdAt;
  final int? updatedAt;

  // Navigation properties
  final Dashboard? dashboard;

  const DashboardWidget({
    required this.id,
    required this.dashboardId,
    required this.type,
    required this.title,
    this.config,
    required this.positionX,
    required this.positionY,
    required this.width,
    required this.height,
    this.isVisible = true,
    required this.createdAt,
    this.updatedAt,
    this.dashboard,
  });

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
      id: json['id'] as int,
      dashboardId: json['dashboardId'] as int,
      type: json['type'] as String,
      title: json['title'] as String,
      config: json['config'] as String?,
      positionX: json['positionX'] as int,
      positionY: json['positionY'] as int,
      width: json['width'] as int,
      height: json['height'] as int,
      isVisible: json['isVisible'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      dashboard: json['dashboard'] != null 
          ? Dashboard.fromJson(json['dashboard'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'type': type,
      'title': title,
      'config': config,
      'positionX': positionX,
      'positionY': positionY,
      'width': width,
      'height': height,
      'isVisible': isVisible,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  DashboardWidget copyWith({
    int? id,
    int? dashboardId,
    String? type,
    String? title,
    String? config,
    int? positionX,
    int? positionY,
    int? width,
    int? height,
    bool? isVisible,
    int? createdAt,
    int? updatedAt,
    Dashboard? dashboard,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      dashboardId: dashboardId ?? this.dashboardId,
      type: type ?? this.type,
      title: title ?? this.title,
      config: config ?? this.config,
      positionX: positionX ?? this.positionX,
      positionY: positionY ?? this.positionY,
      width: width ?? this.width,
      height: height ?? this.height,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dashboard: dashboard ?? this.dashboard,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'DashboardWidget(id: $id, type: $type, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardWidget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء لوحة تحكم
class CreateDashboardRequest {
  final String name;
  final String? description;
  final bool isPublic;
  final List<int>? sharedWithUserIds;

  const CreateDashboardRequest({
    required this.name,
    this.description,
    this.isPublic = false,
    this.sharedWithUserIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'isPublic': isPublic,
      'sharedWithUserIds': sharedWithUserIds,
    };
  }
}

/// نموذج طلب إنشاء ويدجت
class CreateDashboardWidgetRequest {
  final int dashboardId;
  final String type;
  final String title;
  final String? config;
  final int positionX;
  final int positionY;
  final int width;
  final int height;

  const CreateDashboardWidgetRequest({
    required this.dashboardId,
    required this.type,
    required this.title,
    this.config,
    required this.positionX,
    required this.positionY,
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() {
    return {
      'dashboardId': dashboardId,
      'type': type,
      'title': title,
      'config': config,
      'positionX': positionX,
      'positionY': positionY,
      'width': width,
      'height': height,
    };
  }
}
