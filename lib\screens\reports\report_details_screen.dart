import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/report_model.dart';
import '../../models/report_result_model.dart';
import '../../models/permission_model.dart';
import '../../database/permission_repository.dart';
import '../../services/report_service.dart';
import '../../services/report_export_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/responsive_helper.dart';

/// شاشة تفاصيل التقرير
///
/// تعرض تفاصيل التقرير ونتائجه
class ReportDetailsScreen extends StatefulWidget {
  final String reportId;

  const ReportDetailsScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ReportDetailsScreen> createState() => _ReportDetailsScreenState();
}

class _ReportDetailsScreenState extends State<ReportDetailsScreen> {
  final ReportRepository _reportRepository = ReportRepository();
  final ReportService _reportService = ReportService();
  final ReportExportService _reportExportService = ReportExportService();
  final AuthController _authController = Get.find<AuthController>();
  final PermissionRepository _permissionRepository = PermissionRepository();

  Report? _report;
  ReportResult? _reportResult;
  bool _isLoading = true;
  String? _errorMessage;
  bool _hasExportPermission = false;

  @override
  void initState() {
    super.initState();
    _loadReport();
    _checkPermissions();
  }

  /// التحقق من صلاحيات المستخدم
  Future<void> _checkPermissions() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser != null) {
      // التحقق من صلاحية التصدير
      final hasExport = await _permissionRepository.hasPermission(
        currentUser.id,
        PermissionType.export,
        PermissionScope.reports
      );

      if (mounted) {
        setState(() {
          _hasExportPermission = hasExport;
        });
      }
    }
  }

  /// تحميل التقرير
  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // الحصول على التقرير
      final report = await _reportRepository.getReportById(widget.reportId);
      if (report == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'التقرير غير موجود';
        });
        return;
      }

      debugPrint('تم تحميل التقرير: ${report.title} (${report.id})');
      debugPrint('نوع التقرير: ${report.type}');
      debugPrint('مشترك: ${report.isShared}');
      debugPrint('مشترك مع: ${report.sharedWithUserIds}');

      // التحقق من صلاحية الوصول للتقرير
      final currentUserId = _authController.currentUser.value?.id;
      final isCreator = report.createdById == currentUserId;
      final isSharedWithUser = report.isShared &&
          report.sharedWithUserIds != null &&
          report.sharedWithUserIds!.contains(currentUserId);

      debugPrint('معرف المستخدم الحالي: $currentUserId');
      debugPrint('هل المستخدم هو منشئ التقرير؟ $isCreator');
      debugPrint('هل التقرير مشترك مع المستخدم؟ $isSharedWithUser');

      if (!isCreator && !isSharedWithUser) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ليس لديك صلاحية للوصول إلى هذا التقرير';
        });
        return;
      }

      // تنفيذ التقرير
      debugPrint('بدء تنفيذ التقرير...');
      final result = await _reportService.executeReport(widget.reportId);
      debugPrint('اكتمل تنفيذ التقرير بنجاح: ${result.isSuccess}');

      if (!result.isSuccess) {
        debugPrint('فشل تنفيذ التقرير: ${result.errorMessages?.join(', ')}');
        setState(() {
          _isLoading = false;
          _errorMessage = 'فشل تنفيذ التقرير: ${result.errorMessages?.join(', ')}';
        });
        return;
      }

      setState(() {
        _report = report;
        _reportResult = result;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل التقرير: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل التقرير: $e';
      });
    }
  }

  /// عرض مربع حوار تصدير التقرير
  void _showExportReportDialog() {
    if (_report == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('PDF'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(ReportFormat.pdf);
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Excel'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(ReportFormat.excel);
              },
            ),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('CSV'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(ReportFormat.csv);
              },
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('JSON'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(ReportFormat.json);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تصدير التقرير
  Future<void> _exportReport(ReportFormat format) async {
    // التحقق من صلاحيات التصدير
    if (!_hasExportPermission) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('ليس لديك صلاحية لتصدير التقارير'),
          backgroundColor: Colors.red.shade400,
        ),
      );
      return;
    }

    if (_report == null) return;

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تصدير التقرير...'),
          ],
        ),
      ),
    );

    try {
      String? filePath;

      switch (format) {
        case ReportFormat.pdf:
          filePath = await _reportExportService.exportReportToPdf(_report!.id);
          break;
        case ReportFormat.excel:
          filePath = await _reportExportService.exportReportToExcel(_report!.id);
          break;
        case ReportFormat.csv:
          filePath = await _reportExportService.exportReportToCsv(_report!.id);
          break;
        case ReportFormat.json:
          filePath = await _reportExportService.exportReportToJson(_report!.id);
          break;
        default:
          filePath = null;
      }

      // التحقق من أن الـ widget لا يزال موجودًا في شجرة العناصر
      if (!mounted) {
        return;
      }

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      if (filePath != null) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
            duration: const Duration(seconds: 5),
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('فشل تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // التحقق من أن الـ widget لا يزال موجودًا في شجرة العناصر
      if (!mounted) {
        return;
      }

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'تقرير حالة المهام';
      case ReportType.userPerformance:
        return 'تقرير أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'تقرير أداء القسم';
      case ReportType.timeTracking:
        return 'تقرير تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقرير تقدم المهام';
      case ReportType.taskDetails:
        return 'تقرير تفاصيل المهمة';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'تقرير غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_report?.title ?? 'تفاصيل التقرير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadReport,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير',
            onPressed: _showExportReportDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _report == null || _reportResult == null
                  ? const Center(child: Text('لا توجد بيانات للتقرير'))
                  : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader(),
          const SizedBox(height: 24),
          if (_reportResult!.summary != null) ...[
            _buildSummarySection(),
            const SizedBox(height: 24),
          ],
          _buildReportDataSection(),
        ],
      ),
    );
  }

  Widget _buildReportHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _report!.title,
                    style: AppStyles.titleLarge,
                  ),
                ),
                Chip(
                  label: Text(
                    _getReportTypeName(_report!.type),
                    style: const TextStyle(color: Colors.white),
                  ),
                  backgroundColor: _getReportTypeColor(_report!.type),
                ),
              ],
            ),
            if (_report!.description != null) ...[
              const SizedBox(height: 8),
              Text(
                _report!.description!,
                style: AppStyles.bodyMedium,
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text(
                  'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(_report!.createdAt)}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.update, size: 16),
                const SizedBox(width: 8),
                Text(
                  'تاريخ التنفيذ: ${DateFormat('yyyy-MM-dd HH:mm').format(_reportResult!.generatedAt)}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التقرير',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            _buildSummaryContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryContent() {
    if (_reportResult?.summary == null) {
      return const Text('لا يوجد ملخص للتقرير');
    }

    final summary = _reportResult!.summary!;

    // عرض الملخص حسب نوع التقرير
    switch (_report!.type) {
      case ReportType.taskStatus:
        return _buildTaskStatusSummary(summary);
      case ReportType.userPerformance:
        return _buildUserPerformanceSummary(summary);
      case ReportType.departmentPerformance:
        return _buildDepartmentPerformanceSummary(summary);
      case ReportType.timeTracking:
        return _buildTimeTrackingSummary(summary);
      case ReportType.taskProgress:
        return _buildTaskProgressSummary(summary);
      case ReportType.taskDetails:
        return _buildTaskDetailsSummary(summary);
      case ReportType.custom:
        return _buildCustomSummary(summary);
      default:
        return const Text('نوع تقرير غير مدعوم');
    }
  }

  Widget _buildTaskDetailsSummary(Map<String, dynamic> summary) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'عنوان المهمة',
            'value': summary['taskTitle'] ?? 'غير معروف',
            'icon': Icons.assignment,
            'color': Colors.indigo,
          },
          {
            'title': 'الحالة',
            'value': summary['taskStatus'] ?? 'غير معروف',
            'icon': Icons.info,
            'color': Colors.blue,
          },
          {
            'title': 'نسبة الإكمال',
            'value': '${summary['completionPercentage'] ?? 0}%',
            'icon': Icons.percent,
            'color': Colors.green,
          },
          {
            'title': 'عدد سجلات الوقت',
            'value': summary['timeEntriesCount']?.toString() ?? '0',
            'icon': Icons.timer,
            'color': Colors.orange,
          },
        ]),

        const SizedBox(height: 24),

        // معلومات إضافية عن المهمة
        Text(
          'معلومات المهمة',
          style: AppStyles.titleSmall,
        ),
        const SizedBox(height: 8),

        // تفاصيل المهمة في جدول
        Table(
          border: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          columnWidths: const {
            0: FlexColumnWidth(1),
            1: FlexColumnWidth(2),
          },
          children: [
            _buildTableRow('المنشئ', summary['creatorName'] ?? 'غير معروف'),
            _buildTableRow('المسؤول', summary['assigneeName'] ?? 'غير معروف'),
            _buildTableRow('القسم', summary['departmentName'] ?? 'غير معروف'),
            _buildTableRow('تاريخ الإنشاء', summary['createdAt'] ?? 'غير معروف'),
            _buildTableRow('تاريخ الاستحقاق', summary['dueDate'] ?? 'غير معروف'),
            _buildTableRow('الأولوية', summary['priority'] ?? 'غير معروف'),
            _buildTableRow('الوصف', summary['description'] ?? 'لا يوجد وصف'),
          ],
        ),

        // إحصائيات الوقت
        if (summary.containsKey('totalTimeSpent')) ...[
          const SizedBox(height: 24),
          Text(
            'إحصائيات الوقت',
            style: AppStyles.titleSmall,
          ),
          const SizedBox(height: 8),
          _buildSummaryGrid([
            {
              'title': 'إجمالي الوقت (دقائق)',
              'value': summary['totalTimeSpent']?.toString() ?? '0',
              'icon': Icons.timer,
              'color': Colors.purple,
            },
            {
              'title': 'إجمالي الوقت (ساعات)',
              'value': (summary['totalTimeSpent'] != null
                ? (summary['totalTimeSpent'] / 60).toStringAsFixed(1)
                : '0'),
              'icon': Icons.access_time,
              'color': Colors.teal,
            },
            {
              'title': 'عدد المساهمين',
              'value': summary['contributorsCount']?.toString() ?? '0',
              'icon': Icons.people,
              'color': Colors.blue,
            },
          ]),
        ],
      ],
    );
  }

  TableRow _buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(value),
        ),
      ],
    );
  }

  Widget _buildTaskStatusSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.blue,
          },
          {
            'title': 'المهام المكتملة',
            'value': summary['completedTasks'].toString(),
            'icon': Icons.check_circle,
            'color': Colors.green,
          },
          {
            'title': 'المهام قيد التنفيذ',
            'value': summary['inProgressTasks'].toString(),
            'icon': Icons.pending_actions,
            'color': Colors.orange,
          },
          {
            'title': 'المهام المتأخرة',
            'value': summary['overdueTasks'].toString(),
            'icon': Icons.warning,
            'color': Colors.red,
          },
        ]),
        const SizedBox(height: 24),

        // عرض مخطط توزيع الحالات
        if (_reportResult!.data.containsKey('statusDistribution')) ...[
          Text(
            'توزيع المهام حسب الحالة',
            style: AppStyles.titleSmall,
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: _buildStatusPieChart(_reportResult!.data['statusDistribution']),
          ),
        ],
      ],
    );
  }

  Widget _buildUserPerformanceSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي المستخدمين',
            'value': summary['totalUsers'].toString(),
            'icon': Icons.people,
            'color': Colors.blue,
          },
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.green,
          },
          {
            'title': 'المهام المكتملة',
            'value': summary['totalCompletedTasks'].toString(),
            'icon': Icons.check_circle,
            'color': Colors.teal,
          },
          {
            'title': 'معدل الإكمال',
            'value': '${summary['completionRate'].toStringAsFixed(1)}%',
            'icon': Icons.percent,
            'color': Colors.orange,
          },
        ]),
        const SizedBox(height: 16),

        // عرض إحصائيات الوقت
        _buildSummaryGrid([
          {
            'title': 'إجمالي الوقت (دقائق)',
            'value': summary['totalTimeSpent'].toStringAsFixed(0),
            'icon': Icons.timer,
            'color': Colors.purple,
          },
          {
            'title': 'متوسط الوقت لكل مستخدم',
            'value': summary['averageTimePerUser'].toStringAsFixed(1),
            'icon': Icons.person_outline,
            'color': Colors.indigo,
          },
        ]),
      ],
    );
  }

  Widget _buildDepartmentPerformanceSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي الأقسام',
            'value': summary['totalDepartments'].toString(),
            'icon': Icons.business,
            'color': Colors.blue,
          },
          {
            'title': 'إجمالي المستخدمين',
            'value': summary['totalUsers'].toString(),
            'icon': Icons.people,
            'color': Colors.green,
          },
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.orange,
          },
          {
            'title': 'المهام المكتملة',
            'value': summary['totalCompletedTasks'].toString(),
            'icon': Icons.check_circle,
            'color': Colors.teal,
          },
        ]),
        const SizedBox(height: 16),

        // عرض إحصائيات إضافية
        _buildSummaryGrid([
          {
            'title': 'معدل الإكمال',
            'value': '${summary['completionRate'].toStringAsFixed(1)}%',
            'icon': Icons.percent,
            'color': Colors.purple,
          },
          {
            'title': 'متوسط المهام لكل قسم',
            'value': summary['averageTasksPerDepartment'].toStringAsFixed(1),
            'icon': Icons.business_center,
            'color': Colors.indigo,
          },
        ]),
      ],
    );
  }

  Widget _buildTimeTrackingSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي السجلات',
            'value': summary['totalEntries'].toString(),
            'icon': Icons.list_alt,
            'color': Colors.blue,
          },
          {
            'title': 'إجمالي الدقائق',
            'value': summary['totalMinutes'].toStringAsFixed(0),
            'icon': Icons.timer,
            'color': Colors.green,
          },
          {
            'title': 'إجمالي الساعات',
            'value': summary['totalHours'].toStringAsFixed(1),
            'icon': Icons.access_time,
            'color': Colors.orange,
          },
          {
            'title': 'إجمالي المستخدمين',
            'value': summary['totalUsers'].toString(),
            'icon': Icons.people,
            'color': Colors.purple,
          },
        ]),
        const SizedBox(height: 16),

        // عرض إحصائيات إضافية
        _buildSummaryGrid([
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.teal,
          },
          {
            'title': 'متوسط الدقائق لكل سجل',
            'value': summary['averageMinutesPerEntry'].toStringAsFixed(1),
            'icon': Icons.av_timer,
            'color': Colors.indigo,
          },
        ]),
      ],
    );
  }

  Widget _buildTaskProgressSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.blue,
          },
          {
            'title': 'المهام المكتملة',
            'value': summary['completedTasks'].toString(),
            'icon': Icons.check_circle,
            'color': Colors.green,
          },
          {
            'title': 'المهام قيد التنفيذ',
            'value': summary['inProgressTasks'].toString(),
            'icon': Icons.pending_actions,
            'color': Colors.orange,
          },
          {
            'title': 'متوسط نسبة الإكمال',
            'value': '${summary['averageCompletionPercentage'].toStringAsFixed(1)}%',
            'icon': Icons.percent,
            'color': Colors.purple,
          },
        ]),
        const SizedBox(height: 16),

        // عرض إحصائيات إضافية
        _buildSummaryGrid([
          {
            'title': 'معدل الإكمال',
            'value': '${summary['completionRate'].toStringAsFixed(1)}%',
            'icon': Icons.trending_up,
            'color': Colors.teal,
          },
        ]),
      ],
    );
  }

  Widget _buildCustomSummary(Map<String, dynamic> summary) {
    return Column(
      children: [
        // عرض الإحصائيات الرئيسية
        _buildSummaryGrid([
          {
            'title': 'إجمالي المهام',
            'value': summary['totalTasks'].toString(),
            'icon': Icons.assignment,
            'color': Colors.blue,
          },
          {
            'title': 'إجمالي المستخدمين',
            'value': summary['totalUsers'].toString(),
            'icon': Icons.people,
            'color': Colors.green,
          },
          {
            'title': 'إجمالي الأقسام',
            'value': summary['totalDepartments'].toString(),
            'icon': Icons.business,
            'color': Colors.purple,
          },
        ]),
      ],
    );
  }

  Widget _buildSummaryGrid(List<Map<String, dynamic>> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: ResponsiveHelper.isDesktop(context) ? 4 : (ResponsiveHelper.isTablet(context) ? 3 : 2),
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildSummaryItem(
          title: item['title'],
          value: item['value'],
          icon: item['icon'],
          color: item['color'],
        );
      },
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // 0.1 * 255 = ~25
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(76)), // 0.3 * 255 = ~76
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withAlpha(204), // 0.8 * 255 = ~204
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildReportDataSection() {
    // عرض البيانات حسب نوع التقرير
    switch (_report!.type) {
      case ReportType.taskStatus:
        return _buildTaskStatusData();
      case ReportType.userPerformance:
        return _buildUserPerformanceData();
      case ReportType.departmentPerformance:
        return _buildDepartmentPerformanceData();
      case ReportType.timeTracking:
        return _buildTimeTrackingData();
      case ReportType.taskProgress:
        return _buildTaskProgressData();
      case ReportType.taskDetails:
        return _buildTaskDetailsData();
      case ReportType.custom:
        return _buildCustomData();
      default:
        return const Center(child: Text('نوع تقرير غير مدعوم'));
    }
  }

  Widget _buildTaskDetailsData() {
    final data = _reportResult!.data;

    if (!data.containsKey('task')) {
      return const Center(child: Text('لا توجد بيانات تفاصيل المهمة'));
    }

    final task = data['task'] as Map<String, dynamic>;
    final timeEntries = data['timeEntries'] as List<dynamic>? ?? [];
    final comments = data['comments'] as List<dynamic>? ?? [];
    final attachments = data['attachments'] as List<dynamic>? ?? [];
    final subtasks = data['subtasks'] as List<dynamic>? ?? [];
    final statusHistory = data['statusHistory'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // بطاقة تفاصيل المهمة
        Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفاصيل المهمة',
                  style: AppStyles.titleMedium,
                ),
                const Divider(),
                const SizedBox(height: 8),

                // عرض تفاصيل المهمة
                _buildTaskDetailsTable(task),
              ],
            ),
          ),
        ),

        // بطاقة المهام الفرعية
        if (subtasks.isNotEmpty) ...[
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المهام الفرعية (${subtasks.length})',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 8),

                  // عرض المهام الفرعية
                  _buildSubtasksTable(subtasks),
                ],
              ),
            ),
          ),
        ],

        // بطاقة سجلات الوقت
        if (timeEntries.isNotEmpty) ...[
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سجلات الوقت (${timeEntries.length})',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 8),

                  // عرض سجلات الوقت
                  _buildTimeEntriesTable(timeEntries),
                ],
              ),
            ),
          ),
        ],

        // بطاقة التعليقات
        if (comments.isNotEmpty) ...[
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التعليقات (${comments.length})',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 8),

                  // عرض التعليقات
                  _buildCommentsSection(comments),
                ],
              ),
            ),
          ),
        ],

        // بطاقة المرفقات
        if (attachments.isNotEmpty) ...[
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المرفقات (${attachments.length})',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 8),

                  // عرض المرفقات
                  _buildAttachmentsTable(attachments),
                ],
              ),
            ),
          ),
        ],

        // بطاقة تاريخ الحالة
        if (statusHistory.isNotEmpty) ...[
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ الحالة',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 8),

                  // عرض تاريخ الحالة
                  _buildStatusHistoryTable(statusHistory),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTaskDetailsTable(Map<String, dynamic> task) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الخاصية')),
          DataColumn(label: Text('القيمة')),
        ],
        rows: [
          DataRow(
            cells: [
              const DataCell(Text('العنوان')),
              DataCell(Text(task['title'] ?? 'غير معروف')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('الوصف')),
              DataCell(Text(task['description'] ?? '-')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('الحالة')),
              DataCell(Text(task['status'] ?? 'غير معروف')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('الأولوية')),
              DataCell(Text(task['priority'] ?? 'غير معروف')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('نسبة الإكمال')),
              DataCell(Text('${task['completionPercentage'] ?? 0}%')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('تاريخ الإنشاء')),
              DataCell(Text(task['createdAt'] != null
                ? DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(task['createdAt']))
                : '-')),
            ],
          ),
          DataRow(
            cells: [
              const DataCell(Text('تاريخ الاستحقاق')),
              DataCell(Text(task['dueDate'] != null
                ? DateFormat('yyyy-MM-dd').format(DateTime.parse(task['dueDate']))
                : '-')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaskStatusData() {
    final data = _reportResult!.data;
    final tasks = data['tasks'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'قائمة المهام',
                  style: AppStyles.titleMedium,
                ),
                const Divider(),
                const SizedBox(height: 16),
                _buildTasksTable(tasks),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTasksTable(List<dynamic> tasks) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('العنوان')),
          DataColumn(label: Text('الحالة')),
          DataColumn(label: Text('الأولوية')),
          DataColumn(label: Text('نسبة الإكمال')),
          DataColumn(label: Text('تاريخ الاستحقاق')),
          DataColumn(label: Text('متأخرة')),
        ],
        rows: tasks.map<DataRow>((task) {
          return DataRow(
            cells: [
              DataCell(Text(task['title'])),
              DataCell(Text(task['status'])),
              DataCell(Text(task['priority'])),
              DataCell(Text('${task['completionPercentage']}%')),
              DataCell(Text(task['dueDate'] != null ? DateFormat('yyyy-MM-dd').format(DateTime.parse(task['dueDate'])) : '-')),
              DataCell(Text(task['isOverdue'] ? 'نعم' : 'لا')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUserPerformanceData() {
    final data = _reportResult!.data;

    if (!data.containsKey('users') || (data['users'] as List).isEmpty) {
      return const Center(child: Text('لا توجد بيانات أداء للمستخدمين'));
    }

    final users = data['users'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أداء المستخدمين',
                  style: AppStyles.titleMedium,
                ),
                const Divider(),
                const SizedBox(height: 16),
                _buildUsersPerformanceTable(users),
              ],
            ),
          ),
        ),

        if (data.containsKey('userTaskDistribution')) ...[
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع المهام حسب المستخدم',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 16),
                  _buildUserTaskDistributionTable(data['userTaskDistribution']),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUsersPerformanceTable(List<dynamic> users) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('المهام المسندة')),
          DataColumn(label: Text('المهام المكتملة')),
          DataColumn(label: Text('نسبة الإكمال')),
          DataColumn(label: Text('الوقت المستغرق (دقائق)')),
          DataColumn(label: Text('متوسط الوقت لكل مهمة')),
        ],
        rows: users.map<DataRow>((user) {
          return DataRow(
            cells: [
              DataCell(Text(user['userName'] ?? 'غير معروف')),
              DataCell(Text(user['totalAssignedTasks'].toString())),
              DataCell(Text(user['completedTasks'].toString())),
              DataCell(Text('${user['completionRate'].toStringAsFixed(1)}%')),
              DataCell(Text(user['totalTimeSpent'].toStringAsFixed(0))),
              DataCell(Text(user['averageTimePerTask'].toStringAsFixed(1))),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUserTaskDistributionTable(dynamic distribution) {
    if (distribution is! Map) {
      return const Center(child: Text('بيانات غير صالحة'));
    }

    final entries = distribution.entries.toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('عدد المهام')),
          DataColumn(label: Text('نسبة المهام')),
        ],
        rows: entries.map<DataRow>((entry) {
          final userData = entry.value;
          return DataRow(
            cells: [
              DataCell(Text(userData['userName'] ?? 'غير معروف')),
              DataCell(Text(userData['taskCount'].toString())),
              DataCell(Text('${userData['percentage'].toStringAsFixed(1)}%')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDepartmentPerformanceData() {
    // تنفيذ الدالة
    return const Center(child: Text('بيانات أداء القسم'));
  }

  Widget _buildTimeTrackingData() {
    final data = _reportResult!.data;

    if (!data.containsKey('timeEntries') || (data['timeEntries'] as List).isEmpty) {
      return const Center(child: Text('لا توجد بيانات تتبع الوقت'));
    }

    final timeEntries = data['timeEntries'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'سجلات تتبع الوقت',
                  style: AppStyles.titleMedium,
                ),
                const Divider(),
                const SizedBox(height: 16),
                _buildTimeEntriesTable(timeEntries),
              ],
            ),
          ),
        ),

        if (data.containsKey('userTaskTimeDistribution')) ...[
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع الوقت حسب المستخدم والمهمة',
                    style: AppStyles.titleMedium,
                  ),
                  const Divider(),
                  const SizedBox(height: 16),
                  _buildUserTaskTimeDistributionTable(data['userTaskTimeDistribution']),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeEntriesTable(List<dynamic> timeEntries) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('المهمة')),
          DataColumn(label: Text('وقت البدء')),
          DataColumn(label: Text('وقت الانتهاء')),
          DataColumn(label: Text('المدة (دقائق)')),
          DataColumn(label: Text('الوصف')),
        ],
        rows: timeEntries.map<DataRow>((entry) {
          return DataRow(
            cells: [
              DataCell(Text(entry['userName'] ?? 'غير معروف')),
              DataCell(Text(entry['taskTitle'] ?? 'غير معروف')),
              DataCell(Text(entry['startTime'] != null
                ? DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(entry['startTime']))
                : '-')),
              DataCell(Text(entry['endTime'] != null
                ? DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(entry['endTime']))
                : '-')),
              DataCell(Text(entry['durationMinutes']?.toString() ?? '0')),
              DataCell(Text(entry['description'] ?? '-')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUserTaskTimeDistributionTable(dynamic distribution) {
    if (distribution is! Map) {
      return const Center(child: Text('بيانات غير صالحة'));
    }

    final entries = distribution.entries.toList();
    final List<Map<String, dynamic>> flattenedData = [];

    for (var userEntry in entries) {
      final userData = userEntry.value;
      final userName = userData['userName'] ?? 'غير معروف';

      if (userData['tasks'] is Map) {
        final tasks = userData['tasks'] as Map;
        for (var taskEntry in tasks.entries) {
          final taskData = taskEntry.value;
          flattenedData.add({
            'userName': userName,
            'taskTitle': taskData['taskTitle'] ?? 'غير معروف',
            'totalMinutes': taskData['totalMinutes'] ?? 0,
            'totalHours': taskData['totalHours'] ?? 0,
          });
        }
      }
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('المهمة')),
          DataColumn(label: Text('الوقت (دقائق)')),
          DataColumn(label: Text('الوقت (ساعات)')),
        ],
        rows: flattenedData.map<DataRow>((data) {
          return DataRow(
            cells: [
              DataCell(Text(data['userName'])),
              DataCell(Text(data['taskTitle'])),
              DataCell(Text(data['totalMinutes'].toString())),
              DataCell(Text(data['totalHours'].toStringAsFixed(1))),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTaskProgressData() {
    // تنفيذ الدالة
    return const Center(child: Text('بيانات تقدم المهام'));
  }

  Widget _buildCustomData() {
    // تنفيذ الدالة
    return const Center(child: Text('بيانات التقرير المخصص'));
  }

  Widget _buildStatusPieChart(Map<String, dynamic> statusDistribution) {
    final List<PieChartSectionData> sections = [];
    final List<MapEntry<String, dynamic>> entries = statusDistribution.entries.toList();

    // تعريف الألوان لكل حالة
    final Map<String, Color> statusColors = {
      'pending': AppColors.statusPending,
      'inProgress': AppColors.statusInProgress,
      'completed': AppColors.statusCompleted,
      'waitingForInfo': AppColors.statusWaitingForInfo,
      'cancelled': Colors.grey,
    };

    // إنشاء أقسام المخطط
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final color = statusColors[entry.key] ?? Colors.grey;

      sections.add(
        PieChartSectionData(
          color: color,
          value: entry.value.toDouble(),
          title: '${entry.key}\n${entry.value}',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildSubtasksTable(List<dynamic> subtasks) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('العنوان')),
          DataColumn(label: Text('الحالة')),
          DataColumn(label: Text('المسؤول')),
          DataColumn(label: Text('نسبة الإكمال')),
          DataColumn(label: Text('تاريخ الاستحقاق')),
        ],
        rows: subtasks.map<DataRow>((subtask) {
          return DataRow(
            cells: [
              DataCell(Text(subtask['title'] ?? 'غير معروف')),
              DataCell(Text(subtask['status'] ?? 'غير معروف')),
              DataCell(Text(subtask['assigneeName'] ?? 'غير معروف')),
              DataCell(Text('${subtask['completionPercentage'] ?? 0}%')),
              DataCell(Text(subtask['dueDate'] ?? 'غير معروف')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCommentsSection(List<dynamic> comments) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: comments.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final comment = comments[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: AppColors.primary,
            child: Text(
              (comment['userName'] as String?)?.substring(0, 1).toUpperCase() ?? 'U',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          title: Text(comment['userName'] ?? 'غير معروف'),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(comment['content'] ?? ''),
              const SizedBox(height: 4),
              Text(
                comment['createdAt'] ?? 'غير معروف',
                style: AppStyles.captionSmall,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAttachmentsTable(List<dynamic> attachments) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('اسم الملف')),
          DataColumn(label: Text('النوع')),
          DataColumn(label: Text('الحجم')),
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('تاريخ الرفع')),
        ],
        rows: attachments.map<DataRow>((attachment) {
          return DataRow(
            cells: [
              DataCell(Text(attachment['fileName'] ?? 'غير معروف')),
              DataCell(Text(attachment['fileType'] ?? 'غير معروف')),
              DataCell(Text(attachment['fileSize'] ?? 'غير معروف')),
              DataCell(Text(attachment['userName'] ?? 'غير معروف')),
              DataCell(Text(attachment['createdAt'] ?? 'غير معروف')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStatusHistoryTable(List<dynamic> statusHistory) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الحالة')),
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('التاريخ')),
          DataColumn(label: Text('ملاحظات')),
        ],
        rows: statusHistory.map<DataRow>((history) {
          return DataRow(
            cells: [
              DataCell(Text(history['status'] ?? 'غير معروف')),
              DataCell(Text(history['userName'] ?? 'غير معروف')),
              DataCell(Text(history['createdAt'] ?? 'غير معروف')),
              DataCell(Text(history['notes'] ?? '-')),
            ],
          );
        }).toList(),
      ),
    );
  }

  Color _getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Colors.blue;
      case ReportType.userPerformance:
        return Colors.green;
      case ReportType.departmentPerformance:
        return Colors.purple;
      case ReportType.timeTracking:
        return Colors.orange;
      case ReportType.taskProgress:
        return Colors.teal;
      case ReportType.taskDetails:
        return Colors.indigo;
      case ReportType.custom:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
