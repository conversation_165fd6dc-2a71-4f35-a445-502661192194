import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:super_editor/super_editor.dart';

import '../../controllers/text_document_controller.dart';
import '../../models/text_document_model.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';
import '../../services/export_services/enhanced_pdf_export_service.dart';

/// شاشة محرر المستندات النصية المتقدم
///
/// تستخدم لإنشاء وتحرير المستندات النصية بواجهة متقدمة تشبه برنامج Word
class AdvancedTextDocumentEditorScreen extends StatefulWidget {
  /// معرف المستند (للتحرير)
  final String? documentId;

  /// معرف المهمة (للإنشاء)
  final String? taskId;

  /// عنوان المستند الافتراضي (للإنشاء)
  final String? defaultTitle;

  /// نوع المستند الافتراضي (للإنشاء)
  final TextDocumentType? defaultType;

  /// إنشاء شاشة محرر المستندات النصية المتقدم
  const AdvancedTextDocumentEditorScreen({
    super.key,
    this.documentId,
    this.taskId,
    this.defaultTitle,
    this.defaultType,
  });

  @override
  State<AdvancedTextDocumentEditorScreen> createState() =>
      _AdvancedTextDocumentEditorScreenState();
}

class _AdvancedTextDocumentEditorScreenState
    extends State<AdvancedTextDocumentEditorScreen> {
  final TextDocumentController _documentController =
      Get.find<TextDocumentController>();
  final EnhancedPdfExportService _pdfExportService =
      Get.find<EnhancedPdfExportService>();

  final TextEditingController _titleController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _editorFocusNode = FocusNode();

  // متغيرات المحرر
  TextDocumentType _selectedType = TextDocumentType.note;
  bool _isShared = false;
  bool _isEditing = false;
  bool _isNew = true;
  bool _isLoading = true;

  // متغيرات المحرر المتقدم - سيتم استخدامها في المستقبل
  // تعريف التنسيقات - سيتم استخدامها في المستقبل
  final boldAttribution = const NamedAttribution('bold');
  final italicAttribution = const NamedAttribution('italic');
  final underlineAttribution = const NamedAttribution('underline');

  @override
  void initState() {
    super.initState();
    // تهيئة أولية للمستند الجديد
    if (widget.documentId == null) {
      _isNew = true;
      _isEditing = true;

      // تعيين القيم الافتراضية
      if (widget.defaultTitle != null) {
        _titleController.text = widget.defaultTitle!;
      }

      if (widget.defaultType != null) {
        _selectedType = widget.defaultType!;
      }

      setState(() {
        _isLoading = false;
      });
    }

    // تأجيل تحميل المستند حتى اكتمال عملية البناء الأولى
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeDocument();
    });
  }

  @override
  void dispose() {
    // تحرير الموارد
    _titleController.dispose();
    _titleFocusNode.dispose();
    _editorFocusNode.dispose();

    // تحرير موارد المحرر
    if (_documentController.textController.value != null) {
      _documentController.textController.value!.dispose();
    }

    // تحرير موارد SuperEditor
    // في المستقبل، سيتم تحرير موارد SuperEditor هنا
    // _composer.dispose();

    super.dispose();
  }

  /// تهيئة المستند
  Future<void> _initializeDocument() async {
    if (widget.documentId != null) {
      setState(() {
        _isLoading = true;
        _isNew = false;
      });

      await _documentController.loadDocumentById(widget.documentId!);

      if (_documentController.currentDocument.value != null) {
        final document = _documentController.currentDocument.value!;

        // تهيئة محرر SuperEditor
        _initializeSuperEditor(document.getDocumentText());

        setState(() {
          _titleController.text = document.title;
          _selectedType = document.type;
          _isShared = document.isShared;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      // تهيئة محرر SuperEditor للمستند الجديد
      _initializeSuperEditor('');
    }
  }

  /// تهيئة محرر النصوص
  void _initializeSuperEditor(String content) {
    // في المستقبل، سيتم تهيئة SuperEditor هنا
    // final List<DocumentNode> nodes = [];
    //
    // if (content.isEmpty) {
    //   // إذا كان المحتوى فارغًا، أضف فقرة فارغة
    //   nodes.add(
    //     ParagraphNode(
    //       id: 'paragraph-${DateTime.now().millisecondsSinceEpoch}',
    //       text: AttributedText(),
    //     ),
    //   );
    // } else {
    //   // تقسيم المحتوى إلى فقرات
    //   final paragraphs = content.split('\n');
    //   for (final paragraph in paragraphs) {
    //     nodes.add(
    //       ParagraphNode(
    //         id: 'paragraph-${DateTime.now().millisecondsSinceEpoch}-${paragraphs.indexOf(paragraph)}',
    //         text: AttributedText(paragraph),
    //       ),
    //     );
    //   }
    // }
    //
    // // إنشاء وثيقة جديدة
    // _document = MutableDocument(nodes: nodes);
    //
    // // إنشاء محرر الوثيقة
    // _composer = MutableDocumentComposer();

    // تهيئة المتحكم النصي للتوافق مع الكود الحالي
    _documentController.setTextController(TextEditingController(text: content));
  }

  /// استخراج محتوى المستند من محرر النصوص
  String _getDocumentContent() {
    // استخدام النص من المتحكم القديم للتوافق
    final text = _documentController.textController?.text ?? '';

    // تحويل النص إلى JSON
    return TextDocument.textToJson(text);
  }

  /// تطبيق تنسيق التغميق على النص المحدد
  void _applyBoldFormat() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'تنسيق النص',
      'سيتم دعم تنسيق النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );

    // في المستقبل، سيتم تنفيذ هذه الوظيفة كالتالي:
    // final selection = _composer.selection;
    // if (selection != null && !selection.isCollapsed) {
    //   _document.update([
    //     AddAttributionToTextOperation(
    //       documentRange: selection,
    //       attribution: boldAttribution,
    //     ),
    //   ]);
    // }
  }

  /// تطبيق تنسيق المائل على النص المحدد
  void _applyItalicFormat() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'تنسيق النص',
      'سيتم دعم تنسيق النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق تنسيق التسطير على النص المحدد
  void _applyUnderlineFormat() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'تنسيق النص',
      'سيتم دعم تنسيق النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق لون على النص المحدد
  void _applyTextColor() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'تنسيق النص',
      'سيتم دعم تنسيق النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق محاذاة لليمين
  void _applyRightAlignment() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'محاذاة النص',
      'سيتم دعم محاذاة النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق محاذاة للوسط
  void _applyCenterAlignment() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'محاذاة النص',
      'سيتم دعم محاذاة النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق محاذاة لليسار
  void _applyLeftAlignment() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'محاذاة النص',
      'سيتم دعم محاذاة النص في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق قائمة نقطية
  void _applyBulletList() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'قائمة نقطية',
      'سيتم دعم القوائم النقطية في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// تطبيق قائمة رقمية
  void _applyNumberedList() {
    // عرض رسالة للمستخدم
    Get.snackbar(
      'قائمة رقمية',
      'سيتم دعم القوائم الرقمية في الإصدار القادم',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// حفظ المستند
  Future<void> _saveDocument() async {
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان للمستند',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على محتوى المحرر من SuperEditor
      final documentContent = _getDocumentContent();

      if (_isNew) {
        // إنشاء مستند جديد
        await _documentController.createDocument(
          title: _titleController.text.trim(),
          type: _selectedType,
          taskId: widget.taskId,
          isShared: _isShared,
          content: documentContent,
        );
      } else {
        // تحديث مستند موجود
        await _documentController.updateDocument(
          id: widget.documentId!,
          title: _titleController.text.trim(),
          type: _selectedType,
          isShared: _isShared,
          content: documentContent,
        );
      }

      setState(() {
        _isEditing = false;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في حفظ المستند: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ المستند: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// طباعة المستند
  Future<void> _printDocument() async {
    try {
      // الحصول على نص المحرر
      final plainText = _documentController.textController?.text ?? '';

      // إنشاء ملف PDF
      final pdf = pw.Document();

      // تحديد نوع المستند
      String documentTypeText = '';
      switch (_selectedType) {
        case TextDocumentType.note:
          documentTypeText = 'ملاحظة';
          break;
        case TextDocumentType.report:
          documentTypeText = 'تقرير';
          break;
        // تم إزالة template - غير موجود في enum
        case TextDocumentType.letter:
          documentTypeText = 'خطاب';
          break;
        case TextDocumentType.memo:
          documentTypeText = 'مذكرة';
          break;
        case TextDocumentType.contract:
          documentTypeText = 'عقد';
          break;
        case TextDocumentType.other:
          documentTypeText = 'مستند آخر';
          break;
      }

      // إضافة صفحة
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // رأس المستند
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(width: 1, color: PdfColors.grey300),
                    ),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // العنوان
                          pw.Text(
                            _titleController.text,
                            style: pw.TextStyle(
                              fontSize: 18,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 5),
                          // نوع المستند
                          pw.Text(
                            documentTypeText,
                            style: const pw.TextStyle(
                              fontSize: 12,
                              color: PdfColors.grey700,
                            ),
                          ),
                        ],
                      ),
                      // التاريخ
                      pw.Text(
                        DateTime.now().toString().split(' ')[0],
                        style: const pw.TextStyle(
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // المحتوى
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    child: pw.Text(
                      plainText,
                      style: const pw.TextStyle(
                        fontSize: 12,
                        lineSpacing: 1.5,
                      ),
                    ),
                  ),
                ),

                // تذييل المستند
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      top: pw.BorderSide(width: 1, color: PdfColors.grey300),
                    ),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      pw.Text(
                        'تم إنشاؤه بواسطة نظام إدارة المهام',
                        style: const pw.TextStyle(
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );

      // طباعة المستند
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: _titleController.text,
      );
    } catch (e) {
      debugPrint('خطأ في طباعة المستند: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء طباعة المستند: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصدير المستند كملف PDF
  Future<void> _exportToPdf() async {
    try {
      // الحصول على نص المحرر
      final plainText = _documentController.textController?.text ?? '';

      // تحديد نوع المستند
      String documentTypeText = '';
      switch (_selectedType) {
        case TextDocumentType.note:
          documentTypeText = 'ملاحظة';
          break;
        case TextDocumentType.report:
          documentTypeText = 'تقرير';
          break;
        // تم إزالة template - غير موجود في enum
        case TextDocumentType.letter:
          documentTypeText = 'خطاب';
          break;
        case TextDocumentType.memo:
          documentTypeText = 'مذكرة';
          break;
        case TextDocumentType.contract:
          documentTypeText = 'عقد';
          break;
        case TextDocumentType.other:
          documentTypeText = 'مستند آخر';
          break;
      }

      // تصدير إلى PDF
      final filePath = await _pdfExportService.exportToPdf(
        title: _titleController.text,
        data: {
          'content': plainText,
          'documentType': documentTypeText,
          'createdAt': DateTime.now().toString(),
          'isShared': _isShared,
        },
        fileName:
            '${_titleController.text.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );

      if (filePath != null) {
        Get.snackbar(
          'تم التصدير',
          'تم تصدير المستند بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تصدير المستند',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير المستند: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير المستند: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNew ? 'إنشاء مستند جديد' : 'تحرير المستند'),
        actions: [
          // زر الطباعة
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة',
            onPressed: _printDocument,
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير كملف PDF',
            onPressed: _exportToPdf,
          ),
          // زر التحرير/الحفظ
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            tooltip: _isEditing ? 'حفظ' : 'تحرير',
            onPressed: _isEditing
                ? _saveDocument
                : () {
                    setState(() {
                      _isEditing = true;
                    });
                  },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // عنوان المستند
                  TextField(
                    controller: _titleController,
                    focusNode: _titleFocusNode,
                    decoration: AppStyles.inputDecoration(
                      labelText: 'عنوان المستند',
                      prefixIcon: const Icon(Icons.title),
                    ),
                    enabled: _isEditing,
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),

                  // نوع المستند
                  DropdownButtonFormField<TextDocumentType>(
                    decoration: AppStyles.inputDecoration(
                      labelText: 'نوع المستند',
                      prefixIcon: const Icon(Icons.category),
                    ),
                    value: _selectedType,
                    items: TextDocumentType.values.map((type) {
                      String label;
                      IconData icon;

                      switch (type) {
                        case TextDocumentType.note:
                          label = 'ملاحظة';
                          icon = Icons.note;
                          break;
                        case TextDocumentType.report:
                          label = 'تقرير';
                          icon = Icons.description;
                          break;
                        case TextDocumentType.template:
                          label = 'قالب';
                          icon = Icons.article_outlined;
                          break;
                        case TextDocumentType.letter:
                          label = 'خطاب';
                          icon = Icons.mail_outline;
                          break;
                        case TextDocumentType.memo:
                          label = 'مذكرة';
                          icon = Icons.sticky_note_2_outlined;
                          break;
                        case TextDocumentType.contract:
                          label = 'عقد';
                          icon = Icons.handshake_outlined;
                          break;
                        case TextDocumentType.other:
                          label = 'أخرى';
                          icon = Icons.more_horiz;
                          break;
                      }

                      return DropdownMenuItem<TextDocumentType>(
                        value: type,
                        child: Row(
                          children: [
                            Icon(icon, size: 20),
                            const SizedBox(width: 8),
                            Text(label),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: _isEditing
                        ? (value) {
                            if (value != null) {
                              setState(() {
                                _selectedType = value;
                              });
                            }
                          }
                        : null,
                  ),
                  const SizedBox(height: 16),

                  // خيار المشاركة
                  SwitchListTile(
                    title: const Text('مشاركة المستند مع الآخرين'),
                    subtitle:
                        const Text('السماح للآخرين بالوصول إلى هذا المستند'),
                    value: _isShared,
                    onChanged: _isEditing
                        ? (value) {
                            setState(() {
                              _isShared = value;
                            });
                          }
                        : null,
                    activeColor: AppColors.primary,
                  ),
                  const SizedBox(height: 16),

                  // محرر النصوص المتقدم
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          // شريط أدوات المحرر
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Row(
                                children: [
                                  // أزرار التنسيق الأساسية
                                  IconButton(
                                    icon: const Icon(Icons.format_bold),
                                    onPressed:
                                        _isEditing ? _applyBoldFormat : null,
                                    tooltip: 'تغميق',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.format_italic),
                                    onPressed:
                                        _isEditing ? _applyItalicFormat : null,
                                    tooltip: 'مائل',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.format_underline),
                                    onPressed: _isEditing
                                        ? _applyUnderlineFormat
                                        : null,
                                    tooltip: 'تسطير',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.format_color_text),
                                    onPressed:
                                        _isEditing ? _applyTextColor : null,
                                    tooltip: 'لون النص',
                                  ),

                                  const VerticalDivider(),

                                  // أزرار المحاذاة
                                  IconButton(
                                    icon: const Icon(Icons.format_align_right),
                                    onPressed: _isEditing
                                        ? _applyRightAlignment
                                        : null,
                                    tooltip: 'محاذاة لليمين',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.format_align_center),
                                    onPressed: _isEditing
                                        ? _applyCenterAlignment
                                        : null,
                                    tooltip: 'توسيط',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.format_align_left),
                                    onPressed:
                                        _isEditing ? _applyLeftAlignment : null,
                                    tooltip: 'محاذاة لليسار',
                                  ),

                                  const VerticalDivider(),

                                  // أزرار القوائم
                                  IconButton(
                                    icon:
                                        const Icon(Icons.format_list_bulleted),
                                    onPressed:
                                        _isEditing ? _applyBulletList : null,
                                    tooltip: 'قائمة نقطية',
                                  ),
                                  IconButton(
                                    icon:
                                        const Icon(Icons.format_list_numbered),
                                    onPressed:
                                        _isEditing ? _applyNumberedList : null,
                                    tooltip: 'قائمة رقمية',
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // محرر النصوص
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(8),
                                  bottomRight: Radius.circular(8),
                                ),
                              ),
                              child: _isLoading
                                  ? const Center(
                                      child: Text('جاري تحميل المحرر...'),
                                    )
                                  : _documentController.textController == null
                                      ? const Center(
                                          child: Text('جاري تحميل المحرر...'),
                                        )
                                      : TextField(
                                          controller: _documentController
                                              .textController,
                                          focusNode: _editorFocusNode,
                                          enabled: _isEditing,
                                          maxLines: null,
                                          decoration: const InputDecoration(
                                            hintText:
                                                'اكتب محتوى المستند هنا...',
                                            border: InputBorder.none,
                                          ),
                                          style: const TextStyle(fontSize: 16),
                                        ),

                              // TODO: سيتم تنفيذ SuperEditor في المستقبل
                              // SuperEditor.standard(
                              //   editor: _editor,
                              //   focusNode: _editorFocusNode,
                              //   inputSource: _isEditing
                              //       ? TextInputSource.keyboard
                              //       : TextInputSource.none,
                              // ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
