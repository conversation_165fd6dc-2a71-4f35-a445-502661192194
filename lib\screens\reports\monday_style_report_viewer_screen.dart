import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../controllers/report_controller.dart';
'../../models/reporting/report_result_model.dart';
import '../../routes/app_routes.dart';
import '../../services/power_bi_analytics_service.dart';
import '../../database/database_helper.dart';
import '../../widgets/reporting/report_filter_panel.dart';
import '../../widgets/reporting/report_toolbar.dart';
import '../../widgets/reporting/visualizations/visualization_factory.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

/// شاشة عرض التقرير بتصميم Monday.com
///
/// تعرض تفاصيل التقرير ونتائجه بتصميم مشابه لـ Monday.com
class MondayStyleReportViewerScreen extends StatefulWidget {
  const MondayStyleReportViewerScreen({Key? key}) : super(key: key);

  @override
  State<MondayStyleReportViewerScreen> createState() =>
      _MondayStyleReportViewerScreenState();
}

class _MondayStyleReportViewerScreenState
    extends State<MondayStyleReportViewerScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  final PowerBIAnalyticsService _powerBIService =
      Get.find<PowerBIAnalyticsService>();

  late String _reportId;
  EnhancedReport? _report;
  ReportResult? _reportResult;
  bool _isLoading = true;
  bool _isExecuting = false;
  String? _errorMessage;

  // قائمة بالمفاتيح الأجنبية المكتشفة
  final Map<String, Map<String, String>> _foreignKeys = {};

  @override
  void initState() {
    super.initState();
    try {
      // Handle different argument formats
      if (Get.arguments is String) {
        _reportId = Get.arguments as String;
      } else if (Get.arguments is Map<String, dynamic>) {
        _reportId = Get.arguments['reportId'] as String;
      } else if (Get.arguments == null) {
        debugPrint('خطأ: المعاملات فارغة');
        _errorMessage = 'لم يتم تحديد معرف التقرير';
        return;
      } else {
        debugPrint('خطأ: نوع المعاملات غير صالح: ${Get.arguments.runtimeType}');
        _errorMessage = 'خطأ في معاملات التقرير';
        return;
      }
      _loadReport();
    } catch (e) {
      debugPrint('خطأ في استخراج معرف التقرير: $e');
      _errorMessage = 'خطأ في استخراج معرف التقرير: $e';
    }
  }

  /// تحميل التقرير
  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل التقرير
      final report = await _reportController.getReportById(_reportId);

      if (report == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'لم يتم العثور على التقرير';
        });
        return;
      }

      setState(() {
        _report = report;
      });

      // تنفيذ التقرير
      await _executeReport();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل التقرير: $e';
      });
    }
  }

  /// تنفيذ التقرير
  Future<void> _executeReport() async {
    if (_report == null) return;

    setState(() {
      _isExecuting = true;
      _errorMessage = null;
    });

    try {
      // تحديد فترة التقرير
      final now = DateTime.now();
      DateTime startDate;
      DateTime endDate = now;

      switch (_report!.period) {
        case ReportPeriod.today:
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case ReportPeriod.yesterday:
          startDate = DateTime(now.year, now.month, now.day - 1);
          endDate = DateTime(now.year, now.month, now.day);
          break;
        case ReportPeriod.thisWeek:
          // بداية الأسبوع (الأحد)
          startDate = DateTime(now.year, now.month, now.day - now.weekday % 7);
          break;
        case ReportPeriod.lastWeek:
          // بداية الأسبوع الماضي
          startDate =
              DateTime(now.year, now.month, now.day - now.weekday % 7 - 7);
          endDate = DateTime(now.year, now.month, now.day - now.weekday % 7);
          break;
        case ReportPeriod.thisMonth:
          startDate = DateTime(now.year, now.month, 1);
          break;
        case ReportPeriod.lastMonth:
          startDate = DateTime(now.year, now.month - 1, 1);
          endDate = DateTime(now.year, now.month, 1);
          break;
        case ReportPeriod.thisQuarter:
          final quarter = (now.month - 1) ~/ 3;
          startDate = DateTime(now.year, quarter * 3 + 1, 1);
          break;
        case ReportPeriod.lastQuarter:
          final quarter = (now.month - 1) ~/ 3;
          if (quarter == 0) {
            startDate = DateTime(now.year - 1, 10, 1);
            endDate = DateTime(now.year, 1, 1);
          } else {
            startDate = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
            endDate = DateTime(now.year, quarter * 3 + 1, 1);
          }
          break;
        case ReportPeriod.thisYear:
          startDate = DateTime(now.year, 1, 1);
          break;
        case ReportPeriod.lastYear:
          startDate = DateTime(now.year - 1, 1, 1);
          endDate = DateTime(now.year, 1, 1);
          break;
        case ReportPeriod.custom:
          startDate =
              _report!.customStartDate ?? DateTime(now.year, now.month, 1);
          endDate = _report!.customEndDate ?? now;
          break;
        default:
          startDate = DateTime(now.year, now.month, 1);
      }

      // تنفيذ التقرير
      final result = await _reportController.executeReport(
        _reportId,
        dateRange: DateTimeRange(start: startDate, end: endDate),
      );

      setState(() {
        _reportResult = result;
        _isLoading = false;
        _isExecuting = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isExecuting = false;
        _errorMessage = 'حدث خطأ أثناء تنفيذ التقرير: $e';
      });
    }
  }

  /// تصدير التقرير
  Future<void> _exportReport(ReportFormat format) async {
    if (_report == null || _reportResult == null) return;

    try {
      final filePath = await _reportController.exportReport(
        reportId: _reportId,
        format: format,
      );

      if (filePath != null) {
        Get.snackbar(
          'تم التصدير بنجاح',
          'تم تصدير التقرير إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 5),
          backgroundColor: Colors.green.withAlpha(179),
          colorText: Colors.white,
          margin: const EdgeInsets.all(8),
          mainButton: TextButton(
            onPressed: () {
              // فتح الملف
              _reportController.openExportedFile(filePath);
            },
            child: const Text(
              'فتح',
              style: TextStyle(color: Colors.white),
            ),
          ),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تصدير التقرير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withAlpha(179),
          colorText: Colors.white,
          margin: const EdgeInsets.all(8),
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(179),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('عرض التقرير'),
        ),
        body: const Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('عرض التقرير'),
        ),
        body: ErrorView(
          message: _errorMessage!,
          onRetry: _loadReport,
        ),
      );
    }

    if (_report == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('عرض التقرير'),
        ),
        body: const ErrorView(
          message: 'لم يتم العثور على التقرير',
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_report!.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _executeReport,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // الانتقال إلى شاشة تعديل التقرير
              Get.toNamed(
                AppRoutes.createReport,
                arguments: _reportId,
              );
            },
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: Icon(
              _report!.isFavorite ? Icons.star : Icons.star_border,
              color: _report!.isFavorite ? Colors.amber : null,
            ),
            onPressed: () {
              _reportController.toggleFavorite(_reportId);
              setState(() {
                _report = _report!.copyWith(
                  isFavorite: !_report!.isFavorite,
                );
              });
            },
            tooltip:
                _report!.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الأدوات
          ReportToolbar(
            onExportPdf: () => _exportReport(ReportFormat.pdf),
            onExportExcel: () => _exportReport(ReportFormat.excel),
            onExportCsv: () => _exportReport(ReportFormat.csv),
            onShare: () {
              // مشاركة التقرير
              Get.dialog(
                AlertDialog(
                  title: const Text('مشاركة التقرير'),
                  content: const Text('هذه الميزة قيد التطوير'),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              );
            },
            onPrint: () {
              // طباعة التقرير
              Get.dialog(
                AlertDialog(
                  title: const Text('طباعة التقرير'),
                  content: const Text('هذه الميزة قيد التطوير'),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              );
            },
          ),

          // لوحة الفلاتر
          ReportFilterPanel(
            filters: _report!.filters,
            period: _report!.period,
            customStartDate: _report!.customStartDate,
            customEndDate: _report!.customEndDate,
            onFiltersChanged: (filters) {
              // تحديث الفلاتر وإعادة تنفيذ التقرير
              _reportController.updateReportFilters(_reportId, filters);
              setState(() {
                _report = _report!.copyWith(filters: filters);
              });
              _executeReport();
            },
            onPeriodChanged: (period, startDate, endDate) {
              // تحديث الفترة وإعادة تنفيذ التقرير
              _reportController.updateReportPeriod(
                  _reportId, period, startDate, endDate);
              setState(() {
                _report = _report!.copyWith(
                  period: period,
                  customStartDate: startDate,
                  customEndDate: endDate,
                );
              });
              _executeReport();
            },
            isEditable: true,
          ),

          // مؤشر التحميل أثناء تنفيذ التقرير
          if (_isExecuting) const LinearProgressIndicator(),

          // محتوى التقرير
          Expanded(
            child: _buildReportContent(),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التقرير
  Widget _buildReportContent() {
    if (_reportResult == null) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    if (!_reportResult!.isSuccess) {
      return ErrorView(
        message:
            'فشل تنفيذ التقرير: ${_reportResult!.errorMessages?.join(', ') ?? 'خطأ غير معروف'}',
        onRetry: _executeReport,
      );
    }

    // إذا لم تكن هناك تصورات مرئية، عرض البيانات كجدول
    if (_report!.visualizations.isEmpty) {
      return _buildDataTable();
    }

    // عرض التصورات المرئية
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص التقرير
          if (_reportResult!.summary != null &&
              _reportResult!.summary!.isNotEmpty)
            _buildSummarySection(),

          const SizedBox(height: 24),

          // التصورات المرئية
          for (final visualization in _report!.visualizations)
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: _buildVisualization(visualization),
            ),

          const SizedBox(height: 24),

          // جدول البيانات
          _buildDataTable(),
        ],
      ),
    );
  }

  /// بناء قسم الملخص
  Widget _buildSummarySection() {
    final summary = _reportResult!.summary!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص التقرير',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                for (final entry in summary.entries)
                  _buildSummaryItem(entry.key, entry.value),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem(String key, dynamic value) {
    String displayKey = key;
    String displayValue = '';

    // تنسيق المفتاح
    displayKey = key
        .replaceAll('_', ' ')
        .replaceAll('total', 'إجمالي ')
        .replaceAll('average', 'متوسط ');

    // تنسيق القيمة
    if (value is double) {
      displayValue = value.toStringAsFixed(2);
    } else if (value is DateTime) {
      displayValue = DateFormat('yyyy/MM/dd').format(value);
    } else {
      displayValue = value.toString();
    }

    return SizedBox(
      width: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayKey,
            style: AppStyles.captionMedium.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 4),
          Text(
            displayValue,
            style: AppStyles.titleMedium,
          ),
        ],
      ),
    );
  }

  /// بناء التصور المرئي
  Widget _buildVisualization(ReportVisualization visualization) {
    // الحصول على بيانات التصور المرئي
    final visualizationData =
        _reportResult!.visualizationData?[visualization.id];

    if (visualizationData == null || visualizationData.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                visualization.title,
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 16),
              const Center(
                child: Text('لا توجد بيانات للعرض'),
              ),
            ],
          ),
        ),
      );
    }

    // إنشاء التصور المرئي باستخدام المصنع
    return VisualizationFactory.createVisualization(
      visualization: visualization,
      data: visualizationData,
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    final data = _reportResult!.data;

    if (data == null || data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    // استخراج أسماء الأعمدة
    final columns = data.first.keys.toList();

    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'بيانات التقرير',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SingleChildScrollView(
                  child: DataTable(
                    columns: [
                      for (final column in columns)
                        DataColumn(
                          label: Text(
                            column,
                            style: AppStyles.titleSmall,
                          ),
                        ),
                    ],
                    rows: [
                      for (final row in data)
                        DataRow(
                          cells: [
                            for (final column in columns)
                              DataCell(
                                Text(
                                  _formatCellValue(row[column], column),
                                  style: AppStyles.bodyMedium,
                                ),
                              ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق قيمة الخلية
  String _formatCellValue(dynamic value, [String? columnName]) {
    if (value == null) {
      return '';
    }

    if (value is DateTime) {
      return DateFormat('yyyy/MM/dd').format(value);
    }

    if (value is double) {
      return value.toStringAsFixed(2);
    }

    // التحقق مما إذا كان عمود مفتاح أجنبي
    if (columnName != null &&
        columnName.endsWith('_id') &&
        value.toString().isNotEmpty) {
      // البحث عن عمود العرض المقابل في البيانات
      final displayColumnName = "${columnName}_display";

      // التحقق من وجود البيانات
      if (_reportResult != null &&
          _reportResult!.data != null &&
          _reportResult!.data!.isNotEmpty) {
        // البحث عن الصف الحالي
        for (final row in _reportResult!.data!) {
          // التحقق من أن هذا هو الصف الصحيح
          if (row[columnName] == value && row.containsKey(displayColumnName)) {
            // استخدام قيمة العرض إذا كانت موجودة
            final displayValue = row[displayColumnName];
            if (displayValue != null && displayValue.toString().isNotEmpty) {
              return displayValue.toString();
            }
          }
        }
      }

      // إذا لم يتم العثور على قيمة العرض، استخدم الآلية القديمة
      // استخراج اسم الجدول المرتبط
      final relatedTableName = columnName.replaceAll('_id', '');

      // التحقق من وجود الجدول في قائمة المفاتيح الأجنبية
      if (_foreignKeys.containsKey(columnName)) {
        // استخدام القيمة المخزنة مسبقًا إذا كانت موجودة
        final cachedValue =
            _getForeignKeyDisplayValueFromCache(columnName, value.toString());
        if (cachedValue != null) {
          return cachedValue;
        }
      }

      // إذا لم تكن القيمة مخزنة، قم بتخزينها للاستخدام المستقبلي
      _loadForeignKeyDisplayValue(
          relatedTableName, columnName, value.toString());
    }

    return value.toString();
  }

  /// الحصول على قيمة المفتاح الأجنبي من الذاكرة المؤقتة
  String? _getForeignKeyDisplayValueFromCache(
      String columnName, String foreignKeyValue) {
    try {
      if (_foreignKeys.containsKey(columnName)) {
        final cachedValues = _foreignKeys[columnName]!;
        if (cachedValues.containsKey(foreignKeyValue)) {
          return cachedValues[foreignKeyValue];
        }
      }
      return null;
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على قيمة المفتاح الأجنبي من الذاكرة المؤقتة: $e');
      return null;
    }
  }

  /// تحميل قيمة المفتاح الأجنبي وتخزينها في الذاكرة المؤقتة
  Future<void> _loadForeignKeyDisplayValue(
      String tableName, String columnName, String foreignKeyValue) async {
    try {
      // التحقق من وجود الجدول
      final tables = await _powerBIService.getAvailableTables();
      if (!tables.contains(tableName)) {
        return;
      }

      // البحث عن عمود مناسب للعرض (name، title، إلخ)
      final columns = await _powerBIService.getTableColumns(tableName);
      String? displayColumn;

      for (final column in columns) {
        final columnName = column['name'] as String;
        if (['name', 'title', 'label', 'description'].contains(columnName)) {
          displayColumn = columnName;
          break;
        }
      }

      // إذا لم يتم العثور على عمود مناسب، استخدم المعرف
      displayColumn ??= 'id';

      // استعلام للحصول على القيمة المقروءة
      final db = await Get.find<DatabaseHelper>().database;
      final result = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [foreignKeyValue],
        limit: 1,
      );

      if (result.isNotEmpty && result.first.containsKey(displayColumn)) {
        final displayValue =
            result.first[displayColumn]?.toString() ?? foreignKeyValue;

        // تخزين القيمة في الذاكرة المؤقتة
        if (!_foreignKeys.containsKey(columnName)) {
          _foreignKeys[columnName] = {};
        }

        setState(() {
          _foreignKeys[columnName]![foreignKeyValue] = displayValue;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل قيمة المفتاح الأجنبي: $e');
    }
  }
}
