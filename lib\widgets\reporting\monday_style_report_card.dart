import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
'visualizations/mini_visualization.dart';

/// بطاقة تقرير بتصميم Monday.com
///
/// تعرض معلومات التقرير بتصميم مشابه لـ Monday.com
class MondayStyleReportCard extends StatelessWidget {
  /// التقرير
  final EnhancedReport report;

  /// حدث النقر
  final VoidCallback onTap;

  /// حدث التعديل
  final VoidCallback onEdit;

  /// حدث الحذف
  final VoidCallback onDelete;

  /// حدث تبديل المفضلة
  final VoidCallback onToggleFavorite;

  const MondayStyleReportCard({
    super.key,
    required this.report,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onToggleFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getReportTypeColor(report.type).withAlpha(76), // 0.3 * 255 = 76
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            _buildCardHeader(),

            // محتوى البطاقة
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان التقرير
                    Text(
                      report.title,
                      style: AppStyles.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // وصف التقرير
                    if (report.description != null && report.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        report.description!,
                        style: AppStyles.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    const SizedBox(height: 8),

                    // معاينة التصور المرئي
                    Expanded(
                      child: _buildVisualizationPreview(),
                    ),
                  ],
                ),
              ),
            ),

            // تذييل البطاقة
            _buildCardFooter(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getReportTypeColor(report.type).withAlpha(25), // 0.1 * 255 = 25
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // أيقونة نوع التقرير
          Icon(
            _getReportTypeIcon(report.type),
            size: 16,
            color: _getReportTypeColor(report.type),
          ),

          const SizedBox(width: 8),

          // نوع التقرير
          Expanded(
            child: Text(
              _getReportTypeText(report.type),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: _getReportTypeColor(report.type),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // زر المفضلة
          IconButton(
            icon: Icon(
              report.isFavorite ? Icons.star : Icons.star_border,
              size: 20,
              color: report.isFavorite ? Colors.amber : Colors.grey,
            ),
            onPressed: onToggleFavorite,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: report.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
          ),
        ],
      ),
    );
  }

  /// بناء معاينة التصور المرئي
  Widget _buildVisualizationPreview() {
    // إذا لم يكن هناك تصورات مرئية، عرض رسالة
    if (report.visualizations.isEmpty) {
      return Center(
        child: Text(
          'لا توجد تصورات مرئية',
          style: AppStyles.bodySmall.copyWith(color: Colors.grey),
        ),
      );
    }

    // اختيار التصور المرئي الأول للعرض
    final visualization = report.visualizations.first;

    // عرض معاينة مصغرة للتصور المرئي
    return MiniVisualization(
      type: visualization.type,
      title: visualization.title,
    );
  }

  /// بناء تذييل البطاقة
  Widget _buildCardFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(25), // 0.1 * 255 = 25
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // تاريخ التحديث
          Expanded(
            child: Text(
              'تم التحديث: ${report.updatedAt != null ? DateFormat('yyyy/MM/dd').format(report.updatedAt!) : DateFormat('yyyy/MM/dd').format(report.createdAt)}',
              style: AppStyles.captionSmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // زر التعديل
          IconButton(
            icon: const Icon(Icons.edit, size: 16),
            onPressed: onEdit,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'تعديل',
          ),

          const SizedBox(width: 8),

          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete, size: 16),
            onPressed: onDelete,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'حذف',
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع التقرير
  IconData _getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Icons.assignment;
      case ReportType.userPerformance:
        return Icons.person;
      case ReportType.departmentPerformance:
        return Icons.business;
      case ReportType.timeTracking:
        return Icons.timer;
      case ReportType.taskProgress:
        return Icons.trending_up;
      case ReportType.taskDetails:
        return Icons.list_alt;
      case ReportType.taskCompletion:
        return Icons.check_circle;
      case ReportType.userActivity:
        return Icons.history;
      case ReportType.departmentWorkload:
        return Icons.work;
      case ReportType.projectStatus:
        return Icons.folder;
      case ReportType.custom:
        return Icons.dashboard_customize;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على نص نوع التقرير
  String _getReportTypeText(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدمين';
      case ReportType.departmentPerformance:
        return 'أداء الأقسام';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهام';
      case ReportType.taskCompletion:
        return 'إكمال المهام';
      case ReportType.userActivity:
        return 'نشاط المستخدمين';
      case ReportType.departmentWorkload:
        return 'عبء العمل للأقسام';
      case ReportType.projectStatus:
        return 'حالة المشاريع';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'تقرير';
    }
  }

  /// الحصول على لون نوع التقرير
  Color _getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Colors.blue;
      case ReportType.userPerformance:
        return Colors.purple;
      case ReportType.departmentPerformance:
        return Colors.teal;
      case ReportType.timeTracking:
        return Colors.orange;
      case ReportType.taskProgress:
        return Colors.green;
      case ReportType.taskDetails:
        return Colors.indigo;
      case ReportType.taskCompletion:
        return Colors.lightGreen;
      case ReportType.userActivity:
        return Colors.amber;
      case ReportType.departmentWorkload:
        return Colors.deepOrange;
      case ReportType.projectStatus:
        return Colors.cyan;
      case ReportType.custom:
        return Colors.pink;
      default:
        return Colors.blueGrey;
    }
  }
}
