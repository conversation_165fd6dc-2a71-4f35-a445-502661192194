import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../models/text_document_model.dart';
import 'api_service.dart';

/// خدمة API للمستندات النصية
class TextDocumentApiService {
  final ApiService _apiService = Get.find<ApiService>();

  /// الحصول على جميع المستندات
  Future<List<TextDocument>> getAllDocuments() async {
    try {
      final response = await _apiService.get('/documents');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات: $e');
      rethrow;
    }
  }

  /// الحصول على مستند بواسطة المعرف
  Future<TextDocument?> getDocumentById(String id) async {
    try {
      final response = await _apiService.get('/documents/$id');
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستند $id: $e');
      return null;
    }
  }

  /// الحصول على المستندات المرتبطة بمهمة
  Future<List<TextDocument>> getDocumentsByTaskId(String taskId) async {
    try {
      final response = await _apiService.get('/documents/task/$taskId');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
      return [];
    }
  }

  /// البحث في المستندات
  Future<List<TextDocument>> searchDocuments(String query) async {
    try {
      final response = await _apiService.get('/documents/search?q=$query');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المستندات: $e');
      return [];
    }
  }

  /// إنشاء مستند جديد
  Future<TextDocument?> createDocument(CreateTextDocumentRequest request) async {
    try {
      final response = await _apiService.post(
        '/documents',
        body: request.toJson(),
      );
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستند: $e');
      rethrow;
    }
  }

  /// تحديث مستند
  Future<TextDocument?> updateDocument(
    String id,
    UpdateTextDocumentRequest request,
  ) async {
    try {
      final response = await _apiService.put(
        '/documents/$id',
        body: request.toJson(),
      );
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستند $id: $e');
      rethrow;
    }
  }

  /// حذف مستند
  Future<bool> deleteDocument(String id) async {
    try {
      final response = await _apiService.delete('/documents/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستند $id: $e');
      return false;
    }
  }

  /// مشاركة مستند
  Future<bool> shareDocument(String id, bool isShared) async {
    try {
      final response = await _apiService.put(
        '/documents/$id/share',
        body: {'isShared': isShared},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مشاركة المستند $id: $e');
      return false;
    }
  }

  /// الحصول على المستندات المشتركة
  Future<List<TextDocument>> getSharedDocuments() async {
    try {
      final response = await _apiService.get('/documents/shared');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة: $e');
      return [];
    }
  }

  /// الحصول على المستندات حسب النوع
  Future<List<TextDocument>> getDocumentsByType(TextDocumentType type) async {
    try {
      final response = await _apiService.get('/documents/type/${type.value}');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات النوع ${type.value}: $e');
      return [];
    }
  }

  /// الحصول على المستندات الحديثة
  Future<List<TextDocument>> getRecentDocuments({int limit = 10}) async {
    try {
      final response = await _apiService.get('/documents/recent?limit=$limit');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة: $e');
      return [];
    }
  }

  /// تصدير مستند
  Future<String?> exportDocument(String id, String format) async {
    try {
      final response = await _apiService.get('/documents/$id/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير المستند $id: $e');
      return null;
    }
  }
}
