import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../models/task_comment_models.dart';
import '../services/api/task_api_service.dart';

/// متحكم المهام
class TaskController extends GetxController {
  final TaskApiService _apiService = TaskApiService();

  // قوائم المهام
  final RxList<Task> _allTasks = <Task>[].obs;
  final RxList<Task> _filteredTasks = <Task>[].obs;
  final RxList<Task> _myTasks = <Task>[].obs;
  final RxList<Task> _assignedTasks = <Task>[].obs;
  final RxList<TaskComment> _taskComments = <TaskComment>[].obs;

  // المهمة الحالية
  final Rx<Task?> _currentTask = Rx<Task?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<TaskStatus?> _statusFilter = Rx<TaskStatus?>(null);
  final Rx<TaskPriority?> _priorityFilter = Rx<TaskPriority?>(null);
  final Rx<int?> _assigneeFilter = Rx<int?>(null);
  final Rx<int?> _departmentFilter = Rx<int?>(null);
  final RxBool _showOverdueOnly = false.obs;
  final RxBool _showMyTasksOnly = false.obs;

  // Getters
  List<Task> get allTasks => _allTasks;
  List<Task> get filteredTasks => _filteredTasks;
  List<Task> get myTasks => _myTasks;
  List<Task> get assignedTasks => _assignedTasks;
  List<TaskComment> get taskComments => _taskComments;
  Task? get currentTask => _currentTask.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTasks();
  }

  /// تحميل جميع المهام
  Future<void> loadAllTasks() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getAllTasks();
      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام: $e';
      debugPrint('خطأ في تحميل المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مهمة بواسطة المعرف
  Future<void> loadTaskById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final task = await _apiService.getTaskById(id);
      if (task != null) {
        _currentTask.value = task;
        debugPrint('تم تحميل المهمة: ${task.title}');
      } else {
        _error.value = 'المهمة غير موجودة';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل المهمة: $e';
      debugPrint('خطأ في تحميل المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مهام المستخدم الحالي
  Future<void> loadMyTasks(int userId) async {
    try {
      final tasks = await _apiService.getTasksByAssignee(userId);
      _myTasks.assignAll(tasks);
      debugPrint('تم تحميل ${tasks.length} مهمة للمستخدم');
    } catch (e) {
      debugPrint('خطأ في تحميل مهام المستخدم: $e');
    }
  }

  /// تحميل المهام المعينة من قبل المستخدم
  Future<void> loadAssignedTasks(int userId) async {
    try {
      final tasks = await _apiService.getTasksByCreator(userId);
      _assignedTasks.assignAll(tasks);
      debugPrint('تم تحميل ${tasks.length} مهمة معينة');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المعينة: $e');
    }
  }

  /// تحميل تعليقات المهمة
  Future<void> loadTaskComments(int taskId) async {
    try {
      final comments = await _apiService.getTaskComments(taskId);
      _taskComments.assignAll(comments);
      debugPrint('تم تحميل ${comments.length} تعليق');
    } catch (e) {
      debugPrint('خطأ في تحميل تعليقات المهمة: $e');
    }
  }

  /// البحث في المهام
  Future<void> searchTasks(String query) async {
    _searchQuery.value = query;
    
    if (query.isEmpty) {
      _applyFilters();
      return;
    }

    try {
      final tasks = await _apiService.searchTasks(query);
      _filteredTasks.assignAll(tasks);
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      _applyFilters(); // العودة للمرشحات المحلية في حالة الخطأ
    }
  }

  /// إنشاء مهمة جديدة
  Future<bool> createTask({
    required String title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    TaskPriority priority = TaskPriority.medium,
    int? estimatedTime,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final request = CreateTaskRequest(
        title: title,
        description: description,
        taskTypeId: taskTypeId,
        assigneeId: assigneeId,
        departmentId: departmentId,
        startDate: startDate?.millisecondsSinceEpoch ~/ 1000,
        dueDate: dueDate?.millisecondsSinceEpoch ~/ 1000,
        priority: priority,
        estimatedTime: estimatedTime,
      );

      final newTask = await _apiService.createTask(request);
      if (newTask != null) {
        _allTasks.add(newTask);
        _currentTask.value = newTask;
        _applyFilters();
        debugPrint('تم إنشاء المهمة: ${newTask.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المهمة: $e';
      debugPrint('خطأ في إنشاء المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مهمة
  Future<bool> updateTask({
    required int id,
    String? title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    TaskStatus? status,
    TaskPriority? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final request = UpdateTaskRequest(
        title: title,
        description: description,
        taskTypeId: taskTypeId,
        assigneeId: assigneeId,
        departmentId: departmentId,
        startDate: startDate?.millisecondsSinceEpoch ~/ 1000,
        dueDate: dueDate?.millisecondsSinceEpoch ~/ 1000,
        status: status,
        priority: priority,
        completionPercentage: completionPercentage,
        estimatedTime: estimatedTime,
        actualTime: actualTime,
      );

      final updatedTask = await _apiService.updateTask(id, request);
      if (updatedTask != null) {
        final index = _allTasks.indexWhere((task) => task.id == id);
        if (index != -1) {
          _allTasks[index] = updatedTask;
        }
        _currentTask.value = updatedTask;
        _applyFilters();
        debugPrint('تم تحديث المهمة: ${updatedTask.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المهمة: $e';
      debugPrint('خطأ في تحديث المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مهمة
  Future<bool> deleteTask(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.deleteTask(id);
      if (success) {
        _allTasks.removeWhere((task) => task.id == id);
        if (_currentTask.value?.id == id) {
          _currentTask.value = null;
        }
        _applyFilters();
        debugPrint('تم حذف المهمة');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المهمة: $e';
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تغيير حالة المهمة
  Future<bool> changeTaskStatus(int id, TaskStatus status) async {
    return await updateTask(
      id: id,
      status: status,
      completedAt: status == TaskStatus.completed 
          ? DateTime.now() 
          : null,
    );
  }

  /// تعيين مهمة لمستخدم
  Future<bool> assignTask(int taskId, int assigneeId) async {
    return await updateTask(
      id: taskId,
      assigneeId: assigneeId,
    );
  }

  /// إضافة تعليق على المهمة
  Future<bool> addTaskComment(int taskId, String content) async {
    try {
      final comment = await _apiService.addTaskComment(taskId, content);
      if (comment != null) {
        _taskComments.add(comment);
        debugPrint('تم إضافة التعليق');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      return false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = List<Task>.from(_allTasks);

    // تطبيق مرشح الحالة
    if (_statusFilter.value != null) {
      filtered = filtered.where((task) => task.status == _statusFilter.value).toList();
    }

    // تطبيق مرشح الأولوية
    if (_priorityFilter.value != null) {
      filtered = filtered.where((task) => task.priority == _priorityFilter.value).toList();
    }

    // تطبيق مرشح المعين إليه
    if (_assigneeFilter.value != null) {
      filtered = filtered.where((task) => task.assigneeId == _assigneeFilter.value).toList();
    }

    // تطبيق مرشح القسم
    if (_departmentFilter.value != null) {
      filtered = filtered.where((task) => task.departmentId == _departmentFilter.value).toList();
    }

    // تطبيق مرشح المهام المتأخرة
    if (_showOverdueOnly.value) {
      filtered = filtered.where((task) => task.isOverdue).toList();
    }

    // تطبيق مرشح البحث المحلي
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((task) =>
          task.title.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          (task.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }

    _filteredTasks.assignAll(filtered);
  }

  /// تعيين مرشحات
  void setStatusFilter(TaskStatus? status) {
    _statusFilter.value = status;
    _applyFilters();
  }

  void setPriorityFilter(TaskPriority? priority) {
    _priorityFilter.value = priority;
    _applyFilters();
  }

  void setAssigneeFilter(int? assigneeId) {
    _assigneeFilter.value = assigneeId;
    _applyFilters();
  }

  void setDepartmentFilter(int? departmentId) {
    _departmentFilter.value = departmentId;
    _applyFilters();
  }

  void setOverdueFilter(bool showOverdueOnly) {
    _showOverdueOnly.value = showOverdueOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _statusFilter.value = null;
    _priorityFilter.value = null;
    _assigneeFilter.value = null;
    _departmentFilter.value = null;
    _showOverdueOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllTasks();
  }

  /// الحصول على إحصائيات المهام
  Map<String, int> get taskStats {
    return {
      'total': _allTasks.length,
      'pending': _allTasks.where((t) => t.status == TaskStatus.pending).length,
      'inProgress': _allTasks.where((t) => t.status == TaskStatus.inProgress).length,
      'completed': _allTasks.where((t) => t.status == TaskStatus.completed).length,
      'overdue': _allTasks.where((t) => t.isOverdue).length,
    };
  }
}
