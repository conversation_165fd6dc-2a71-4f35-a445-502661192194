import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../controllers/report_controller.dart';
'../../models/reporting/report_result_model.dart';
import '../../services/power_bi_analytics_service.dart';
import '../../database/database_helper.dart';
import '../../widgets/reporting/report_filter_panel.dart';
import '../../widgets/reporting/visualizations/bar_chart_visualization.dart';
import '../../widgets/reporting/visualizations/pie_chart_visualization.dart';
import '../../widgets/reporting/visualizations/line_chart_visualization.dart';
import '../../widgets/reporting/visualizations/table_visualization.dart';
import '../../widgets/reporting/visualizations/kpi_card_visualization.dart';
import 'report_builder_screen.dart';

/// شاشة عرض التقرير
///
/// تعرض نتائج تنفيذ التقرير
class ReportViewerScreen extends StatefulWidget {
  /// معرف التقرير
  final String reportId;

  const ReportViewerScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ReportViewerScreen> createState() => _ReportViewerScreenState();
}

class _ReportViewerScreenState extends State<ReportViewerScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  final PowerBIAnalyticsService _powerBIService =
      Get.find<PowerBIAnalyticsService>();
  final ScrollController _scrollController = ScrollController();

  // قائمة بالمفاتيح الأجنبية المكتشفة
  final Map<String, Map<String, String>> _foreignKeys = {};

  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to delay loading until after the initial build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadReport();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل التقرير
  Future<void> _loadReport() async {
    try {
      await _reportController.loadReport(widget.reportId);
      // Add a small delay to ensure the UI has time to update
      await Future.delayed(const Duration(milliseconds: 50));
      await _reportController.executeCurrentReport();
    } catch (e) {
      debugPrint('Error loading report: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          final report = _reportController.currentReport.value;
          return Text(report?.title ?? 'عرض التقرير');
        }),
        actions: [
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _reportController.executeCurrentReport,
            tooltip: 'تحديث',
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _showExportDialog,
            tooltip: 'تصدير',
          ),
          // زر التعديل
          Obx(() {
            final report = _reportController.currentReport.value;
            if (report == null) return const SizedBox.shrink();

            return IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () =>
                  Get.to(() => ReportBuilderScreen(report: report)),
              tooltip: 'تعديل',
            );
          }),
        ],
      ),
      body: Obx(() {
        if (_reportController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (_reportController.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  _reportController.error.value,
                  style: AppStyles.bodyMedium.copyWith(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadReport,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final report = _reportController.currentReport.value;
        final result = _reportController.currentResult.value;

        if (report == null) {
          return const Center(
            child: Text('لم يتم العثور على التقرير'),
          );
        }

        if (result == null || !result.isSuccess) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.warning_amber,
                  color: Colors.orange,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  result?.errorMessages?.join('\n') ??
                      'حدث خطأ أثناء تنفيذ التقرير',
                  style: AppStyles.bodyMedium.copyWith(color: Colors.orange),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _reportController.executeCurrentReport,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات التقرير
              _buildReportInfo(report),
              const SizedBox(height: 16),

              // لوحة الفلاتر
              ReportFilterPanel(
                filters: report.filters,
                period: report.period,
                customStartDate: report.customStartDate,
                customEndDate: report.customEndDate,
                onFiltersChanged: (filters) {
                  final updatedReport = report.copyWith(filters: filters);
                  _reportController.updateReport(updatedReport).then((_) {
                    _reportController.executeCurrentReport();
                  });
                },
                onPeriodChanged: (period, startDate, endDate) {
                  final updatedReport = report.copyWith(
                    period: period,
                    customStartDate: startDate,
                    customEndDate: endDate,
                  );
                  _reportController.updateReport(updatedReport).then((_) {
                    _reportController.executeCurrentReport();
                  });
                },
              ),
              const SizedBox(height: 24),

              // ملخص التقرير
              if (result.summary != null && result.summary!.isNotEmpty)
                _buildReportSummary(result.summary!),

              const SizedBox(height: 24),

              // التصورات المرئية
              ...report.visualizations.map((visualization) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: _buildVisualization(visualization, result),
                );
              }),

              // بيانات التقرير
              if (result.data != null && result.data!.isNotEmpty) ...[
                const SizedBox(height: 24),
                Text(
                  'بيانات التقرير',
                  style: AppStyles.titleLarge,
                ),
                const SizedBox(height: 16),
                _buildDataTable(result.data!),
              ],
            ],
          ),
        );
      }),
    );
  }

  /// بناء معلومات التقرير
  Widget _buildReportInfo(EnhancedReport report) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان التقرير
            Text(
              report.title,
              style: AppStyles.titleLarge,
            ),
            if (report.description != null &&
                report.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                report.description!,
                style: AppStyles.bodyMedium,
              ),
            ],
            const SizedBox(height: 16),
            // معلومات إضافية
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                // نوع التقرير
                _buildInfoChip(
                  Icons.category,
                  _getReportTypeName(report.type),
                ),
                // تاريخ الإنشاء
                _buildInfoChip(
                  Icons.calendar_today,
                  'تم الإنشاء: ${DateFormat('yyyy-MM-dd').format(report.createdAt)}',
                ),
                // تاريخ التحديث
                if (report.updatedAt != null)
                  _buildInfoChip(
                    Icons.update,
                    'تم التحديث: ${DateFormat('yyyy-MM-dd').format(report.updatedAt!)}',
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة معلومات
  Widget _buildInfoChip(IconData icon, String label) {
    return Chip(
      avatar: Icon(
        icon,
        size: 16,
        color: Colors.grey[700],
      ),
      label: Text(
        label,
        style: AppStyles.bodySmall,
      ),
      backgroundColor: Colors.grey[200],
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  /// بناء ملخص التقرير
  Widget _buildReportSummary(Map<String, dynamic> summary) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التقرير',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: _buildSummaryItems(summary),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عناصر الملخص
  List<Widget> _buildSummaryItems(Map<String, dynamic> summary) {
    final List<Widget> items = [];

    summary.forEach((key, value) {
      if (value is Map) {
        // إذا كانت القيمة خريطة، أضف عنوانًا فرعيًا وعناصر الخريطة
        items.add(
          SizedBox(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatSummaryKey(key),
                  style: AppStyles.titleSmall,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  children:
                      _buildSummaryItems(Map<String, dynamic>.from(value)),
                ),
              ],
            ),
          ),
        );
      } else {
        // إذا كانت القيمة بسيطة، أضف بطاقة معلومات
        items.add(
          SizedBox(
            width: 200,
            child: Card(
              elevation: 1,
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatSummaryKey(key),
                      style:
                          AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value.toString(),
                      style: AppStyles.titleMedium,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    });

    return items;
  }

  /// بناء التصور المرئي
  Widget _buildVisualization(
      ReportVisualization visualization, ReportResult result) {
    // التحقق من وجود بيانات للتصور المرئي
    final visualizationData =
        result.visualizationData?[visualization.id] ?? result.data ?? [];

    if (visualizationData.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                visualization.title,
                style: AppStyles.titleMedium,
              ),
              if (visualization.description != null &&
                  visualization.description!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  visualization.description!,
                  style: AppStyles.bodySmall,
                ),
              ],
              const SizedBox(height: 16),
              const Center(
                child: Text('لا توجد بيانات لعرض التصور المرئي'),
              ),
            ],
          ),
        ),
      );
    }

    // إنشاء التصور المرئي حسب النوع
    switch (visualization.type) {
      case VisualizationType.barChart:
        return BarChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: visualizationData,
          xAxisField:
              visualization.xAxisField ?? visualization.dataFields.first,
          xAxisLabel: visualization.xAxisLabel,
          yAxisField: visualization.yAxisField ?? visualization.dataFields.last,
          yAxisLabel: visualization.yAxisLabel,
          orientation: visualization.orientation,
          showValues: visualization.showValues,
          showLabels: visualization.showLabels,
          showGrid: visualization.showGrid,
          showLegend: visualization.showLegend,
          legendPosition: visualization.legendPosition,
          seriesColors: visualization.seriesColors,
          width: visualization.width,
          height: visualization.height,
        );

      case VisualizationType.pieChart:
        return PieChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: visualizationData,
          valueField: visualization.dataFields.length > 1
              ? visualization.dataFields[1]
              : 'value',
          labelField: visualization.dataFields.first,
          showValues: visualization.showValues,
          showPercentages: true,
          showLegend: visualization.showLegend,
          legendPosition: visualization.legendPosition,
          sectionColors: visualization.seriesColors,
          width: visualization.width,
          height: visualization.height,
        );

      case VisualizationType.lineChart:
        // تحديد حقول البيانات للمخطط الخطي
        final yAxisFields = visualization.dataFields.length > 1
            ? visualization.dataFields.sublist(1)
            : [visualization.dataFields.first];

        return LineChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: visualizationData,
          xAxisField:
              visualization.xAxisField ?? visualization.dataFields.first,
          xAxisLabel: visualization.xAxisLabel,
          yAxisFields: yAxisFields,
          yAxisLabels: visualization.dataLabels,
          yAxisLabel: visualization.yAxisLabel,
          showDots: true,
          showArea: false,
          showLabels: visualization.showLabels,
          showGrid: visualization.showGrid,
          showLegend: visualization.showLegend,
          legendPosition: visualization.legendPosition,
          seriesColors: visualization.seriesColors,
          width: visualization.width,
          height: visualization.height,
        );

      case VisualizationType.table:
        return TableVisualization(
          title: visualization.title,
          description: visualization.description,
          data: visualizationData,
          fields: visualization.dataFields,
          fieldLabels: visualization.dataLabels,
          showHeader: true,
          showAlternatingRows: true,
          showBorders: true,
          showRowNumbers: true,
          rowsPerPage: 10,
          width: visualization.width,
          height: visualization.height,
        );

      case VisualizationType.kpiCard:
        // التحقق من وجود بيانات كافية
        if (visualizationData.isEmpty) {
          return const Center(
            child: Text('لا توجد بيانات كافية لعرض بطاقة مؤشر الأداء'),
          );
        }

        // استخدام أول عنصر في البيانات
        final kpiData = visualizationData.first;

        return KpiCardVisualization(
          title: visualization.title,
          description: visualization.description,
          data: kpiData,
          valueField: visualization.dataFields.first,
          targetField: visualization.dataFields.length > 1
              ? visualization.dataFields[1]
              : null,
          previousField: visualization.dataFields.length > 2
              ? visualization.dataFields[2]
              : null,
          unit: visualization.settings?['unit'],
          format: visualization.settings?['format'],
          color: visualization.seriesColors?.first,
          icon: Icons.analytics,
          width: visualization.width,
          height: visualization.height ?? 200,
        );

      default:
        // للأنواع الأخرى، عرض بطاقة بسيطة
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  visualization.title,
                  style: AppStyles.titleMedium,
                ),
                if (visualization.description != null &&
                    visualization.description!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    visualization.description!,
                    style: AppStyles.bodySmall,
                  ),
                ],
                const SizedBox(height: 16),
                const Center(
                  child: Text('نوع التصور المرئي غير مدعوم حاليًا'),
                ),
              ],
            ),
          ),
        );
    }
  }

  /// بناء جدول البيانات
  Widget _buildDataTable(List<Map<String, dynamic>> data) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    // الحصول على أسماء الأعمدة
    final columns = data.first.keys.toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: columns.map((column) {
            return DataColumn(
              label: Text(
                _formatSummaryKey(column),
                style: AppStyles.titleSmall,
              ),
            );
          }).toList(),
          rows: data.map((row) {
            return DataRow(
              cells: columns.map((column) {
                return DataCell(
                  FutureBuilder<String>(
                    future: _getForeignKeyDisplayValue(column, row[column]),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Text(
                          row[column]?.toString() ?? '',
                          style: AppStyles.bodyMedium,
                        );
                      }

                      return Text(
                        snapshot.data ?? (row[column]?.toString() ?? ''),
                        style: AppStyles.bodyMedium,
                      );
                    },
                  ),
                );
              }).toList(),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// عرض مربع حوار التصدير
  void _showExportDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('تصدير التقرير'),
        content: const Text('اختر تنسيق التصدير:'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _reportController.exportCurrentReportToPdf().then((filePath) {
                if (filePath != null) {
                  _reportController.openFile(filePath);
                }
              });
            },
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _reportController.exportCurrentReportToExcel().then((filePath) {
                if (filePath != null) {
                  _reportController.openFile(filePath);
                }
              });
            },
            child: const Text('Excel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _reportController.exportCurrentReportToCsv().then((filePath) {
                if (filePath != null) {
                  _reportController.openFile(filePath);
                }
              });
            },
            child: const Text('CSV'),
          ),
        ],
      ),
    );
  }

  /// تنسيق مفتاح الملخص
  String _formatSummaryKey(String key) {
    // تحويل camelCase إلى كلمات منفصلة
    final formattedKey = key.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(0)}',
    );

    // تحويل الحرف الأول إلى حرف كبير
    return formattedKey.substring(0, 1).toUpperCase() +
        formattedKey.substring(1);
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'أداء القسم';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.taskCompletion:
        return 'إكمال المهام';
      case ReportType.userActivity:
        return 'نشاط المستخدم';
      case ReportType.departmentWorkload:
        return 'عبء العمل للقسم';
      case ReportType.projectStatus:
        return 'حالة المشروع';
      case ReportType.custom:
        return 'مخصص';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على قيمة المفتاح الأجنبي
  Future<String> _getForeignKeyDisplayValue(
      String columnName, dynamic value) async {
    if (value == null || value.toString().isEmpty) {
      return '';
    }

    // التحقق مما إذا كان عمود مفتاح أجنبي
    if (columnName.endsWith('_id')) {
      // البحث عن عمود العرض المقابل في البيانات
      final displayColumnName = "${columnName}_display";

      // التحقق من وجود البيانات
      if (_reportController.currentResult.value != null &&
          _reportController.currentResult.value!.data != null &&
          _reportController.currentResult.value!.data!.isNotEmpty) {
        // البحث عن الصف الحالي
        for (final row in _reportController.currentResult.value!.data!) {
          // التحقق من أن هذا هو الصف الصحيح
          if (row[columnName] == value && row.containsKey(displayColumnName)) {
            // استخدام قيمة العرض إذا كانت موجودة
            final displayValue = row[displayColumnName];
            if (displayValue != null && displayValue.toString().isNotEmpty) {
              return displayValue.toString();
            }
          }
        }
      }

      // استخراج اسم الجدول المرتبط
      final relatedTableName = columnName.replaceAll('_id', '');

      // التحقق من وجود الجدول في قائمة المفاتيح الأجنبية
      if (_foreignKeys.containsKey(columnName)) {
        // استخدام القيمة المخزنة مسبقًا إذا كانت موجودة
        final cachedValue =
            _getForeignKeyDisplayValueFromCache(columnName, value.toString());
        if (cachedValue != null) {
          return cachedValue;
        }
      }

      // إذا لم تكن القيمة مخزنة، قم بتخزينها للاستخدام المستقبلي
      await _loadForeignKeyDisplayValue(
          relatedTableName, columnName, value.toString());

      // محاولة الحصول على القيمة مرة أخرى بعد التحميل
      final cachedValue =
          _getForeignKeyDisplayValueFromCache(columnName, value.toString());
      if (cachedValue != null) {
        return cachedValue;
      }
    }

    // إذا لم يكن عمود مفتاح أجنبي أو لم يتم العثور على القيمة، أرجع القيمة الأصلية
    if (value is DateTime) {
      return DateFormat('yyyy/MM/dd').format(value);
    } else if (value is double) {
      return value.toStringAsFixed(2);
    }

    return value.toString();
  }

  /// الحصول على قيمة المفتاح الأجنبي من الذاكرة المؤقتة
  String? _getForeignKeyDisplayValueFromCache(
      String columnName, String foreignKeyValue) {
    try {
      if (_foreignKeys.containsKey(columnName)) {
        final cachedValues = _foreignKeys[columnName]!;
        if (cachedValues.containsKey(foreignKeyValue)) {
          return cachedValues[foreignKeyValue];
        }
      }
      return null;
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على قيمة المفتاح الأجنبي من الذاكرة المؤقتة: $e');
      return null;
    }
  }

  /// تحميل قيمة المفتاح الأجنبي وتخزينها في الذاكرة المؤقتة
  Future<void> _loadForeignKeyDisplayValue(
      String tableName, String columnName, String foreignKeyValue) async {
    try {
      // التحقق من وجود الجدول
      final tables = await _powerBIService.getAvailableTables();
      if (!tables.contains(tableName)) {
        return;
      }

      // البحث عن عمود مناسب للعرض (name، title، إلخ)
      final columns = await _powerBIService.getTableColumns(tableName);
      String? displayColumn;

      for (final column in columns) {
        final columnName = column['name'] as String;
        if (['name', 'title', 'label', 'description'].contains(columnName)) {
          displayColumn = columnName;
          break;
        }
      }

      // إذا لم يتم العثور على عمود مناسب، استخدم المعرف
      displayColumn ??= 'id';

      // استعلام للحصول على القيمة المقروءة
      final db = await Get.find<DatabaseHelper>().database;
      final result = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [foreignKeyValue],
        limit: 1,
      );

      if (result.isNotEmpty && result.first.containsKey(displayColumn)) {
        final displayValue =
            result.first[displayColumn]?.toString() ?? foreignKeyValue;

        // تخزين القيمة في الذاكرة المؤقتة
        if (!_foreignKeys.containsKey(columnName)) {
          _foreignKeys[columnName] = {};
        }

        setState(() {
          _foreignKeys[columnName]![foreignKeyValue] = displayValue;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل قيمة المفتاح الأجنبي: $e');
    }
  }
}
