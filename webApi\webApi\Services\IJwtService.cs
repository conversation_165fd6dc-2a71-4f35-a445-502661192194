using webApi.Models;
using webApi.Models.Auth;

namespace webApi.Services;

/// <summary>
/// واجهة خدمة JWT
/// </summary>
public interface IJwtService
{
    /// <summary>
    /// إنشاء رمز الوصول JWT
    /// </summary>
    /// <param name="user">بيانات المستخدم</param>
    /// <returns>رمز الوصول</returns>
    string GenerateAccessToken(User user);

    /// <summary>
    /// إنشاء رمز التحديث
    /// </summary>
    /// <returns>رمز التحديث</returns>
    string GenerateRefreshToken();

    /// <summary>
    /// التحقق من صحة رمز الوصول
    /// </summary>
    /// <param name="token">رمز الوصول</param>
    /// <returns>معرف المستخدم إذا كان الرمز صحيح</returns>
    int? ValidateAccessToken(string token);

    /// <summary>
    /// الحصول على معرف المستخدم من الرمز
    /// </summary>
    /// <param name="token">رمز الوصول</param>
    /// <returns>معرف المستخدم</returns>
    int? GetUserIdFromToken(string token);

    /// <summary>
    /// الحصول على دور المستخدم من الرمز
    /// </summary>
    /// <param name="token">رمز الوصول</param>
    /// <returns>دور المستخدم</returns>
    UserRole? GetUserRoleFromToken(string token);

    /// <summary>
    /// الحصول على تاريخ انتهاء صلاحية الرمز
    /// </summary>
    /// <param name="token">رمز الوصول</param>
    /// <returns>تاريخ انتهاء الصلاحية</returns>
    DateTime? GetTokenExpiration(string token);
}
