import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';

'../../services/reporting/report_service.dart';

/// زر تصدير التقرير
///
/// يوفر واجهة مستخدم لتصدير التقارير بتنسيقات مختلفة
class ReportExportButton extends StatefulWidget {
  /// معرف التقرير
  final String reportId;

  /// عنوان التقرير
  final String title;

  /// حجم الزر
  final double? size;

  /// لون الزر
  final Color? color;

  /// نص الزر
  final String? buttonText;

  /// هل يعرض نص الزر
  final bool showText;

  /// هل يعرض أيقونة الزر
  final bool showIcon;

  const ReportExportButton({
    super.key,
    required this.reportId,
    required this.title,
    this.size,
    this.color,
    this.buttonText,
    this.showText = true,
    this.showIcon = true,
  });

  @override
  State<ReportExportButton> createState() => _ReportExportButtonState();
}

class _ReportExportButtonState extends State<ReportExportButton> {
  /// خدمة التقارير
  final ReportService _reportService = Get.find<ReportService>();

  /// مؤشر الحوار
  BuildContext? _dialogContext;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<ReportFormat>(
      tooltip: 'تصدير التقرير',
      icon: Icon(
        Icons.download,
        color: widget.color ?? Theme.of(context).primaryColor,
        size: widget.size,
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: ReportFormat.pdf,
          child: Row(
            children: [
              Icon(Icons.picture_as_pdf),
              SizedBox(width: 8),
              Text('تصدير كـ PDF'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: ReportFormat.excel,
          child: Row(
            children: [
              Icon(Icons.table_chart),
              SizedBox(width: 8),
              Text('تصدير كـ Excel'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: ReportFormat.csv,
          child: Row(
            children: [
              Icon(Icons.format_list_bulleted),
              SizedBox(width: 8),
              Text('تصدير كـ CSV'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: ReportFormat.json,
          child: Row(
            children: [
              Icon(Icons.code),
              SizedBox(width: 8),
              Text('تصدير كـ JSON'),
            ],
          ),
        ),
      ],
      onSelected: (format) => _exportReport(format, context),
    );
  }

  /// تصدير التقرير
  void _exportReport(ReportFormat format, BuildContext context) {
    // عرض مؤشر التحميل
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext dialogContext) {
        _dialogContext = dialogContext;
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text('جاري تصدير التقرير...'),
            ],
          ),
        );
      },
    );

    // تنفيذ عملية التصدير في الخلفية
    _performExport(format, context);
  }

  /// تنفيذ عملية التصدير
  Future<void> _performExport(ReportFormat format, BuildContext originalContext) async {
    // حفظ نسخة من السياق
    final context = originalContext;
    String? filePath;

    try {
      // تصدير التقرير حسب التنسيق المحدد
      switch (format) {
        case ReportFormat.pdf:
          filePath = await _reportService.exportReportToPdf(widget.reportId);
          break;
        case ReportFormat.excel:
          filePath = await _reportService.exportReportToExcel(widget.reportId);
          break;
        case ReportFormat.csv:
          filePath = await _reportService.exportReportToCsv(widget.reportId);
          break;
        case ReportFormat.json:
          filePath = await _reportService.exportReportToJson(widget.reportId);
          break;
        default:
          filePath = null;
      }

      // التحقق من أن الـ widget لا يزال موجودًا
      if (!mounted) return;

      // إغلاق مؤشر التحميل
      if (_dialogContext != null) {
        Navigator.of(_dialogContext!).pop();
        _dialogContext = null;
      }

      // عرض النتيجة
      if (!mounted) return;

      // استخدام ميثود آمنة للتعامل مع السياق
      _safeShowSnackbar(
        context: context,
        message: filePath != null
            ? 'تم تصدير التقرير بتنسيق ${_getFormatName(format)} بنجاح'
            : 'فشل تصدير التقرير بتنسيق ${_getFormatName(format)}',
        isSuccess: filePath != null,
        filePath: filePath,
      );
    } catch (e) {
      // التحقق من أن الـ widget لا يزال موجودًا
      if (!mounted) return;

      // إغلاق مؤشر التحميل
      if (_dialogContext != null) {
        Navigator.of(_dialogContext!).pop();
        _dialogContext = null;
      }

      // عرض رسالة الخطأ
      if (!mounted) return;

      _safeShowSnackbar(
        context: context,
        message: 'فشل تصدير التقرير بتنسيق ${_getFormatName(format)}: ${e.toString()}',
        isSuccess: false,
      );
    }
  }

  /// عرض رسالة في شريط الإشعارات بطريقة آمنة
  void _safeShowSnackbar({
    required BuildContext context,
    required String message,
    required bool isSuccess,
    String? filePath,
  }) {
    if (!mounted) return;

    final snackBar = SnackBar(
      content: Text(message),
      backgroundColor: isSuccess ? Colors.green : Colors.red,
      duration: const Duration(seconds: 5),
      action: isSuccess && filePath != null ? SnackBarAction(
        label: 'فتح',
        textColor: Colors.white,
        onPressed: () {
          // استخدام OpenFile لفتح الملف
          OpenFile.open(filePath);
        },
      ) : null,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// الحصول على اسم التنسيق
  String _getFormatName(ReportFormat format) {
    switch (format) {
      case ReportFormat.pdf:
        return 'PDF';
      case ReportFormat.excel:
        return 'Excel';
      case ReportFormat.csv:
        return 'CSV';
      case ReportFormat.json:
        return 'JSON';
      case ReportFormat.image:
        return 'صورة';
      default:
        return 'غير معروف';
    }
  }
}
