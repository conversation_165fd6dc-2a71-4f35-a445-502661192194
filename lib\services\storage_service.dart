import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_models.dart';

/// خدمة التخزين المحلي
class StorageService {
  static const String _sessionDataKey = 'session_data';
  static const String _userPreferencesKey = 'user_preferences';
  static const String _appSettingsKey = 'app_settings';

  SharedPreferences? _prefs;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    debugPrint('تم تهيئة خدمة التخزين المحلي');
  }

  /// التأكد من تهيئة SharedPreferences
  Future<SharedPreferences> get prefs async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  /// حفظ بيانات الجلسة
  Future<void> saveSessionData(SessionData sessionData) async {
    try {
      final prefsInstance = await prefs;
      final jsonString = jsonEncode(sessionData.toJson());
      await prefsInstance.setString(_sessionDataKey, jsonString);
      debugPrint('تم حفظ بيانات الجلسة');
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات الجلسة: $e');
      rethrow;
    }
  }

  /// الحصول على بيانات الجلسة
  Future<SessionData?> getSessionData() async {
    try {
      final prefsInstance = await prefs;
      final jsonString = prefsInstance.getString(_sessionDataKey);
      
      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
        final sessionData = SessionData.fromJson(jsonData);
        
        // التحقق من صحة الجلسة
        if (!sessionData.isExpired) {
          debugPrint('تم العثور على جلسة صالحة');
          return sessionData;
        } else {
          debugPrint('انتهت صلاحية الجلسة المحفوظة');
          await clearSessionData();
          return null;
        }
      }
      
      debugPrint('لا توجد بيانات جلسة محفوظة');
      return null;
    } catch (e) {
      debugPrint('خطأ في قراءة بيانات الجلسة: $e');
      await clearSessionData(); // مسح البيانات التالفة
      return null;
    }
  }

  /// مسح بيانات الجلسة
  Future<void> clearSessionData() async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.remove(_sessionDataKey);
      debugPrint('تم مسح بيانات الجلسة');
    } catch (e) {
      debugPrint('خطأ في مسح بيانات الجلسة: $e');
    }
  }

  /// حفظ تفضيلات المستخدم
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final prefsInstance = await prefs;
      final jsonString = jsonEncode(preferences);
      await prefsInstance.setString(_userPreferencesKey, jsonString);
      debugPrint('تم حفظ تفضيلات المستخدم');
    } catch (e) {
      debugPrint('خطأ في حفظ تفضيلات المستخدم: $e');
    }
  }

  /// الحصول على تفضيلات المستخدم
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final prefsInstance = await prefs;
      final jsonString = prefsInstance.getString(_userPreferencesKey);
      
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      
      return {};
    } catch (e) {
      debugPrint('خطأ في قراءة تفضيلات المستخدم: $e');
      return {};
    }
  }

  /// حفظ إعدادات التطبيق
  Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    try {
      final prefsInstance = await prefs;
      final jsonString = jsonEncode(settings);
      await prefsInstance.setString(_appSettingsKey, jsonString);
      debugPrint('تم حفظ إعدادات التطبيق');
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات التطبيق: $e');
    }
  }

  /// الحصول على إعدادات التطبيق
  Future<Map<String, dynamic>> getAppSettings() async {
    try {
      final prefsInstance = await prefs;
      final jsonString = prefsInstance.getString(_appSettingsKey);
      
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      
      return {};
    } catch (e) {
      debugPrint('خطأ في قراءة إعدادات التطبيق: $e');
      return {};
    }
  }

  /// حفظ قيمة نصية
  Future<void> setString(String key, String value) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.setString(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة النصية: $e');
    }
  }

  /// الحصول على قيمة نصية
  Future<String?> getString(String key) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getString(key);
    } catch (e) {
      debugPrint('خطأ في قراءة القيمة النصية: $e');
      return null;
    }
  }

  /// حفظ قيمة منطقية
  Future<void> setBool(String key, bool value) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.setBool(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة المنطقية: $e');
    }
  }

  /// الحصول على قيمة منطقية
  Future<bool> getBool(String key, {bool defaultValue = false}) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getBool(key) ?? defaultValue;
    } catch (e) {
      debugPrint('خطأ في قراءة القيمة المنطقية: $e');
      return defaultValue;
    }
  }

  /// حفظ قيمة رقمية
  Future<void> setInt(String key, int value) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.setInt(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة الرقمية: $e');
    }
  }

  /// الحصول على قيمة رقمية
  Future<int> getInt(String key, {int defaultValue = 0}) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getInt(key) ?? defaultValue;
    } catch (e) {
      debugPrint('خطأ في قراءة القيمة الرقمية: $e');
      return defaultValue;
    }
  }

  /// حفظ قيمة عشرية
  Future<void> setDouble(String key, double value) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.setDouble(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة العشرية: $e');
    }
  }

  /// الحصول على قيمة عشرية
  Future<double> getDouble(String key, {double defaultValue = 0.0}) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getDouble(key) ?? defaultValue;
    } catch (e) {
      debugPrint('خطأ في قراءة القيمة العشرية: $e');
      return defaultValue;
    }
  }

  /// حفظ قائمة نصية
  Future<void> setStringList(String key, List<String> value) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.setStringList(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القائمة النصية: $e');
    }
  }

  /// الحصول على قائمة نصية
  Future<List<String>> getStringList(String key) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getStringList(key) ?? [];
    } catch (e) {
      debugPrint('خطأ في قراءة القائمة النصية: $e');
      return [];
    }
  }

  /// حذف مفتاح معين
  Future<void> remove(String key) async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف المفتاح: $e');
    }
  }

  /// مسح جميع البيانات المحفوظة
  Future<void> clear() async {
    try {
      final prefsInstance = await prefs;
      await prefsInstance.clear();
      debugPrint('تم مسح جميع البيانات المحفوظة');
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
    }
  }

  /// التحقق من وجود مفتاح
  Future<bool> containsKey(String key) async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.containsKey(key);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود المفتاح: $e');
      return false;
    }
  }

  /// الحصول على جميع المفاتيح
  Future<Set<String>> getKeys() async {
    try {
      final prefsInstance = await prefs;
      return prefsInstance.getKeys();
    } catch (e) {
      debugPrint('خطأ في الحصول على المفاتيح: $e');
      return {};
    }
  }
}
