import 'user_model.dart';
import 'department_model.dart';
import 'task_type_models.dart';

/// نموذج حالة المهمة
class TaskStatus {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final String? icon;
  final int orderIndex;
  final bool isDefault;
  final int createdAt;

  const TaskStatus({
    required this.id,
    required this.name,
    this.description,
    this.color,
    this.icon,
    required this.orderIndex,
    this.isDefault = false,
    required this.createdAt,
  });

  factory TaskStatus.fromJson(Map<String, dynamic> json) {
    return TaskStatus(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      orderIndex: json['orderIndex'] as int,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'icon': icon,
      'orderIndex': orderIndex,
      'isDefault': isDefault,
      'createdAt': createdAt,
    };
  }

  @override
  String toString() {
    return 'TaskStatus(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskStatus && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج أولوية المهمة
class TaskPriority {
  final int id;
  final String name;
  final String? description;
  final int level;
  final String? color;
  final String? icon;
  final bool isDefault;
  final int createdAt;
  final bool isActive;
  final int? updatedAt;
  final int? createdBy;

  // Navigation properties
  final User? createdByUser;

  const TaskPriority({
    required this.id,
    required this.name,
    this.description,
    required this.level,
    this.color,
    this.icon,
    this.isDefault = false,
    required this.createdAt,
    this.isActive = true,
    this.updatedAt,
    this.createdBy,
    this.createdByUser,
  });

  factory TaskPriority.fromJson(Map<String, dynamic> json) {
    return TaskPriority(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      level: json['level'] as int,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      updatedAt: json['updatedAt'] as int?,
      createdBy: json['createdBy'] as int?,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'level': level,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
      'createdAt': createdAt,
      'isActive': isActive,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
    };
  }

  @override
  String toString() {
    return 'TaskPriority(id: $id, name: $name, level: $level)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskPriority && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج المهمة
class Task {
  final int id;
  final String title;
  final String? description;
  final int? taskTypeId;
  final int creatorId;
  final int? assigneeId;
  final int? departmentId;
  final int createdAt; // Unix timestamp
  final int? startDate; // Unix timestamp
  final int? dueDate; // Unix timestamp
  final int? completedAt; // Unix timestamp
  final int status; // ID من جدول TaskStatus
  final int priority; // ID من جدول TaskPriority
  final int completionPercentage;
  final int? estimatedTime; // بالدقائق
  final int? actualTime; // بالدقائق
  final bool isDeleted;

  // Navigation properties
  final User? creator;
  final User? assignee;
  final Department? department;
  final TaskType? taskType;
  final TaskStatus? statusNavigation;
  final TaskPriority? priorityNavigation;

  const Task({
    required this.id,
    required this.title,
    this.description,
    this.taskTypeId,
    required this.creatorId,
    this.assigneeId,
    this.departmentId,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.completedAt,
    this.status = 1, // Default: pending
    this.priority = 2, // Default: medium
    this.completionPercentage = 0,
    this.estimatedTime,
    this.actualTime,
    this.isDeleted = false,
    this.creator,
    this.assignee,
    this.department,
    this.taskType,
    this.statusNavigation,
    this.priorityNavigation,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      taskTypeId: json['taskTypeId'] as int?,
      creatorId: json['creatorId'] as int,
      assigneeId: json['assigneeId'] as int?,
      departmentId: json['departmentId'] as int?,
      createdAt: json['createdAt'] as int,
      startDate: json['startDate'] as int?,
      dueDate: json['dueDate'] as int?,
      completedAt: json['completedAt'] as int?,
      status: json['status'] as int? ?? 1,
      priority: json['priority'] as int? ?? 2,
      completionPercentage: json['completionPercentage'] as int? ?? 0,
      estimatedTime: json['estimatedTime'] as int?,
      actualTime: json['actualTime'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      creator: json['creator'] != null
          ? User.fromJson(json['creator'] as Map<String, dynamic>)
          : null,
      assignee: json['assignee'] != null
          ? User.fromJson(json['assignee'] as Map<String, dynamic>)
          : null,
      department: json['department'] != null
          ? Department.fromJson(json['department'] as Map<String, dynamic>)
          : null,
      taskType: json['taskType'] != null
          ? TaskType.fromJson(json['taskType'] as Map<String, dynamic>)
          : null,
      statusNavigation: json['statusNavigation'] != null
          ? TaskStatus.fromJson(json['statusNavigation'] as Map<String, dynamic>)
          : null,
      priorityNavigation: json['priorityNavigation'] != null
          ? TaskPriority.fromJson(json['priorityNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'taskTypeId': taskTypeId,
      'creatorId': creatorId,
      'assigneeId': assigneeId,
      'departmentId': departmentId,
      'createdAt': createdAt,
      'startDate': startDate,
      'dueDate': dueDate,
      'completedAt': completedAt,
      'status': status,
      'priority': priority,
      'completionPercentage': completionPercentage,
      'estimatedTime': estimatedTime,
      'actualTime': actualTime,
      'isDeleted': isDeleted,
    };
  }

  Task copyWith({
    int? id,
    String? title,
    String? description,
    int? taskTypeId,
    int? creatorId,
    int? assigneeId,
    int? departmentId,
    int? createdAt,
    int? startDate,
    int? dueDate,
    int? completedAt,
    int? status,
    int? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
    bool? isDeleted,
    User? creator,
    User? assignee,
    Department? department,
    TaskType? taskType,
    TaskStatus? statusNavigation,
    TaskPriority? priorityNavigation,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      taskTypeId: taskTypeId ?? this.taskTypeId,
      creatorId: creatorId ?? this.creatorId,
      assigneeId: assigneeId ?? this.assigneeId,
      departmentId: departmentId ?? this.departmentId,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      actualTime: actualTime ?? this.actualTime,
      isDeleted: isDeleted ?? this.isDeleted,
      creator: creator ?? this.creator,
      assignee: assignee ?? this.assignee,
      department: department ?? this.department,
      taskType: taskType ?? this.taskType,
      statusNavigation: statusNavigation ?? this.statusNavigation,
      priorityNavigation: priorityNavigation ?? this.priorityNavigation,
    );
  }

  /// التحقق من كون المهمة متأخرة
  bool get isOverdue {
    if (dueDate == null || completionPercentage >= 100) return false;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now > dueDate!;
  }

  /// التحقق من كون المهمة مكتملة
  bool get isCompleted => completionPercentage >= 100;

  /// الحصول على تاريخ الاستحقاق كـ DateTime
  DateTime? get dueDateDateTime => dueDate != null 
      ? DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000)
      : null;

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'Task(id: $id, title: $title, status: $status, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Task && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج نوع المهمة - سيتم استيراده من task_type_models.dart
// تم نقل TaskType إلى ملف منفصل لتجنب التكرار

/// نموذج طلب إنشاء مهمة جديدة
class CreateTaskRequest {
  final String title;
  final String? description;
  final int? taskTypeId;
  final int? assigneeId;
  final int? departmentId;
  final int? startDate;
  final int? dueDate;
  final int priority;
  final int? estimatedTime;

  const CreateTaskRequest({
    required this.title,
    this.description,
    this.taskTypeId,
    this.assigneeId,
    this.departmentId,
    this.startDate,
    this.dueDate,
    this.priority = 2, // Default: medium
    this.estimatedTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'taskTypeId': taskTypeId,
      'assigneeId': assigneeId,
      'departmentId': departmentId,
      'startDate': startDate,
      'dueDate': dueDate,
      'priority': priority,
      'estimatedTime': estimatedTime,
    };
  }
}

/// نموذج طلب تحديث مهمة
class UpdateTaskRequest {
  final String? title;
  final String? description;
  final int? taskTypeId;
  final int? assigneeId;
  final int? departmentId;
  final int? startDate;
  final int? dueDate;
  final int? status;
  final int? priority;
  final int? completionPercentage;
  final int? estimatedTime;
  final int? actualTime;

  const UpdateTaskRequest({
    this.title,
    this.description,
    this.taskTypeId,
    this.assigneeId,
    this.departmentId,
    this.startDate,
    this.dueDate,
    this.status,
    this.priority,
    this.completionPercentage,
    this.estimatedTime,
    this.actualTime,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (description != null) json['description'] = description;
    if (taskTypeId != null) json['taskTypeId'] = taskTypeId;
    if (assigneeId != null) json['assigneeId'] = assigneeId;
    if (departmentId != null) json['departmentId'] = departmentId;
    if (startDate != null) json['startDate'] = startDate;
    if (dueDate != null) json['dueDate'] = dueDate;
    if (status != null) json['status'] = status;
    if (priority != null) json['priority'] = priority;
    if (completionPercentage != null) json['completionPercentage'] = completionPercentage;
    if (estimatedTime != null) json['estimatedTime'] = estimatedTime;
    if (actualTime != null) json['actualTime'] = actualTime;
    return json;
  }
}

// تم نقل TaskComment إلى task_comment_models.dart لتجنب التكرار
