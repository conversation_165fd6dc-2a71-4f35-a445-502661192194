import 'user_model.dart';

/// أنواع سجلات النظام
enum SystemLogType {
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  error('error', 'خطأ'),
  security('security', 'أمان'),
  audit('audit', 'مراجعة');

  const SystemLogType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static SystemLogType fromValue(String value) {
    return SystemLogType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SystemLogType.info,
    );
  }
}

/// نموذج سجل النظام - متطابق مع ASP.NET Core API
class SystemLog {
  final int id;
  final String logType;
  final String logLevel;
  final String message;
  final String? details;
  final int? userId;
  final String? ipAddress;
  final int createdAt;

  // Navigation properties
  final User? user;

  const SystemLog({
    required this.id,
    required this.logType,
    required this.logLevel,
    required this.message,
    this.details,
    this.userId,
    this.ipAddress,
    required this.createdAt,
    this.user,
  });

  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as int,
      logType: json['logType'] as String,
      logLevel: json['logLevel'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
      userId: json['userId'] as int?,
      ipAddress: json['ipAddress'] as String?,
      createdAt: json['createdAt'] as int,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'logType': logType,
      'logLevel': logLevel,
      'message': message,
      'details': details,
      'userId': userId,
      'ipAddress': ipAddress,
      'createdAt': createdAt,
    };
  }

  /// الحصول على التاريخ كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);
}

/// نموذج إعدادات النظام - متطابق مع ASP.NET Core API
class SystemSetting {
  final int id;
  final String settingKey;
  final String settingValue;
  final String? settingGroup;
  final String? description;
  final int createdAt;
  final int? updatedAt;
  final int? createdBy;

  // Navigation properties
  final User? createdByNavigation;

  const SystemSetting({
    required this.id,
    required this.settingKey,
    required this.settingValue,
    this.settingGroup,
    this.description,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.createdByNavigation,
  });

  factory SystemSetting.fromJson(Map<String, dynamic> json) {
    return SystemSetting(
      id: json['id'] as int,
      settingKey: json['settingKey'] as String,
      settingValue: json['settingValue'] as String,
      settingGroup: json['settingGroup'] as String?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      createdBy: json['createdBy'] as int?,
      createdByNavigation: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'settingKey': settingKey,
      'settingValue': settingValue,
      'settingGroup': settingGroup,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
    };
  }
}

/// نموذج النسخة الاحتياطية
class Backup {
  final int id;
  final String fileName;
  final String filePath;
  final int fileSize;
  final int createdBy;
  final int createdAt;
  final String? description;
  final bool isAutoBackup;
  final bool isRestored;
  final int? restoredAt;
  final int? restoredBy;

  // Navigation properties
  final User? createdByUser;
  final User? restoredByUser;

  const Backup({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.createdBy,
    required this.createdAt,
    this.description,
    this.isAutoBackup = false,
    this.isRestored = false,
    this.restoredAt,
    this.restoredBy,
    this.createdByUser,
    this.restoredByUser,
  });

  factory Backup.fromJson(Map<String, dynamic> json) {
    return Backup(
      id: json['id'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: json['fileSize'] as int,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      description: json['description'] as String?,
      isAutoBackup: json['isAutoBackup'] as bool? ?? false,
      isRestored: json['isRestored'] as bool? ?? false,
      restoredAt: json['restoredAt'] as int?,
      restoredBy: json['restoredBy'] as int?,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      restoredByUser: json['restoredByNavigation'] != null 
          ? User.fromJson(json['restoredByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'description': description,
      'isAutoBackup': isAutoBackup,
      'isRestored': isRestored,
      'restoredAt': restoredAt,
      'restoredBy': restoredBy,
    };
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الاستعادة كـ DateTime
  DateTime? get restoredAtDateTime => restoredAt != null 
      ? DateTime.fromMillisecondsSinceEpoch(restoredAt! * 1000)
      : null;
}

// تم نقل مودلات Permission و UserPermission إلى ملف منفصل

/// نموذج طلب إنشاء نسخة احتياطية
class CreateBackupRequest {
  final String? description;
  final bool includeFiles;

  const CreateBackupRequest({
    this.description,
    this.includeFiles = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'includeFiles': includeFiles,
    };
  }
}

/// نموذج طلب تحديث إعدادات النظام
class UpdateSystemSettingsRequest {
  final Map<String, String> settings;

  const UpdateSystemSettingsRequest({
    required this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'settings': settings,
    };
  }
}
