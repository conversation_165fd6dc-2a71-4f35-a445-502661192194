import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/report_models.dart';
import '../services/api/reports_api_service.dart';

/// متحكم التقارير
class ReportsController extends GetxController {
  final ReportsApiService _apiService = ReportsApiService();

  // قوائم التقارير
  final RxList<Report> _allReports = <Report>[].obs;
  final RxList<Report> _filteredReports = <Report>[].obs;
  final RxList<Report> _myReports = <Report>[].obs;

  // التقرير الحالي
  final Rx<Report?> _currentReport = Rx<Report?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _typeFilter = Rx<String?>(null);
  final Rx<String?> _statusFilter = Rx<String?>(null);
  final RxBool _showMyReportsOnly = false.obs;

  // Getters
  List<Report> get allReports => _allReports;
  List<Report> get filteredReports => _filteredReports;
  List<Report> get myReports => _myReports;
  Report? get currentReport => _currentReport.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get typeFilter => _typeFilter.value;
  String? get statusFilter => _statusFilter.value;
  bool get showMyReportsOnly => _showMyReportsOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllReports();
    loadMyReports();
  }

  /// تحميل جميع التقارير
  Future<void> loadAllReports() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final reports = await _apiService.getAllReports();
      _allReports.assignAll(reports);
      _applyFilters();
      debugPrint('تم تحميل ${reports.length} تقرير');
    } catch (e) {
      _error.value = 'خطأ في تحميل التقارير: $e';
      debugPrint('خطأ في تحميل التقارير: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل تقاريري
  Future<void> loadMyReports() async {
    try {
      final reports = await _apiService.getMyReports();
      _myReports.assignAll(reports);
      debugPrint('تم تحميل ${reports.length} من تقاريري');
    } catch (e) {
      debugPrint('خطأ في تحميل تقاريري: $e');
    }
  }

  /// الحصول على تقرير بالمعرف
  Future<void> getReportById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final report = await _apiService.getReportById(id);
      _currentReport.value = report;
      debugPrint('تم تحميل التقرير: ${report.title}');
    } catch (e) {
      _error.value = 'خطأ في تحميل التقرير: $e';
      debugPrint('خطأ في تحميل التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء تقرير جديد
  Future<bool> createReport(Report report) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newReport = await _apiService.createReport(report);
      _allReports.add(newReport);
      _applyFilters();
      await loadMyReports();
      debugPrint('تم إنشاء تقرير جديد: ${newReport.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء التقرير: $e';
      debugPrint('خطأ في إنشاء التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث تقرير
  Future<bool> updateReport(int id, Report report) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateReport(id, report);
      final index = _allReports.indexWhere((r) => r.id == id);
      if (index != -1) {
        _allReports[index] = report;
        _applyFilters();
      }
      await loadMyReports();
      debugPrint('تم تحديث التقرير: ${report.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث التقرير: $e';
      debugPrint('خطأ في تحديث التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteReport(id);
      _allReports.removeWhere((r) => r.id == id);
      _applyFilters();
      await loadMyReports();
      debugPrint('تم حذف التقرير');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف التقرير: $e';
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// توليد تقرير
  Future<bool> generateReport(int reportId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.generateReport(reportId);
      await loadAllReports();
      debugPrint('تم توليد التقرير');
      return true;
    } catch (e) {
      _error.value = 'خطأ في توليد التقرير: $e';
      debugPrint('خطأ في توليد التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير تقرير
  Future<String?> exportReport(int reportId, String format) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportReport(reportId, format);
      debugPrint('تم تصدير التقرير: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير التقرير: $e';
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على التقارير حسب النوع
  Future<void> getReportsByType(String type) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final reports = await _apiService.getReportsByType(type);
      _allReports.assignAll(reports);
      _applyFilters();
      debugPrint('تم تحميل ${reports.length} تقرير من نوع $type');
    } catch (e) {
      _error.value = 'خطأ في تحميل تقارير النوع: $e';
      debugPrint('خطأ في تحميل تقارير النوع: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allReports.where((report) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!report.title.toLowerCase().contains(query) &&
            !report.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النوع
      if (_typeFilter.value != null && report.type != _typeFilter.value) {
        return false;
      }

      // مرشح الحالة
      if (_statusFilter.value != null && report.status != _statusFilter.value) {
        return false;
      }

      // مرشح تقاريري فقط
      if (_showMyReportsOnly.value) {
        if (!_myReports.any((myReport) => myReport.id == report.id)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredReports.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النوع
  void setTypeFilter(String? type) {
    _typeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح الحالة
  void setStatusFilter(String? status) {
    _statusFilter.value = status;
    _applyFilters();
  }

  /// تعيين مرشح تقاريري فقط
  void setMyReportsFilter(bool showMyReportsOnly) {
    _showMyReportsOnly.value = showMyReportsOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _typeFilter.value = null;
    _statusFilter.value = null;
    _showMyReportsOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllReports(),
      loadMyReports(),
    ]);
  }
}
