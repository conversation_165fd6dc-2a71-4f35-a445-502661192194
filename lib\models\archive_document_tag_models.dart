import 'archive_models.dart';

/// نموذج ربط الوثائق بالعلامات - متطابق مع ASP.NET Core API
class ArchiveDocumentTag {
  final int documentId;
  final int tagId;

  // Navigation properties
  final ArchiveDocument? document;
  final ArchiveTag? tag;

  const ArchiveDocumentTag({
    required this.documentId,
    required this.tagId,
    this.document,
    this.tag,
  });

  factory ArchiveDocumentTag.fromJson(Map<String, dynamic> json) {
    return ArchiveDocumentTag(
      documentId: json['documentId'] as int,
      tagId: json['tagId'] as int,
      document: json['document'] != null
          ? ArchiveDocument.fromJson(json['document'] as Map<String, dynamic>)
          : null,
      tag: json['tag'] != null
          ? ArchiveTag.fromJson(json['tag'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documentId': documentId,
      'tagId': tagId,
    };
  }

  ArchiveDocumentTag copyWith({
    int? documentId,
    int? tagId,
    ArchiveDocument? document,
    ArchiveTag? tag,
  }) {
    return ArchiveDocumentTag(
      documentId: documentId ?? this.documentId,
      tagId: tagId ?? this.tagId,
      document: document ?? this.document,
      tag: tag ?? this.tag,
    );
  }

  @override
  String toString() {
    return 'ArchiveDocumentTag(documentId: $documentId, tagId: $tagId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArchiveDocumentTag && 
           other.documentId == documentId && 
           other.tagId == tagId;
  }

  @override
  int get hashCode => Object.hash(documentId, tagId);
}

/// نموذج طلب ربط وثيقة بعلامة
class LinkDocumentTagRequest {
  final int documentId;
  final int tagId;

  const LinkDocumentTagRequest({
    required this.documentId,
    required this.tagId,
  });

  Map<String, dynamic> toJson() {
    return {
      'documentId': documentId,
      'tagId': tagId,
    };
  }
}

/// نموذج طلب إلغاء ربط وثيقة بعلامة
class UnlinkDocumentTagRequest {
  final int documentId;
  final int tagId;

  const UnlinkDocumentTagRequest({
    required this.documentId,
    required this.tagId,
  });

  Map<String, dynamic> toJson() {
    return {
      'documentId': documentId,
      'tagId': tagId,
    };
  }
}
