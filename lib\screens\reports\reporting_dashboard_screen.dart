import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../controllers/report_controller.dart';
import '../../controllers/auth_controller.dart';
'../../widgets/reporting/report_card.dart';
import '../../routes/app_routes.dart';
import 'report_builder_screen.dart';
import 'report_viewer_screen.dart';
import 'json_export_settings_screen.dart';

/// شاشة لوحة التقارير الرئيسية
///
/// توفر واجهة لعرض وإدارة التقارير
class ReportingDashboardScreen extends StatefulWidget {
  const ReportingDashboardScreen({super.key});

  @override
  State<ReportingDashboardScreen> createState() => _ReportingDashboardScreenState();
}

class _ReportingDashboardScreenState extends State<ReportingDashboardScreen> with SingleTickerProviderStateMixin {
  final ReportController _reportController = Get.find<ReportController>();
  final AuthController _authController = Get.find<AuthController>();
  late TabController _tabController;
  final RxBool _isSearching = false.obs;
  final RxString _searchQuery = ''.obs;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل التقارير
  Future<void> _loadReports() async {
    await _reportController.loadReports();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          if (_isSearching.value) {
            return TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'بحث عن تقرير...',
                border: InputBorder.none,
              ),
              onChanged: (value) {
                _searchQuery.value = value;
              },
              autofocus: true,
              textDirection: ui.TextDirection.rtl,
            );
          }
          return const Text('نظام التقارير');
        }),
        actions: [
          Obx(() {
            return IconButton(
              icon: Icon(_isSearching.value ? Icons.close : Icons.search),
              onPressed: () {
                if (_isSearching.value) {
                  _searchController.clear();
                  _searchQuery.value = '';
                }
                _isSearching.value = !_isSearching.value;
              },
            );
          }),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _navigateToExportedReports,
            tooltip: 'التقارير المصدرة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'تقاريري'),
            Tab(text: 'المشتركة'),
            Tab(text: 'المفضلة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // تقاريري
          _buildReportsTab(_reportController.reports),

          // التقارير المشتركة
          _buildReportsTab(_reportController.sharedReports),

          // التقارير المفضلة
          _buildReportsTab(_reportController.favoriteReports),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateReportDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// بناء علامة تبويب التقارير
  Widget _buildReportsTab(RxList<EnhancedReport> reports) {
    return Obx(() {
      if (_reportController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (_reportController.error.value.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                _reportController.error.value,
                style: AppStyles.bodyMedium.copyWith(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadReports,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      // تصفية التقارير حسب البحث
      final filteredReports = _searchQuery.value.isEmpty
          ? reports
          : reports.where((report) {
              final query = _searchQuery.value.toLowerCase();
              return report.title.toLowerCase().contains(query) ||
                  (report.description?.toLowerCase().contains(query) ?? false);
            }).toList();

      if (filteredReports.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.assignment_outlined,
                color: Colors.grey,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                _searchQuery.value.isNotEmpty
                    ? 'لا توجد نتائج مطابقة للبحث'
                    : 'لا توجد تقارير',
                style: AppStyles.bodyMedium.copyWith(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              if (_searchQuery.value.isNotEmpty)
                ElevatedButton(
                  onPressed: () {
                    _searchController.clear();
                    _searchQuery.value = '';
                    _isSearching.value = false;
                  },
                  child: const Text('مسح البحث'),
                ),
              if (_searchQuery.value.isEmpty && _tabController.index == 0)
                ElevatedButton(
                  onPressed: () => _showCreateReportDialog(),
                  child: const Text('إنشاء تقرير جديد'),
                ),
            ],
          ),
        );
      }

      // تحديد عدد الأعمدة حسب حجم الشاشة
      final screenWidth = MediaQuery.of(context).size.width;
      int crossAxisCount;

      if (screenWidth < 600) {
        crossAxisCount = 1;
      } else if (screenWidth < 900) {
        crossAxisCount = 2;
      } else if (screenWidth < 1200) {
        crossAxisCount = 3;
      } else {
        crossAxisCount = 4;
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: filteredReports.length,
          itemBuilder: (context, index) {
            final report = filteredReports[index];
            return ReportCard(
              report: report,
              onTap: () => _openReport(report),
              onEdit: _tabController.index == 0 ? () => _editReport(report) : null,
              onDelete: _tabController.index == 0 ? () => _deleteReport(report) : null,
              onFavorite: () => _toggleFavorite(report),
              onExport: () => _exportReport(report),
              onShare: _tabController.index == 0 ? () => _shareReport(report) : null,
            );
          },
        ),
      );
    });
  }

  /// فتح تقرير
  void _openReport(EnhancedReport report) {
    Get.to(() => ReportViewerScreen(reportId: report.id));
  }

  /// تعديل تقرير
  void _editReport(EnhancedReport report) {
    Get.to(() => ReportBuilderScreen(report: report));
  }

  /// حذف تقرير
  void _deleteReport(EnhancedReport report) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف التقرير'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              await _reportController.deleteReport(report.id);
            },
            child: const Text('حذف'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  /// تبديل حالة المفضلة
  void _toggleFavorite(EnhancedReport report) {
    final updatedReport = report.copyWith(
      isFavorite: !report.isFavorite,
    );
    _reportController.updateReport(updatedReport);
  }

  /// تصدير تقرير
  void _exportReport(EnhancedReport report) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تصدير التقرير: ${report.title}',
              style: AppStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'اختر تنسيق التصدير:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildExportButton(
                  context,
                  report,
                  ReportFormat.pdf,
                  Icons.picture_as_pdf,
                  'PDF',
                  Colors.red,
                ),
                _buildExportButton(
                  context,
                  report,
                  ReportFormat.excel,
                  Icons.table_chart,
                  'Excel',
                  Colors.green,
                ),
                _buildExportButton(
                  context,
                  report,
                  ReportFormat.csv,
                  Icons.format_list_bulleted,
                  'CSV',
                  Colors.blue,
                ),
                _buildExportButton(
                  context,
                  report,
                  ReportFormat.json,
                  Icons.code,
                  'JSON',
                  Colors.purple,
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// بناء زر التصدير
  Widget _buildExportButton(
    BuildContext context,
    EnhancedReport report,
    ReportFormat format,
    IconData icon,
    String label,
    Color color,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        _exportReportWithFormat(report, format);
      },
      child: Column(
        children: [
          CircleAvatar(
            backgroundColor: Colors.grey.shade100,
            radius: 28,
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }

  /// تصدير التقرير بتنسيق محدد
  void _exportReportWithFormat(EnhancedReport report, ReportFormat format) {
    if (format == ReportFormat.json) {
      // عرض شاشة إعدادات تصدير JSON
      Get.to(() => JsonExportSettingsScreen(
            reportId: report.id,
            title: report.title,
          ));
    } else {
      _reportController.loadReport(report.id).then((_) {
        _reportController.executeCurrentReport().then((_) {
          switch (format) {
            case ReportFormat.pdf:
              _reportController.exportCurrentReportToPdf();
              break;
            case ReportFormat.excel:
              _reportController.exportCurrentReportToExcel();
              break;
            case ReportFormat.csv:
              _reportController.exportCurrentReportToCsv();
              break;
            default:
              break;
          }
        });
      });
    }
  }

  /// مشاركة تقرير
  void _shareReport(EnhancedReport report) {
    // سيتم تنفيذ هذا لاحقًا
  }

  /// عرض مربع حوار إنشاء تقرير
  void _showCreateReportDialog() {
    Get.to(() => ReportBuilderScreen());
  }

  /// الانتقال إلى شاشة التقارير المصدرة
  void _navigateToExportedReports() {
    Get.toNamed(AppRoutes.exportedReports);
  }
}
