import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/calendar_models.dart';
import '../services/api/calendar_events_api_service.dart';

/// متحكم أحداث التقويم
class CalendarEventsController extends GetxController {
  final CalendarEventsApiService _apiService = CalendarEventsApiService();

  // قوائم الأحداث
  final RxList<CalendarEvent> _allEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _filteredEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _todayEvents = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _upcomingEvents = <CalendarEvent>[].obs;

  // الحدث الحالي
  final Rx<CalendarEvent?> _currentEvent = Rx<CalendarEvent?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<DateTime?> _dateFilter = Rx<DateTime?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<CalendarEvent> get allEvents => _allEvents;
  List<CalendarEvent> get filteredEvents => _filteredEvents;
  List<CalendarEvent> get todayEvents => _todayEvents;
  List<CalendarEvent> get upcomingEvents => _upcomingEvents;
  CalendarEvent? get currentEvent => _currentEvent.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  DateTime? get dateFilter => _dateFilter.value;
  int? get userFilter => _userFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllEvents();
    loadTodayEvents();
    loadUpcomingEvents();
  }

  /// تحميل جميع أحداث التقويم
  Future<void> loadAllEvents() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final events = await _apiService.getAllEvents();
      _allEvents.assignAll(events);
      _applyFilters();
      debugPrint('تم تحميل ${events.length} حدث تقويم');
    } catch (e) {
      _error.value = 'خطأ في تحميل أحداث التقويم: $e';
      debugPrint('خطأ في تحميل أحداث التقويم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل أحداث اليوم
  Future<void> loadTodayEvents() async {
    try {
      final events = await _apiService.getTodayEvents();
      _todayEvents.assignAll(events);
      debugPrint('تم تحميل ${events.length} حدث لليوم');
    } catch (e) {
      debugPrint('خطأ في تحميل أحداث اليوم: $e');
    }
  }

  /// تحميل الأحداث القادمة
  Future<void> loadUpcomingEvents() async {
    try {
      final events = await _apiService.getUpcomingEvents();
      _upcomingEvents.assignAll(events);
      debugPrint('تم تحميل ${events.length} حدث قادم');
    } catch (e) {
      debugPrint('خطأ في تحميل الأحداث القادمة: $e');
    }
  }

  /// الحصول على حدث تقويم بالمعرف
  Future<void> getEventById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final event = await _apiService.getEventById(id);
      _currentEvent.value = event;
      debugPrint('تم تحميل حدث التقويم: ${event.title}');
    } catch (e) {
      _error.value = 'خطأ في تحميل حدث التقويم: $e';
      debugPrint('خطأ في تحميل حدث التقويم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء حدث تقويم جديد
  Future<bool> createEvent(CalendarEvent event) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newEvent = await _apiService.createEvent(event);
      _allEvents.add(newEvent);
      _applyFilters();
      await loadTodayEvents();
      await loadUpcomingEvents();
      debugPrint('تم إنشاء حدث تقويم جديد: ${newEvent.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء حدث التقويم: $e';
      debugPrint('خطأ في إنشاء حدث التقويم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حدث تقويم
  Future<bool> updateEvent(int id, CalendarEvent event) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateEvent(id, event);
      final index = _allEvents.indexWhere((e) => e.id == id);
      if (index != -1) {
        _allEvents[index] = event;
        _applyFilters();
      }
      await loadTodayEvents();
      await loadUpcomingEvents();
      debugPrint('تم تحديث حدث التقويم: ${event.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث حدث التقويم: $e';
      debugPrint('خطأ في تحديث حدث التقويم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حدث تقويم
  Future<bool> deleteEvent(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteEvent(id);
      _allEvents.removeWhere((e) => e.id == id);
      _applyFilters();
      await loadTodayEvents();
      await loadUpcomingEvents();
      debugPrint('تم حذف حدث التقويم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف حدث التقويم: $e';
      debugPrint('خطأ في حذف حدث التقويم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأحداث حسب التاريخ
  Future<void> getEventsByDate(DateTime date) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final events = await _apiService.getEventsByDate(date);
      _allEvents.assignAll(events);
      _applyFilters();
      debugPrint('تم تحميل ${events.length} حدث للتاريخ ${date.toString()}');
    } catch (e) {
      _error.value = 'خطأ في تحميل أحداث التاريخ: $e';
      debugPrint('خطأ في تحميل أحداث التاريخ: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allEvents.where((event) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!event.title.toLowerCase().contains(query) &&
            !event.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح التاريخ
      if (_dateFilter.value != null) {
        final eventDate = DateTime.fromMillisecondsSinceEpoch(event.startTime * 1000);
        final filterDate = _dateFilter.value!;
        if (eventDate.year != filterDate.year ||
            eventDate.month != filterDate.month ||
            eventDate.day != filterDate.day) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && event.userId != _userFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !event.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredEvents.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ
  void setDateFilter(DateTime? date) {
    _dateFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _dateFilter.value = null;
    _userFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllEvents(),
      loadTodayEvents(),
      loadUpcomingEvents(),
    ]);
  }
}
