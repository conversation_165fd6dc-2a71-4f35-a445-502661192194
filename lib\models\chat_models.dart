import 'user_model.dart';
import 'message_models.dart';

/// نموذج مجموعة الدردشة
class ChatGroup {
  final int id;
  final String name;
  final String? description;
  final String? avatar;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isPrivate;
  final int? maxMembers;

  // Navigation properties
  final User? createdByUser;
  final List<GroupMember>? members;
  final List<Message>? messages;

  const ChatGroup({
    required this.id,
    required this.name,
    this.description,
    this.avatar,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isPrivate = false,
    this.maxMembers,
    this.createdByUser,
    this.members,
    this.messages,
  });

  factory ChatGroup.fromJson(Map<String, dynamic> json) {
    return ChatGroup(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      avatar: json['avatar'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isPrivate: json['isPrivate'] as bool? ?? false,
      maxMembers: json['maxMembers'] as int?,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      members: json['groupMembers'] != null 
          ? (json['groupMembers'] as List)
              .map((m) => GroupMember.fromJson(m as Map<String, dynamic>))
              .toList()
          : null,
      messages: json['messages'] != null 
          ? (json['messages'] as List)
              .map((m) => Message.fromJson(m as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isPrivate': isPrivate,
      'maxMembers': maxMembers,
    };
  }

  ChatGroup copyWith({
    int? id,
    String? name,
    String? description,
    String? avatar,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    bool? isPrivate,
    int? maxMembers,
    User? createdByUser,
    List<GroupMember>? members,
    List<Message>? messages,
  }) {
    return ChatGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isPrivate: isPrivate ?? this.isPrivate,
      maxMembers: maxMembers ?? this.maxMembers,
      createdByUser: createdByUser ?? this.createdByUser,
      members: members ?? this.members,
      messages: messages ?? this.messages,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على عدد الأعضاء
  int get memberCount => members?.length ?? 0;

  /// الحصول على عدد الرسائل
  int get messageCount => messages?.length ?? 0;

  @override
  String toString() {
    return 'ChatGroup(id: $id, name: $name, memberCount: $memberCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatGroup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج عضو المجموعة
class GroupMember {
  final int id;
  final int groupId;
  final int userId;
  final String role; // admin, moderator, member
  final int joinedAt;
  final bool isActive;
  final bool isMuted;
  final int? mutedUntil;

  // Navigation properties
  final ChatGroup? group;
  final User? user;

  const GroupMember({
    required this.id,
    required this.groupId,
    required this.userId,
    this.role = 'member',
    required this.joinedAt,
    this.isActive = true,
    this.isMuted = false,
    this.mutedUntil,
    this.group,
    this.user,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      id: json['id'] as int,
      groupId: json['groupId'] as int,
      userId: json['userId'] as int,
      role: json['role'] as String? ?? 'member',
      joinedAt: json['joinedAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      isMuted: json['isMuted'] as bool? ?? false,
      mutedUntil: json['mutedUntil'] as int?,
      group: json['group'] != null 
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'role': role,
      'joinedAt': joinedAt,
      'isActive': isActive,
      'isMuted': isMuted,
      'mutedUntil': mutedUntil,
    };
  }

  GroupMember copyWith({
    int? id,
    int? groupId,
    int? userId,
    String? role,
    int? joinedAt,
    bool? isActive,
    bool? isMuted,
    int? mutedUntil,
    ChatGroup? group,
    User? user,
  }) {
    return GroupMember(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
      isMuted: isMuted ?? this.isMuted,
      mutedUntil: mutedUntil ?? this.mutedUntil,
      group: group ?? this.group,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الانضمام كـ DateTime
  DateTime get joinedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(joinedAt * 1000);

  /// الحصول على تاريخ انتهاء الكتم كـ DateTime
  DateTime? get mutedUntilDateTime => mutedUntil != null
      ? DateTime.fromMillisecondsSinceEpoch(mutedUntil! * 1000)
      : null;

  /// التحقق من كون العضو مدير
  bool get isAdmin => role == 'admin';

  /// التحقق من كون العضو مشرف
  bool get isModerator => role == 'moderator';

  /// التحقق من كون العضو عادي
  bool get isRegularMember => role == 'member';

  @override
  String toString() {
    return 'GroupMember(id: $id, userId: $userId, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupMember && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مجموعة دردشة
class CreateChatGroupRequest {
  final String name;
  final String? description;
  final bool isPrivate;
  final int? maxMembers;
  final List<int>? memberIds;

  const CreateChatGroupRequest({
    required this.name,
    this.description,
    this.isPrivate = false,
    this.maxMembers,
    this.memberIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'isPrivate': isPrivate,
      'maxMembers': maxMembers,
      'memberIds': memberIds,
    };
  }
}

/// نموذج طلب إضافة عضو للمجموعة
class AddGroupMemberRequest {
  final int userId;
  final String role;

  const AddGroupMemberRequest({
    required this.userId,
    this.role = 'member',
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'role': role,
    };
  }
}
