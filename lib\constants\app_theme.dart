import 'package:flutter/material.dart';  // Includes WidgetState and WidgetStateProperty
import 'package:flutter_application_2/services/storage_service.dart';
import 'package:get/get.dart';
import 'package:http/http.dart';
import 'app_colors.dart';
import 'app_styles.dart';

/// مدير السمات للتطبيق
///
/// يوفر هذا الملف سمات النظام (الفاتح والداكن) ويتيح التبديل بينهما
/// كما يوفر طرقًا مساعدة للحصول على السمة المناسبة للوضع الحالي
class AppTheme {
  // منع إنشاء نسخة من الكلاس
  AppTheme._();

  /// سمة النظام الفاتحة
  static final ThemeData lightTheme = ThemeData(
    // ألوان أساسية
    primaryColor: AppColors.primary,
    primaryColorLight: AppColors.primaryLight,
    primaryColorDark: AppColors.primaryDark,

    // ألوان ثانوية
    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,
      secondary: AppColors.accent,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      surface: AppColors.lightBackground,
      surfaceContainerHighest: AppColors.lightCard,
      onSurface: AppColors.lightTextPrimary,
      error: Colors.red,
      onError: Colors.white,
    ),

    // ألوان الخلفية
    scaffoldBackgroundColor: AppColors.lightBackground,
    cardColor: AppColors.lightCard,

    // ألوان النصوص
    textTheme: TextTheme(
      displayLarge: AppStyles.headingLarge,
      displayMedium: AppStyles.headingMedium,
      displaySmall: AppStyles.headingSmall,
      headlineMedium: AppStyles.titleLarge,
      headlineSmall: AppStyles.titleMedium,
      titleMedium: AppStyles.titleSmall,
      bodyLarge: AppStyles.bodyLarge,
      bodyMedium: AppStyles.bodyMedium,
      bodySmall: AppStyles.bodySmall,
      labelLarge: AppStyles.labelLarge,
      labelMedium: AppStyles.labelMedium,
      labelSmall: AppStyles.labelSmall,
    ),

    // أنماط الأزرار
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: AppStyles.primaryButtonStyle,
    ),
    textButtonTheme: TextButtonThemeData(
      style: AppStyles.textButtonStyle,
    ),

    // أنماط حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.lightBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.lightBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: AppColors.primary, width: 2.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      fillColor: AppColors.lightBackground,
      filled: true,
    ),

    // أنماط AppBar
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
    ),

    // أنماط TabBar
    tabBarTheme: const TabBarTheme(
      labelColor: AppColors.primary,
      unselectedLabelColor: Color(0xFF424242), // تم تحسين لون النص غير المحدد ليكون أكثر وضوحًا
      indicatorColor: AppColors.primary,
    ),

    // أنماط CheckBox
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return AppColors.lightBorder;
      }),
      checkColor: WidgetStateProperty.all(Colors.white),
    ),

    // أنماط Radio
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return AppColors.lightBorder;
      }),
    ),

    // أنماط Switch
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return Colors.white;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary.withValues(alpha: 128);
        }
        return AppColors.lightBorder;
      }),
    ),

    // أنماط Slider
    sliderTheme: SliderThemeData(
      activeTrackColor: AppColors.primary,
      inactiveTrackColor: AppColors.primary.withValues(alpha: 51),  // 0.2 * 255 = 51
      thumbColor: AppColors.primary,
      overlayColor: AppColors.primary.withValues(alpha: 26),  // 0.1 * 255 = 25.5 ≈ 26
      trackHeight: 4.0,
    ),

    // أنماط Divider
    dividerTheme: DividerThemeData(
      color: AppColors.lightDivider,
      thickness: 1.0,
      space: 1.0,
    ),

    // أنماط Card
    cardTheme: CardTheme(
      color: AppColors.lightCard,
      elevation: 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),

    // أنماط Chip
    chipTheme: ChipThemeData(
      backgroundColor: AppColors.lightBackground,
      disabledColor: AppColors.lightBorder,
      selectedColor: AppColors.primary,
      secondarySelectedColor: AppColors.primary,
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
      labelStyle: AppStyles.bodySmall,
      secondaryLabelStyle: AppStyles.bodySmall.copyWith(color: Colors.white),
      brightness: Brightness.light,
    ),

    // أنماط BottomNavigationBar
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.lightCard,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.lightTextSecondary,
    ),

    // أنماط FloatingActionButton
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
    ),

    // أنماط SnackBar
    snackBarTheme: SnackBarThemeData(
      backgroundColor: AppColors.lightCard,
      contentTextStyle: AppStyles.bodyMedium,
      actionTextColor: AppColors.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      behavior: SnackBarBehavior.floating,
    ),

    // أنماط Dialog
    dialogTheme: DialogTheme(
      backgroundColor: AppColors.lightDialog,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      titleTextStyle: AppStyles.titleLarge,
      contentTextStyle: AppStyles.bodyMedium,
    ),

    // أنماط BottomSheet
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: AppColors.lightDialog,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
    ),

    // أنماط PopupMenu
    popupMenuTheme: PopupMenuThemeData(
      color: AppColors.lightCard,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),

    // أنماط Tooltip
    tooltipTheme: TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColors.lightTextPrimary.withValues(alpha: 230),  // 0.9 * 255 = 229.5 ≈ 230
        borderRadius: BorderRadius.circular(4.0),
      ),
      textStyle: AppStyles.bodySmall.copyWith(color: Colors.white),
    ),

    // وضع السمة
    brightness: Brightness.light,
  );

  /// سمة النظام الداكنة
  static final ThemeData darkTheme = ThemeData(
    // ألوان أساسية
    primaryColor: AppColors.primary,
    primaryColorLight: AppColors.primaryLight,
    primaryColorDark: AppColors.primaryDark,

    // ألوان ثانوية
    colorScheme: const ColorScheme.dark(
      primary: AppColors.primary,
      secondary: AppColors.accent,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      surface: AppColors.darkBackground,
      surfaceContainerHighest: AppColors.darkCard,
      onSurface: AppColors.darkTextPrimary,
      error: Colors.red,
      onError: Colors.white,
    ),

    // ألوان الخلفية
    scaffoldBackgroundColor: AppColors.darkBackground,
    cardColor: AppColors.darkCard,

    // ألوان النصوص
    textTheme: TextTheme(
      displayLarge: AppStyles.headingLarge,
      displayMedium: AppStyles.headingMedium,
      displaySmall: AppStyles.headingSmall,
      headlineMedium: AppStyles.titleLarge,
      headlineSmall: AppStyles.titleMedium,
      titleMedium: AppStyles.titleSmall,
      bodyLarge: AppStyles.bodyLarge,
      bodyMedium: AppStyles.bodyMedium,
      bodySmall: AppStyles.bodySmall,
      labelLarge: AppStyles.labelLarge,
      labelMedium: AppStyles.labelMedium,
      labelSmall: AppStyles.labelSmall,
    ),

    // أنماط الأزرار
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: AppStyles.primaryButtonStyle,
    ),
    textButtonTheme: TextButtonThemeData(
      style: AppStyles.textButtonStyle,
    ),

    // أنماط حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.darkBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.darkBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: AppColors.primary, width: 2.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.red, width: 2.0),
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      fillColor: AppColors.darkBackground,
      filled: true,
    ),

    // أنماط AppBar
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1A1A1A),
      foregroundColor: Colors.white,
      elevation: 0,
    ),

    // أنماط TabBar
    tabBarTheme: const TabBarTheme(
      labelColor: AppColors.primary,
      unselectedLabelColor: Color(0xFFDDDDDD), // تم تحسين لون النص غير المحدد ليكون أكثر وضوحًا
      indicatorColor: AppColors.primary,
    ),

    // أنماط CheckBox
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return AppColors.darkBorder;
      }),
      checkColor: WidgetStateProperty.all(Colors.white),
    ),

    // أنماط Radio
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return AppColors.darkBorder;
      }),
    ),

    // أنماط Switch
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return Colors.white;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary.withValues(alpha: 128);
        }
        return AppColors.darkBorder;
      }),
    ),

    // أنماط Slider
    sliderTheme: SliderThemeData(
      activeTrackColor: AppColors.primary,
      inactiveTrackColor: AppColors.primary.withValues(alpha: 51),  // 0.2 * 255 = 51
      thumbColor: AppColors.primary,
      overlayColor: AppColors.primary.withValues(alpha: 26),  // 0.1 * 255 = 25.5 ≈ 26
      trackHeight: 4.0,
    ),

    // أنماط Divider
    dividerTheme: DividerThemeData(
      color: AppColors.darkDivider,
      thickness: 1.0,
      space: 1.0,
    ),

    // أنماط Card
    cardTheme: CardTheme(
      color: AppColors.darkCard,
      elevation: 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),

    // أنماط Chip
    chipTheme: ChipThemeData(
      backgroundColor: AppColors.darkBackground,
      disabledColor: AppColors.darkBorder,
      selectedColor: AppColors.primary,
      secondarySelectedColor: AppColors.primary,
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
      labelStyle: AppStyles.bodySmall,
      secondaryLabelStyle: AppStyles.bodySmall.copyWith(color: Colors.white),
      brightness: Brightness.dark,
    ),

    // أنماط BottomNavigationBar
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.darkCard,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.darkTextSecondary,
    ),

    // أنماط FloatingActionButton
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
    ),

    // أنماط SnackBar
    snackBarTheme: SnackBarThemeData(
      backgroundColor: AppColors.darkCard,
      contentTextStyle: AppStyles.bodyMedium,
      actionTextColor: AppColors.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      behavior: SnackBarBehavior.floating,
    ),

    // أنماط Dialog
    dialogTheme: DialogTheme(
      backgroundColor: AppColors.darkDialog,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      titleTextStyle: AppStyles.titleLarge,
      contentTextStyle: AppStyles.bodyMedium,
    ),

    // أنماط BottomSheet
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: AppColors.darkDialog,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
    ),

    // أنماط PopupMenu
    popupMenuTheme: PopupMenuThemeData(
      color: AppColors.darkCard,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),

    // أنماط Tooltip
    tooltipTheme: TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColors.darkTextPrimary.withAlpha(230), // تم تغيير withOpacity(0.9) إلى withAlpha(230)
        borderRadius: BorderRadius.circular(4.0),
      ),
      textStyle: AppStyles.bodySmall.copyWith(color: Colors.black),
    ),

    // وضع السمة
    brightness: Brightness.dark,
  );

  /// الحصول على السمة المناسبة للوضع الحالي
  static ThemeData get theme => Get.isDarkMode ? darkTheme : lightTheme;

  /// تبديل وضع السمة
  static void toggleTheme() {
    Get.changeThemeMode(Get.isDarkMode ? ThemeMode.light : ThemeMode.dark);
  }

  /// الحصول على وضع السمة الحالي
  static ThemeMode get themeMode => Get.isDarkMode ? ThemeMode.dark : ThemeMode.light;

  /// حفظ وضع السمة
  static Future<void> saveThemeMode(ThemeMode mode) async {
    try {
      final storage = Get.find<StorageService>();

      // حفظ وضع السمة
      await storage.setString('theme_mode', mode.toString());

      // التحقق من نجاح الحفظ
      final savedMode = await storage.getString('theme_mode');
      if (savedMode == mode.toString()) {
        debugPrint('تم حفظ وضع السمة بنجاح: ${mode.toString()}');
      } else {
        debugPrint('تحذير: قيمة السمة المحفوظة ($savedMode) لا تطابق القيمة المطلوبة (${mode.toString()})');
      }

      // حفظ قيمة isDarkMode أيضًا كاحتياط إضافي
      final isDark = mode == ThemeMode.dark;
      await storage.setBool('is_dark_mode', isDark);
      debugPrint('تم حفظ حالة الوضع الداكن: $isDark');

    } catch (e) {
      debugPrint('خطأ في حفظ وضع السمة: $e');
      // محاولة الحفظ بطريقة بديلة
      try {
        final storage = Get.find<StorageService>();
        final isDark = mode == ThemeMode.dark;
        await storage.setBool('is_dark_mode_backup', isDark);
        debugPrint('تم حفظ حالة الوضع الداكن بطريقة بديلة: $isDark');
      } catch (backupError) {
        debugPrint('فشل الحفظ البديل أيضًا: $backupError');
      }
    }
  }

  /// تحميل وضع السمة المحفوظ
  static Future<ThemeMode> loadThemeMode() async {
    try {
      final storage = Get.find<StorageService>();
      final String? themeMode = await storage.getString('theme_mode');
      debugPrint('وضع السمة المحفوظ: $themeMode');

      if (themeMode == 'ThemeMode.dark') {
        debugPrint('تم تحميل الوضع الداكن من التخزين');
        return ThemeMode.dark;
      } else if (themeMode == 'ThemeMode.light') {
        debugPrint('تم تحميل الوضع الفاتح من التخزين');
        return ThemeMode.light;
      } else if (themeMode == 'ThemeMode.system') {
        debugPrint('تم تحميل وضع النظام من التخزين');
        return ThemeMode.system;
      }

      // التحقق من القيمة الاحتياطية إذا لم يتم العثور على وضع السمة
      final bool? isDarkMode = await storage.getBool('is_dark_mode');
      if (isDarkMode != null) {
        debugPrint('استخدام القيمة الاحتياطية للوضع الداكن: $isDarkMode');
        return isDarkMode ? ThemeMode.dark : ThemeMode.light;
      }

      // التحقق من القيمة الاحتياطية الثانية
      final bool? isDarkModeBackup = await storage.getBool('is_dark_mode_backup');
      if (isDarkModeBackup != null) {
        debugPrint('استخدام القيمة الاحتياطية الثانية للوضع الداكن: $isDarkModeBackup');
        return isDarkModeBackup ? ThemeMode.dark : ThemeMode.light;
      }

    } catch (e) {
      debugPrint('خطأ في تحميل وضع السمة: $e');
    }

    // استخدام وضع النظام كقيمة افتراضية
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    final defaultMode = brightness == Brightness.dark ? ThemeMode.dark : ThemeMode.light;
    debugPrint('استخدام وضع النظام كقيمة افتراضية: ${brightness == Brightness.dark ? "داكن" : "فاتح"}');

    // حفظ القيمة الافتراضية
    try {
      saveThemeMode(defaultMode);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة الافتراضية: $e');
    }

    return defaultMode;
  }
}
