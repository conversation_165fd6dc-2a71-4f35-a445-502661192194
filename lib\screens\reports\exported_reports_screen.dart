import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';

'../../controllers/report_controller.dart';
import '../../services/reporting/export/file_handler_service.dart';
import '../../utils/app_styles.dart';

/// شاشة عرض التقارير المصدرة
class ExportedReportsScreen extends StatefulWidget {
  const ExportedReportsScreen({Key? key}) : super(key: key);

  @override
  State<ExportedReportsScreen> createState() => _ExportedReportsScreenState();
}

class _ExportedReportsScreenState extends State<ExportedReportsScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  final FileHandlerService _fileHandlerService = FileHandlerService();

  List<ExportedReportInfo> _exportedReports = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadExportedReports();
  }

  /// تحميل التقارير المصدرة
  Future<void> _loadExportedReports() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final reports = await _reportController.getExportedReports();
      setState(() {
        _exportedReports = reports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل التقارير المصدرة: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المصدرة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadExportedReports,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// بناء جسم الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadExportedReports,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_exportedReports.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.info_outline,
              color: Colors.blue,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد تقارير مصدرة',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Get.back(),
              child: const Text('العودة إلى التقارير'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _exportedReports.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final report = _exportedReports[index];
        return _buildReportCard(report);
      },
    );
  }

  /// بناء بطاقة التقرير
  Widget _buildReportCard(ExportedReportInfo report) {
    final file = File(report.filePath);
    final exists = file.existsSync();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildFormatIcon(report.format),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        report.title,
                        style: AppStyles.titleMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'تم التصدير: ${_formatDate(report.exportedAt)}',
                        style: AppStyles.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حجم الملف: ${_formatFileSize(report.fileSize)}',
                        style: AppStyles.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'المسار: ${report.filePath}',
                        style: AppStyles.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (exists) ...[
                  IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'فتح الملف',
                    onPressed: () => _openFile(report.filePath),
                  ),
                  IconButton(
                    icon: const Icon(Icons.share),
                    tooltip: 'مشاركة',
                    onPressed: () => _shareFile(report.filePath),
                  ),
                ] else
                  const Chip(
                    label: Text('الملف غير موجود'),
                    backgroundColor: Colors.red,
                    labelStyle: TextStyle(color: Colors.white),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أيقونة التنسيق
  Widget _buildFormatIcon(ReportFormat format) {
    IconData icon;
    Color color;

    switch (format) {
      case ReportFormat.pdf:
        icon = Icons.picture_as_pdf;
        color = Colors.red;
        break;
      case ReportFormat.excel:
        icon = Icons.table_chart;
        color = Colors.green;
        break;
      case ReportFormat.csv:
        icon = Icons.format_list_bulleted;
        color = Colors.blue;
        break;
      case ReportFormat.json:
        icon = Icons.code;
        color = Colors.purple;
        break;
      case ReportFormat.image:
        icon = Icons.image;
        color = Colors.orange;
        break;
      default:
        icon = Icons.description;
        color = Colors.grey;
    }

    return CircleAvatar(
      backgroundColor: color.withValues(alpha: 51), // 0.2 * 255 = 51
      radius: 24,
      child: Icon(
        icon,
        color: color,
        size: 24,
      ),
    );
  }

  /// فتح الملف
  Future<void> _openFile(String filePath) async {
    try {
      final result = await OpenFile.open(filePath);
      if (result.type != ResultType.done) {
        _showErrorSnackbar('فشل في فتح الملف: ${result.message}');
      }
    } catch (e) {
      _showErrorSnackbar('حدث خطأ أثناء فتح الملف: ${e.toString()}');
    }
  }

  /// مشاركة الملف
  Future<void> _shareFile(String filePath) async {
    try {
      await _fileHandlerService.shareFile(filePath);
    } catch (e) {
      _showErrorSnackbar('حدث خطأ أثناء مشاركة الملف: ${e.toString()}');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int sizeInBytes) {
    if (sizeInBytes < 1024) {
      return '$sizeInBytes بايت';
    } else if (sizeInBytes < 1024 * 1024) {
      final sizeInKB = (sizeInBytes / 1024).toStringAsFixed(1);
      return '$sizeInKB كيلوبايت';
    } else {
      final sizeInMB = (sizeInBytes / (1024 * 1024)).toStringAsFixed(1);
      return '$sizeInMB ميجابايت';
    }
  }
}
