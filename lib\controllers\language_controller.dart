import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/storage_service.dart';

/// متحكم اللغة
class LanguageController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();

  // اللغة الحالية
  final Rx<Locale> _currentLocale = const Locale('ar', '').obs;

  // اللغات المدعومة
  static const List<Locale> supportedLocales = [
    Locale('ar', ''), // العربية
    Locale('en', 'US'), // الإنجليزية
  ];

  // أسماء اللغات
  static const Map<String, String> languageNames = {
    'ar': 'العربية',
    'en': 'English',
  };

  // Getters
  Locale get currentLocale => _currentLocale.value;
  String get currentLanguageCode => _currentLocale.value.languageCode;
  String get currentLanguageName => languageNames[currentLanguageCode] ?? 'العربية';
  bool get isArabic => currentLanguageCode == 'ar';
  bool get isEnglish => currentLanguageCode == 'en';

  @override
  void onInit() {
    super.onInit();
    _loadLanguageSettings();
  }

  /// تحميل إعدادات اللغة من التخزين المحلي
  Future<void> _loadLanguageSettings() async {
    try {
      final languageCode = await _storageService.getString('language_code') ?? 'ar';
      final countryCode = await _storageService.getString('country_code') ?? '';
      
      final locale = Locale(languageCode, countryCode);
      
      // التحقق من دعم اللغة
      if (supportedLocales.any((l) => l.languageCode == languageCode)) {
        _currentLocale.value = locale;
        Get.updateLocale(locale);
        debugPrint('تم تحميل اللغة: $languageCode');
      } else {
        // استخدام العربية كلغة افتراضية
        _currentLocale.value = const Locale('ar', '');
        Get.updateLocale(const Locale('ar', ''));
        debugPrint('لغة غير مدعومة، تم استخدام العربية كافتراضية');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات اللغة: $e');
      // استخدام العربية كلغة افتراضية في حالة الخطأ
      _currentLocale.value = const Locale('ar', '');
      Get.updateLocale(const Locale('ar', ''));
    }
  }

  /// تغيير اللغة
  Future<void> changeLanguage(String languageCode) async {
    try {
      // البحث عن اللغة في القائمة المدعومة
      final locale = supportedLocales.firstWhere(
        (l) => l.languageCode == languageCode,
        orElse: () => const Locale('ar', ''),
      );

      _currentLocale.value = locale;
      
      // حفظ اللغة في التخزين المحلي
      await _storageService.setString('language_code', locale.languageCode);
      await _storageService.setString('country_code', locale.countryCode ?? '');
      
      // تطبيق اللغة الجديدة
      Get.updateLocale(locale);
      
      debugPrint('تم تغيير اللغة إلى: ${languageNames[languageCode]}');
      
      // إظهار رسالة تأكيد
      Get.snackbar(
        isArabic ? 'تم تغيير اللغة' : 'Language Changed',
        isArabic 
            ? 'تم تغيير اللغة إلى ${languageNames[languageCode]}'
            : 'Language changed to ${languageNames[languageCode]}',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('خطأ في تغيير اللغة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تغيير اللغة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تغيير إلى العربية
  Future<void> changeToArabic() async {
    await changeLanguage('ar');
  }

  /// تغيير إلى الإنجليزية
  Future<void> changeToEnglish() async {
    await changeLanguage('en');
  }

  /// تبديل اللغة بين العربية والإنجليزية
  Future<void> toggleLanguage() async {
    if (isArabic) {
      await changeToEnglish();
    } else {
      await changeToArabic();
    }
  }

  /// الحصول على اتجاه النص للغة الحالية
  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }

  /// الحصول على محاذاة النص للغة الحالية
  TextAlign get textAlign {
    return isArabic ? TextAlign.right : TextAlign.left;
  }

  /// الحصول على محاذاة النص المعاكسة للغة الحالية
  TextAlign get oppositeTextAlign {
    return isArabic ? TextAlign.left : TextAlign.right;
  }

  /// الحصول على قائمة اللغات المدعومة مع أسمائها
  List<Map<String, String>> get supportedLanguages {
    return supportedLocales.map((locale) => {
      'code': locale.languageCode,
      'name': languageNames[locale.languageCode] ?? locale.languageCode,
      'locale': '${locale.languageCode}_${locale.countryCode}',
    }).toList();
  }

  /// التحقق من دعم لغة معينة
  bool isLanguageSupported(String languageCode) {
    return supportedLocales.any((l) => l.languageCode == languageCode);
  }

  /// إعادة تعيين اللغة إلى الافتراضية (العربية)
  Future<void> resetToDefault() async {
    await changeLanguage('ar');
  }

  /// الحصول على النص المترجم
  String tr(String key) {
    return key.tr;
  }

  /// الحصول على النص المترجم مع معاملات
  String trParams(String key, Map<String, String> params) {
    return key.trParams(params);
  }

  /// الحصول على النص المترجم مع معاملات متعددة
  String trPlural(String key, int count) {
    return key.trPlural(count);
  }
}
