    -- Migration Script: Update Archive Models
-- تحديث نماذج الأرشيف لإضافة الخصائص المفقودة

-- 1. تحديث جدول archive_categories
-- إضافة الأعمدة الجديدة
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'color')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    ADD [color] NVARCHAR(20) NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'icon')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    ADD [icon] NVARCHAR(50) NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_categories] 
    ADD [is_active] BIT NOT NULL DEFAULT 1;
END

-- 2. تحديث جدول archive_documents
-- إضافة الأعمدة الجديدة
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'content')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    ADD [content] NTEXT NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_by')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    ADD [created_by] INT NOT NULL DEFAULT 1;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_at')
BEGIN
    ALTER TABLE [dbo].[archive_documents] 
    ADD [created_at] BIGINT NOT NULL DEFAULT 0;
END

-- 3. تحديث جدول archive_tags
-- إضافة الأعمدة الجديدة
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'description')
BEGIN
    ALTER TABLE [dbo].[archive_tags] 
    ADD [description] NTEXT NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_tags] 
    ADD [is_active] BIT NOT NULL DEFAULT 1;
END

-- 4. إنشاء جدول archive_document_tags (إذا لم يكن موجوداً)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[archive_document_tags]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[archive_document_tags] (
        [document_id] INT NOT NULL,
        [tag_id] INT NOT NULL,
        CONSTRAINT [PK_archive_document_tags] PRIMARY KEY ([document_id], [tag_id]),
        CONSTRAINT [FK_archive_document_tags_document] FOREIGN KEY ([document_id]) 
            REFERENCES [dbo].[archive_documents] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_archive_document_tags_tag] FOREIGN KEY ([tag_id]) 
            REFERENCES [dbo].[archive_tags] ([id]) ON DELETE CASCADE
    );
END

-- 5. إضافة Foreign Key للعمود created_by في archive_documents (إذا لم يكن موجوداً)
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_archive_documents_created_by_users')
BEGIN
    ALTER TABLE [dbo].[archive_documents]
    ADD CONSTRAINT [FK_archive_documents_created_by_users] 
    FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]);
END

-- 6. تحديث البيانات الموجودة
-- تعيين قيم افتراضية للسجلات الموجودة
UPDATE [dbo].[archive_categories] 
SET [is_active] = 1 
WHERE [is_active] IS NULL;

UPDATE [dbo].[archive_tags] 
SET [is_active] = 1 
WHERE [is_active] IS NULL;

-- تعيين قيم created_by و created_at للوثائق الموجودة
UPDATE [dbo].[archive_documents] 
SET [created_by] = [uploaded_by], 
    [created_at] = [uploaded_at] 
WHERE [created_by] = 0 OR [created_at] = 0;

PRINT 'Migration completed successfully: Archive models updated';
