import 'task_models.dart';
import 'user_model.dart';

/// نموذج متتبع تقدم المهمة - متطابق مع ASP.NET Core API
class TaskProgressTracker {
  final int id;
  final int taskId;
  final int progress;
  final int updatedAt;
  final int updatedBy;
  final double progressPercentage;
  final String? notes;

  // Navigation properties
  final Task? task;
  final User? updatedByNavigation;

  const TaskProgressTracker({
    required this.id,
    required this.taskId,
    required this.progress,
    required this.updatedAt,
    required this.updatedBy,
    required this.progressPercentage,
    this.notes,
    this.task,
    this.updatedByNavigation,
  });

  factory TaskProgressTracker.fromJson(Map<String, dynamic> json) {
    return TaskProgressTracker(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      progress: json['progress'] as int,
      updatedAt: json['updatedAt'] as int,
      updatedBy: json['updatedBy'] as int,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      notes: json['notes'] as String?,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      updatedByNavigation: json['updatedByNavigation'] != null
          ? User.fromJson(json['updatedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'progress': progress,
      'updatedAt': updatedAt,
      'updatedBy': updatedBy,
      'progressPercentage': progressPercentage,
      'notes': notes,
    };
  }

  TaskProgressTracker copyWith({
    int? id,
    int? taskId,
    int? progress,
    int? updatedAt,
    int? updatedBy,
    double? progressPercentage,
    String? notes,
    Task? task,
    User? updatedByNavigation,
  }) {
    return TaskProgressTracker(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      progress: progress ?? this.progress,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      notes: notes ?? this.notes,
      task: task ?? this.task,
      updatedByNavigation: updatedByNavigation ?? this.updatedByNavigation,
    );
  }

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime get updatedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(updatedAt * 1000);

  /// الحصول على نسبة التقدم كنص
  String get progressText => '${progressPercentage.toStringAsFixed(1)}%';

  /// التحقق من اكتمال المهمة
  bool get isCompleted => progressPercentage >= 100.0;

  @override
  String toString() {
    return 'TaskProgressTracker(id: $id, progress: $progressText, updatedAt: $updatedAtDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskProgressTracker && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب تحديث تقدم المهمة
class UpdateTaskProgressRequest {
  final int progress;
  final double progressPercentage;
  final String? notes;

  const UpdateTaskProgressRequest({
    required this.progress,
    required this.progressPercentage,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'progress': progress,
      'progressPercentage': progressPercentage,
      'notes': notes,
    };
  }
}
