import 'user_model.dart';
import 'task_models.dart';

/// نموذج تعليق المهمة - متطابق مع ASP.NET Core API
class TaskComment {
  final int id;
  final int taskId;
  final int userId;
  final String content;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final Task? task;
  final User? user;

  const TaskComment({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.task,
    this.user,
  });

  factory TaskComment.fromJson(Map<String, dynamic> json) {
    return TaskComment(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      content: json['content'] as String,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'content': content,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }

  TaskComment copyWith({
    int? id,
    int? taskId,
    int? userId,
    String? content,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    Task? task,
    User? user,
  }) {
    return TaskComment(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      task: task ?? this.task,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'TaskComment(id: $id, taskId: $taskId, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// تم حذف TaskHistory لأنه غير موجود في ASP.NET Core API

/// نموذج طلب إضافة تعليق - متطابق مع API
class AddTaskCommentRequest {
  final int taskId;
  final String content;

  const AddTaskCommentRequest({
    required this.taskId,
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'content': content,
    };
  }
}

/// نموذج طلب تحديث تعليق
class UpdateTaskCommentRequest {
  final String content;

  const UpdateTaskCommentRequest({
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
    };
  }
}
